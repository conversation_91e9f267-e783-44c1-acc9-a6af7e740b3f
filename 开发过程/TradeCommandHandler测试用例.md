# TradeCommandHandler测试用例

## 测试目标
验证TradeCommandHandler的各个命令处理功能是否正确实现，确保与TradeManager的集成正常。

## 测试环境准备

### 数据准备
1. **角色数据**: 创建测试角色，设置初始银两和背包物品
2. **NPC数据**: 配置测试NPC商店
3. **物品数据**: 准备测试物品和装备
4. **市场数据**: 准备一些测试交易

### 配置检查
- 确认command.yml中交易命令配置正确
- 确认TradeManager已正确注入
- 确认数据库连接正常

## 测试用例

### 1. 购买命令测试 (handleBuy)

#### 测试用例1.1: NPC购买 - 正常流程
```
输入命令: 买 回血丹 5
预期结果: 🎉 交易成功！获得 回血丹 x5，花费 250 银两
          💰 掌柜笑道：多谢大侠惠顾！
验证点:
- 角色银两减少250
- 背包增加5个回血丹
- 返回成功消息
```

#### 测试用例1.2: NPC购买 - 银两不足
```
输入命令: 买 神兵利器 1
预期结果: 💸 囊中羞涩！还需 5000 银两 才能购得此宝！
验证点:
- 角色银两不变
- 背包不变
- 返回错误提示
```

#### 测试用例1.3: 玩家市场购买 - 正常流程
```
输入命令: 买 TRADE_1234567890_123 1
预期结果: 🎉 交易成功！获得 青锋剑 x1
          💰 花费 1000 银两
          🎒 请查看行囊！
验证点:
- 角色银两减少1000
- 背包增加青锋剑
- 交易会话状态更新
```

#### 测试用例1.4: 参数错误
```
输入命令: 买 回血丹
预期结果: 🛒 购买格式：买 物品名/交易ID 数量
验证点:
- 返回格式提示
- 数据不变
```

#### 测试用例1.5: 数量格式错误
```
输入命令: 买 回血丹 abc
预期结果: 🔢 数量必须是数字！
验证点:
- 返回数字格式错误
- 数据不变
```

### 2. 出售命令测试 (handleSell)

#### 测试用例2.1: NPC出售 - 正常流程
```
输入命令: 卖 破剑 1
预期结果: 💰 出售成功！破剑 x1 换得 10 银两
          😊 掌柜满意地点点头！
验证点:
- 角色银两增加10
- 背包减少1个破剑
- 返回成功消息
```

#### 测试用例2.2: 市场上架 - 正常流程
```
输入命令: 卖 青锋剑 1 1000 silver
预期结果: 🎺 听好了！听好了！新宝贝上架啦！
          🏪 有缘的大侠快来交易市场瞧瞧！
          ...
验证点:
- 背包减少1个青锋剑
- 创建新的交易会话
- 返回上架成功消息
```

#### 测试用例2.3: 物品不存在
```
输入命令: 卖 不存在的物品 1
预期结果: 🔍 行囊中没有 不存在的物品 这等物品！
验证点:
- 数据不变
- 返回物品不存在提示
```

### 3. 询问NPC测试 (handleAskNpc)

#### 测试用例3.1: 查看NPC商店 - 正常流程
```
输入命令: 询问 NPC_SHOP_01
预期结果: 🏪 杂货铺
          📝 出售各种日常用品
          🛍️ 珍宝清单：
          1. 回血丹 - 50银两 📦(存货:10)
          ...
验证点:
- 返回商店信息
- 显示商品列表
- 显示购买提示
```

#### 测试用例3.2: NPC不存在
```
输入命令: 询问 不存在的NPC
预期结果: 🤷‍♂️ 这位大侠似乎不是商人，没有开设店铺哦！
验证点:
- 返回NPC不存在提示
```

#### 测试用例3.3: 参数缺失
```
输入命令: 询问
预期结果: 💬 询问格式：询问 NPC编号
验证点:
- 返回格式提示
```

### 4. 取消出售测试 (handleCancelSell)

#### 测试用例4.1: 取消自己的交易 - 正常流程
```
输入命令: 取消 TRADE_1234567890_123
预期结果: ✅ 宝物已下架，物归原主！
验证点:
- 交易会话被删除
- 物品返回背包
- 返回成功消息
```

#### 测试用例4.2: 取消他人的交易
```
输入命令: 取消 TRADE_1234567890_456
预期结果: 🚫 此宝物非你所售，无法取消！
验证点:
- 交易会话不变
- 返回权限错误
```

#### 测试用例4.3: 交易不存在
```
输入命令: 取消 TRADE_不存在
预期结果: 🤷‍♂️ 此交易已不存在！
验证点:
- 返回交易不存在提示
```

### 5. 查看市场测试 (handleViewMarket)

#### 测试用例5.1: 查看市场列表 - 有交易
```
输入命令: 市场
预期结果: 🏪 ═══ 西湖交易市场 ═══
          
          🆔 编号：TRADE_1234567890_123
          🎁 宝贝：青锋剑
          📦 数量：1 件
          💰 价格：1000 银两
          🌊 ～～～～～～～～～～～～～～～～
          ...
验证点:
- 显示所有交易
- 格式正确
- 信息完整
```

#### 测试用例5.2: 查看市场列表 - 无交易
```
输入命令: 市场
预期结果: 🏪 ═══ 西湖交易市场 ═══
          
          😔 市场空空如也，暂无宝物出售！
验证点:
- 显示空市场提示
```

#### 测试用例5.3: 查看交易详情 - 正常流程
```
输入命令: 市场 TRADE_1234567890_123
预期结果: 🏷️ ═══ 宝物详情 ═══
          🆔 编号：TRADE_1234567890_123
          🎁 名称：青锋剑
          ✨ 品质：3品
          ⚔️ 基础属性：物攻+10
          🌟 附加属性：物攻+5，暴击+2
          ...
验证点:
- 显示详细信息
- 包含装备属性
- 显示购买指令
```

## 边界条件测试

### 1. 参数验证测试
- 空参数
- 参数过多
- 参数类型错误
- 特殊字符参数

### 2. 状态验证测试
- 角色死亡状态
- 角色不存在
- 背包已满
- 银两不足

### 3. 并发测试
- 同时购买同一物品
- 同时上架同一物品
- 同时取消同一交易

## 集成测试

### 1. 与TradeManager集成
- 验证所有TradeManager方法调用正确
- 验证参数传递正确
- 验证返回值处理正确

### 2. 与命令系统集成
- 验证命令解析正确
- 验证命令分发正确
- 验证权限检查正确

### 3. 与数据库集成
- 验证数据持久化
- 验证事务处理
- 验证并发安全

## 性能测试

### 1. 响应时间测试
- 单个命令处理时间 < 100ms
- 批量操作处理时间合理
- 数据库查询优化

### 2. 并发测试
- 100个并发用户同时交易
- 内存使用稳定
- 无死锁和竞态条件

### 3. 压力测试
- 长时间运行稳定性
- 大量数据处理能力
- 异常恢复能力

## 错误处理测试

### 1. 业务异常
- GameException正确抛出
- 异常消息正确返回
- 数据状态正确回滚

### 2. 系统异常
- 数据库连接异常
- 网络异常
- 内存不足异常

### 3. 用户输入异常
- 恶意输入
- 超长输入
- 非法字符输入

## 验收标准

### 功能完整性
- ✅ 5个命令全部实现
- ✅ 所有参数格式支持
- ✅ 所有错误情况处理

### 用户体验
- ✅ 错误提示友好
- ✅ 成功提示有趣
- ✅ 操作流程顺畅

### 代码质量
- ✅ 代码结构清晰
- ✅ 异常处理完善
- ✅ 日志记录详细

### 性能要求
- ✅ 响应时间 < 100ms
- ✅ 并发处理正常
- ✅ 内存使用合理

## 回归测试

确保TradeCommandHandler的实现不影响其他功能：
1. 其他CommandHandler正常工作
2. TradeManager其他方法正常工作
3. 数据库操作正常
4. 缓存系统正常
