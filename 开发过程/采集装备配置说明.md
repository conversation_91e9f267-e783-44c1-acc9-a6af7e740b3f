# 采集装备配置说明

## 配置概述

为collect.yml中的所有采集类型添加了1-29级三职业装备的采集获取概率配置，让玩家在进行各种采集活动时有机会获得装备奖励。

## 配置的装备类型

### 1. 剑客装备 - 游湖套装 (EQ_YOUHU_*)
- **适用职业**: 剑客 (role_limit=1)
- **等级范围**: 1-29级
- **套装名称**: 游湖套装

| 装备部位 | 物品编号 | 物品名称 | 采集概率 |
|---------|----------|----------|----------|
| 武器 | EQ_YOUHU_WQ_01 | 青锋剑 | 0.05 |
| 头部 | EQ_YOUHU_TG_01 | 游湖冠 | 0.08 |
| 衣服 | EQ_YOUHU_YF_01 | 游湖袍 | 0.03 |
| 护手 | EQ_YOUHU_HS_01 | 游湖护腕 | 0.05 |
| 护膝 | EQ_YOUHU_HX_01 | 游湖护膝 | 0.05 |
| 裤子 | EQ_YOUHU_KZ_01 | 游湖长裤 | 0.03 |
| 鞋子 | EQ_YOUHU_XZ_01 | 游湖靴 | 0.03 |

### 2. 仙师装备 - 湖光套装 (EQ_HUGUANG_*)
- **适用职业**: 仙师 (role_limit=2)
- **等级范围**: 1-29级
- **套装名称**: 湖光套装

| 装备部位 | 物品编号 | 物品名称 | 采集概率 |
|---------|----------|----------|----------|
| 武器 | EQ_HUGUANG_WQ_01 | 湖光杖 | 0.05 |
| 头部 | EQ_HUGUANG_TG_01 | 湖光冠 | 0.08 |
| 衣服 | EQ_HUGUANG_YF_01 | 湖光法袍 | 0.03 |
| 护手 | EQ_HUGUANG_HS_01 | 湖光护腕 | 0.05 |
| 护膝 | EQ_HUGUANG_HX_01 | 湖光护膝 | 0.05 |
| 裤子 | EQ_HUGUANG_KZ_01 | 湖光长裤 | 0.03 |
| 鞋子 | EQ_HUGUANG_XZ_01 | 湖光靴 | 0.03 |

### 3. 圣僧装备 - 印月套装 (EQ_YINYUE_*)
- **适用职业**: 圣僧 (role_limit=3)
- **等级范围**: 1-29级
- **套装名称**: 印月套装

| 装备部位 | 物品编号 | 物品名称 | 采集概率 |
|---------|----------|----------|----------|
| 武器 | EQ_YINYUE_WQ_01 | 印月禅杖 | 0.05 |
| 头部 | EQ_YINYUE_TG_01 | 印月冠 | 0.08 |
| 衣服 | EQ_YINYUE_YF_01 | 印月袈裟 | 0.03 |
| 护手 | EQ_YINYUE_HS_01 | 印月护腕 | 0.05 |
| 护膝 | EQ_YINYUE_HX_01 | 印月护膝 | 0.05 |
| 裤子 | EQ_YINYUE_KZ_01 | 印月长裤 | 0.03 |
| 鞋子 | EQ_YINYUE_XZ_01 | 印月靴 | 0.03 |

### 4. 通用装备 - 玉佩
- **适用职业**: 全职业通用
- **等级范围**: 1-29级

| 装备部位 | 物品编号 | 物品名称 | 采集概率 |
|---------|----------|----------|----------|
| 玉佩 | EQ_YUPEI_01 | 明珠玉佩 | 0.01 |

## 概率设计说明

### 概率分配原则
根据装备的重要性和稀有度设计不同的采集概率：

1. **头部装备 (0.08)**: 最高概率，因为头部装备相对容易获得
2. **武器/护手/护膝 (0.05)**: 中等概率，重要但不过于稀有
3. **衣服/裤子/鞋子 (0.03)**: 较低概率，增加获取难度
4. **玉佩 (0.01)**: 最低概率，作为稀有装备

### 概率平衡考虑
- **总装备概率**: 每个职业套装总概率为 0.32 (8件装备)
- **三职业总概率**: 0.32 × 3 + 0.01 = 0.97
- **平衡性**: 确保不会过度影响原有采集物品的获取

## 涉及的采集类型

### 1. 钓鱼 (xihu_lakeside)
- **位置**: 西湖湖心亭
- **原有物品**: 各种鱼类、药品、材料
- **新增**: 全套1-29级三职业装备 + 玉佩

### 2. 钓鱼 (xihu_duanqiao)  
- **位置**: 断桥
- **原有物品**: 各种鱼类、药品、材料
- **新增**: 全套1-29级三职业装备 + 玉佩

### 3. 采桑 (xihu_lakeside)
- **位置**: 西湖湖心亭
- **原有物品**: 桑叶、药品
- **新增**: 全套1-29级三职业装备 + 玉佩

### 4. 采茶 (xihu_lakeside)
- **位置**: 西湖湖心亭  
- **原有物品**: 茶叶、药品
- **新增**: 全套1-29级三职业装备 + 玉佩

### 5. 挖矿 (xihu_lakeside)
- **位置**: 西湖湖心亭
- **原有物品**: 各种矿石、药品
- **新增**: 全套1-29级三职业装备 + 玉佩

## 配置示例

```yaml
# 钓鱼配置示例
fishing:
  xihu_lakeside:
    items:
      # 原有物品...
      - itemNo: "ITEM_FISH_01"
        rate: 0.8
      
      # 新增装备 - 剑客游湖套装
      - itemNo: "EQ_YOUHU_WQ_01"  # 青锋剑(武器)
        rate: 0.05
      - itemNo: "EQ_YOUHU_TG_01"  # 游湖冠(头部)
        rate: 0.08
      # ... 其他装备
      
      # 新增装备 - 仙师湖光套装
      - itemNo: "EQ_HUGUANG_WQ_01"  # 湖光杖(武器)
        rate: 0.05
      # ... 其他装备
      
      # 新增装备 - 圣僧印月套装  
      - itemNo: "EQ_YINYUE_WQ_01"  # 印月禅杖(武器)
        rate: 0.05
      # ... 其他装备
      
      # 通用玉佩
      - itemNo: "EQ_YUPEI_01"  # 明珠玉佩(玉佩)
        rate: 0.01
```

## 游戏体验影响

### 1. 装备获取多样化
- 玩家不再只能通过战斗或购买获得装备
- 采集活动变得更有价值和吸引力
- 增加了游戏的探索性和趣味性

### 2. 经济系统影响
- 增加了装备的供应渠道
- 可能会影响装备的市场价格
- 为低级玩家提供了更多装备获取途径

### 3. 职业平衡
- 三个职业都有相同的装备获取概率
- 玉佩作为通用装备，所有职业都能使用
- 保持了职业间的平衡性

## 后续优化建议

### 1. 动态概率调整
```yaml
# 可以根据玩家等级调整概率
- itemNo: "EQ_YOUHU_WQ_01"
  rate: 0.05
  levelRange: [1, 15]  # 1-15级时概率较高
  
- itemNo: "EQ_YOUHU_WQ_01"  
  rate: 0.02
  levelRange: [16, 29]  # 16-29级时概率降低
```

### 2. 地区特色装备
```yaml
# 不同采集地点可以有不同的装备倾向
xihu_lakeside:  # 湖心亭偏向法师装备
  - itemNo: "EQ_HUGUANG_WQ_01"
    rate: 0.08  # 提高仙师装备概率
    
xihu_duanqiao:  # 断桥偏向剑客装备  
  - itemNo: "EQ_YOUHU_WQ_01"
    rate: 0.08  # 提高剑客装备概率
```

### 3. 季节性调整
```yaml
# 可以根据游戏内季节调整概率
- itemNo: "EQ_YUPEI_01"
  rate: 0.01
  seasonMultiplier:
    spring: 1.5  # 春季玉佩概率提高
    summer: 1.0
    autumn: 1.0  
    winter: 0.5  # 冬季玉佩概率降低
```

## 测试建议

### 1. 概率验证测试
- 进行大量采集操作，验证实际掉落概率是否符合配置
- 测试不同采集类型的装备掉落是否正常
- 验证职业限制是否正确生效

### 2. 游戏平衡测试
- 观察装备获取速度是否合理
- 监控对游戏经济的影响
- 评估玩家体验的改善程度

### 3. 性能测试
- 验证增加装备掉落后的系统性能
- 测试采集系统的响应速度
- 确保不会影响游戏的整体性能

## 总结

这次配置更新为采集系统增加了丰富的装备奖励，让1-29级的三职业玩家都能通过采集活动获得合适的装备。通过合理的概率设计，既保持了装备的稀有性，又增加了采集活动的吸引力，为游戏的早期体验提供了更多的乐趣和选择。
