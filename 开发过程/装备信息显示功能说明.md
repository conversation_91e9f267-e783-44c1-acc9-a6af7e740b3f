# 装备信息显示功能说明

## 功能概述

在TradeManager中补充了装备信息的详细显示功能，当交易的物品是装备时，会分别显示基础属性和附加属性，让玩家能够清楚了解装备的完整信息。

## 实现位置

- **文件**: `game-server/src/main/java/com/xiziworld/gameserver/domain/manager/TradeManager.java`
- **方法**: `formatEquipmentInfo(Item item, UserAsset asset)`

## 功能特点

### 1. 基础属性和附加属性分开显示
- **基础属性**: 来自`item.attributes`，是装备的固有属性
- **附加属性**: 来自`asset.attributes`，是装备实例的随机属性（排除degree和plus）

### 2. 品质显示
- 显示装备的品质等级（如：3品、5品等）
- 只有品质大于1品的装备才显示品质信息

### 3. 玉佩特殊属性
- 对于玉佩装备，额外显示属性倍率（plus值）
- 格式：属性倍率：1.250

## 显示格式示例

### 普通装备示例
```
宝贝名字：青锋剑
品质：3品
基础属性：物攻+10
附加属性：物攻+5，暴击+2
数量：1
价格：1000
货币：银两
```

### 玉佩装备示例
```
宝贝名字：湖光玉佩
品质：5品
基础属性：法攻+15，法防+10
附加属性：法攻+8，血量+20
属性倍率：1.250
数量：1
价格：2000
货币：银两
```

### 无附加属性装备示例
```
宝贝名字：游湖冠
品质：1品
基础属性：物防+8
数量：1
价格：500
货币：金币
```

## 属性名称映射

系统会将属性键值转换为用户友好的显示名称：

| 属性键 | 显示名称 |
|--------|----------|
| phy_atk | 物攻 |
| mag_atk | 法攻 |
| bud_atk | 佛攻 |
| phy_def | 物防 |
| mag_def | 法防 |
| bud_def | 佛防 |
| hp | 血量 |
| mp | 法力 |
| reflect | 反伤 |
| crit | 暴击 |
| inner | 内力 |

## 技术实现细节

### 1. 方法调用位置
- `createPlayerTradeSession()` - 创建玩家交易会话时
- `getTradeSessionInfo()` - 查看交易信息时

### 2. 核心逻辑
```java
private String formatEquipmentInfo(Item item, UserAsset asset) {
    // 1. 显示品质
    // 2. 显示基础属性（来自item.attributes）
    // 3. 显示附加属性（来自asset.attributes，排除degree和plus）
    // 4. 显示玉佩的属性倍率（如果是玉佩）
}
```

### 3. 属性过滤
- 基础属性：直接从`item.attributes`读取
- 附加属性：从`asset.attributes`读取，但排除`degree`（品质）和`plus`（倍率）
- 玉佩倍率：单独处理`plus`属性

## 依赖组件

- **Helper.getAssetQuality()** - 获取装备品质
- **GameAttributeConstant** - 属性常量定义
- **JSON解析** - 处理属性JSON数据

## 使用场景

1. **玩家交易** - 发布装备到交易市场时显示详细信息
2. **交易查看** - 查看交易市场中的装备信息
3. **装备对比** - 帮助玩家了解装备的完整属性

## 扩展性

该功能设计具有良好的扩展性：
- 可以轻松添加新的属性类型
- 可以调整显示格式
- 可以扩展到其他需要装备信息显示的场景

## 测试建议

1. 测试不同品质的装备显示
2. 测试有/无附加属性的装备
3. 测试玉佩的特殊显示
4. 测试属性名称映射的正确性
