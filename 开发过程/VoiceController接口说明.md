# VoiceController接口说明

## 接口概述

VoiceController提供语音处理的Mock接口，用于模拟语音识别功能。这是一个临时开发的接口，用于其他项目的测试和开发。

## 接口详情

### 1. 语音接收接口

**接口地址**: `POST /voice/receive`

**功能**: 接收语音数据并返回识别结果

### 请求格式

```json
{
    "requestId": "uuid",
    "deviceId": "device001", 
    "region": "area01",
    "timestamp": 1640995200000,
    "audioData": "base64编码的音频数据",
    "audioFormat": {
        "sampleRate": 16000,
        "channels": 1,
        "encoding": "PCM_16BIT"
    },
    "businessParams": {
        // H5注入的业务参数
    }
}
```

#### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| requestId | String | 是 | 请求唯一标识符 |
| deviceId | String | 是 | 设备ID |
| region | String | 否 | 区域标识 |
| timestamp | Long | 否 | 请求时间戳 |
| audioData | String | 是 | Base64编码的音频数据 |
| audioFormat | Object | 否 | 音频格式信息 |
| businessParams | Object | 否 | 业务参数 |

### 响应格式

#### 成功响应
```json
{
    "requestId": "uuid",
    "success": true,
    "data": {
        "text": "识别的文本内容",
        "audioUrl": "http://server/audio/response.mp3",
        "audioBase64": "base64编码的音频",
        "businessData": {
            "processTime": 150,
            "confidence": 0.95,
            "language": "zh-CN"
        }
    }
}
```

#### 错误响应
```json
{
    "requestId": "uuid",
    "success": false,
    "error": {
        "code": "ERROR_CODE",
        "message": "错误描述"
    }
}
```

#### 响应参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| requestId | String | 请求唯一标识符 |
| success | Boolean | 请求是否成功 |
| data | Object | 成功时的数据 |
| data.text | String | 识别出的文本内容 |
| data.audioUrl | String | 响应音频URL（可选） |
| data.audioBase64 | String | 响应音频Base64（可选） |
| data.businessData | Object | 业务相关数据 |
| error | Object | 错误时的信息 |
| error.code | String | 错误代码 |
| error.message | String | 错误描述 |

## Mock实现特点

### 1. 语音识别模拟
系统会根据输入的audioData生成模拟的识别结果，包含以下预设文本：
- "你好，我想查询天气"
- "播放音乐"
- "设置闹钟"
- "今天几号"
- "帮我搜索附近的餐厅"
- "打开导航"
- "发送消息给张三"
- "查看日程安排"

### 2. 智能选择
根据audioData的哈希值选择不同的识别结果，确保相同输入得到相同输出。

### 3. 业务数据模拟
自动生成模拟的业务数据：
- processTime: 处理时间
- confidence: 识别置信度(0.95)
- language: 语言标识(zh-CN)

## 错误处理

### 错误代码

| 错误代码 | 说明 |
|----------|------|
| INVALID_PARAMS | 缺少必要参数 |
| INTERNAL_ERROR | 服务器内部错误 |

### 参数验证
- requestId: 必填
- deviceId: 必填  
- audioData: 必填

## 使用示例

### 1. cURL请求示例
```bash
curl -X POST http://localhost:8080/voice/receive \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "test-001",
    "deviceId": "device001",
    "region": "area01", 
    "timestamp": 1640995200000,
    "audioData": "SGVsbG8gV29ybGQ=",
    "audioFormat": {
      "sampleRate": 16000,
      "channels": 1,
      "encoding": "PCM_16BIT"
    }
  }'
```

### 2. JavaScript请求示例
```javascript
const response = await fetch('/voice/receive', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    requestId: 'test-001',
    deviceId: 'device001',
    region: 'area01',
    timestamp: Date.now(),
    audioData: 'SGVsbG8gV29ybGQ=',
    audioFormat: {
      sampleRate: 16000,
      channels: 1,
      encoding: 'PCM_16BIT'
    }
  })
});

const result = await response.json();
console.log(result);
```

### 3. 成功响应示例
```json
{
    "requestId": "test-001",
    "success": true,
    "data": {
        "text": "你好，我想查询天气",
        "audioUrl": "http://server/audio/response_test-001.mp3",
        "businessData": {
            "processTime": 150,
            "confidence": 0.95,
            "language": "zh-CN"
        }
    }
}
```

### 4. 错误响应示例
```json
{
    "requestId": "test-001",
    "success": false,
    "error": {
        "code": "INVALID_PARAMS",
        "message": "缺少必要参数"
    }
}
```

## 测试接口

### 健康检查接口
**接口地址**: `GET /voice/test`

**响应**: `"Voice Controller is running!"`

**用途**: 验证服务是否正常运行

## 部署说明

### 1. 启动服务
确保Spring Boot应用正常启动，接口将在以下地址可用：
- 本地开发: `http://localhost:8080/voice/receive`
- 测试环境: `http://test-server:8080/voice/receive`

### 2. 跨域配置
如果需要前端调用，可能需要配置CORS：
```java
@CrossOrigin(origins = "*")
@PostMapping("/receive")
```

### 3. 日志配置
建议添加请求日志以便调试：
```java
@Slf4j
public class VoiceController {
    // 在方法中添加日志
    log.info("Received voice request: {}", requestId);
}
```

## 注意事项

1. **Mock数据**: 这是一个Mock接口，不进行真实的语音识别
2. **临时使用**: 仅用于开发和测试，不适用于生产环境
3. **数据安全**: 请勿在生产环境中使用真实的敏感数据
4. **性能**: Mock接口响应速度较快，实际语音识别可能需要更长时间

## 扩展建议

### 1. 添加更多Mock场景
```java
// 可以根据不同参数返回不同结果
if ("weather".equals(businessParams.get("type"))) {
    return "今天天气晴朗，温度25度";
}
```

### 2. 添加延迟模拟
```java
// 模拟真实处理时间
Thread.sleep(100 + new Random().nextInt(200));
```

### 3. 添加错误场景模拟
```java
// 随机返回错误，模拟网络异常等情况
if (new Random().nextInt(100) < 5) {
    return createErrorResponse(requestId, "NETWORK_ERROR", "网络异常");
}
```

这个Mock接口提供了完整的语音处理模拟功能，可以满足其他项目的开发和测试需求。
