# 装备信息显示功能测试用例

## 测试目标
验证TradeManager中装备信息显示功能的正确性，确保基础属性和附加属性能够正确分开显示。

## 测试数据准备

### 测试装备1：青锋剑（基础装备）
```json
// Item数据
{
  "itemNo": "EQ_YOUHU_WQ_01",
  "type": 0,
  "subType": 1,
  "name": "青锋剑",
  "attributes": "{\"phy_atk\":10}"
}

// UserAsset数据
{
  "itemNo": "EQ_YOUHU_WQ_01",
  "attributes": "{\"degree\":1}"
}
```

**预期显示结果**：
```
宝贝名字：青锋剑
基础属性：物攻+10
```

### 测试装备2：强化青锋剑（有附加属性）
```json
// Item数据
{
  "itemNo": "EQ_YOUHU_WQ_01",
  "type": 0,
  "subType": 1,
  "name": "青锋剑",
  "attributes": "{\"phy_atk\":10}"
}

// UserAsset数据
{
  "itemNo": "EQ_YOUHU_WQ_01",
  "attributes": "{\"degree\":3,\"phy_atk\":5,\"crit\":2}"
}
```

**预期显示结果**：
```
宝贝名字：青锋剑
品质：3品
基础属性：物攻+10
附加属性：物攻+5，暴击+2
```

### 测试装备3：湖光玉佩（玉佩特殊属性）
```json
// Item数据
{
  "itemNo": "EQ_HUGUANG_YP_01",
  "type": 0,
  "subType": 8,
  "name": "湖光玉佩",
  "attributes": "{\"mag_atk\":15,\"mag_def\":10}"
}

// UserAsset数据
{
  "itemNo": "EQ_HUGUANG_YP_01",
  "attributes": "{\"degree\":5,\"plus\":1.250,\"mag_atk\":8,\"hp\":20}"
}
```

**预期显示结果**：
```
宝贝名字：湖光玉佩
品质：5品
基础属性：法攻+15，法防+10
附加属性：法攻+8，血量+20
属性倍率：1.250
```

### 测试装备4：复合属性装备
```json
// Item数据
{
  "itemNo": "EQ_YUEWANG_YF_01",
  "type": 0,
  "subType": 3,
  "name": "精忠铠",
  "attributes": "{\"phy_def\":30,\"mag_def\":30,\"bud_def\":30}"
}

// UserAsset数据
{
  "itemNo": "EQ_YUEWANG_YF_01",
  "attributes": "{\"degree\":7,\"hp\":50,\"reflect\":5,\"inner\":10}"
}
```

**预期显示结果**：
```
宝贝名字：精忠铠
品质：7品
基础属性：物防+30，法防+30，佛防+30
附加属性：血量+50，反伤+5，内力+10
```

## 测试方法

### 单元测试方法
```java
@Test
public void testFormatEquipmentInfo() {
    // 1. 准备测试数据
    Item item = new Item();
    item.setType(0);
    item.setSubType(1);
    item.setName("青锋剑");
    item.setAttributes("{\"phy_atk\":10}");
    
    UserAsset asset = new UserAsset();
    asset.setItemNo("EQ_YOUHU_WQ_01");
    asset.setAttributes("{\"degree\":3,\"phy_atk\":5,\"crit\":2}");
    
    // 2. 调用方法
    String result = tradeManager.formatEquipmentInfo(item, asset);
    
    // 3. 验证结果
    assertTrue(result.contains("品质：3品"));
    assertTrue(result.contains("基础属性：物攻+10"));
    assertTrue(result.contains("附加属性：物攻+5，暴击+2"));
}
```

### 集成测试方法
```java
@Test
public void testCreatePlayerTradeSessionWithEquipment() {
    // 1. 创建角色和装备
    UserCharacter character = createTestCharacter();
    UserAsset asset = createTestEquipment();
    
    // 2. 创建交易会话
    String result = tradeManager.createPlayerTradeSession(
        character.getId(), asset.getId(), 1, 1000, "silver"
    );
    
    // 3. 验证交易信息包含装备详情
    assertTrue(result.contains("基础属性："));
    assertTrue(result.contains("附加属性："));
}
```

## 边界条件测试

### 测试用例1：无附加属性装备
- **输入**：只有基础属性，asset.attributes只包含degree
- **预期**：只显示基础属性，不显示附加属性部分

### 测试用例2：1品装备
- **输入**：degree为1或null的装备
- **预期**：不显示品质信息

### 测试用例3：空属性装备
- **输入**：item.attributes为空或null
- **预期**：不显示基础属性部分

### 测试用例4：非玉佩装备
- **输入**：subType不为8的装备
- **预期**：不显示属性倍率

### 测试用例5：玉佩无plus属性
- **输入**：玉佩装备但asset.attributes中无plus
- **预期**：不显示属性倍率

## 性能测试

### 测试目标
- 验证装备信息格式化的性能
- 确保JSON解析不会成为性能瓶颈

### 测试方法
```java
@Test
public void testFormatEquipmentInfoPerformance() {
    // 准备1000个装备数据
    List<Item> items = createTestItems(1000);
    List<UserAsset> assets = createTestAssets(1000);
    
    long startTime = System.currentTimeMillis();
    
    // 批量格式化
    for (int i = 0; i < 1000; i++) {
        tradeManager.formatEquipmentInfo(items.get(i), assets.get(i));
    }
    
    long endTime = System.currentTimeMillis();
    
    // 验证性能（应该在100ms内完成）
    assertTrue((endTime - startTime) < 100);
}
```

## 错误处理测试

### 测试用例1：JSON解析异常
- **输入**：格式错误的attributes JSON
- **预期**：不抛出异常，优雅降级

### 测试用例2：空对象处理
- **输入**：item或asset为null
- **预期**：不抛出异常，返回空字符串或默认信息

## 验收标准

1. ✅ 基础属性和附加属性能够正确分开显示
2. ✅ 品质信息显示正确（>1品才显示）
3. ✅ 玉佩的属性倍率显示正确
4. ✅ 属性名称映射正确（phy_atk -> 物攻）
5. ✅ 边界条件处理正确
6. ✅ 性能满足要求（<100ms/1000次）
7. ✅ 错误处理优雅，不影响主流程

## 回归测试

确保新功能不影响现有的交易功能：
1. NPC交易功能正常
2. 玩家交易创建正常
3. 交易查询功能正常
4. 非装备物品交易正常
