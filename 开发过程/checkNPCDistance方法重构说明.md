# checkNPCDistance方法重构说明

## 重构概述

将`checkNPCDistance`方法从TradeManager移动到MapManager中，这是一次重要的代码重构，提高了代码的组织性和职责分离。

## 重构原因

### 1. 职责分离原则
- **MapManager**: 负责地图、位置、距离相关的逻辑
- **TradeManager**: 负责交易业务逻辑
- `checkNPCDistance`主要处理位置和距离计算，应该属于MapManager的职责

### 2. 代码复用性
- MapManager中已有`scanNpcs`方法，`checkNPCDistance`可以直接复用
- 其他需要距离检查的功能也可以使用这个方法
- 避免在多个Manager中重复实现类似逻辑

### 3. 依赖关系优化
- 原来TradeManager需要依赖MapManager来调用`scanNpcs`
- 现在直接在MapManager中实现，减少跨Manager的方法调用
- 更清晰的模块边界

## 重构前后对比

### 重构前的结构
```java
// TradeManager.java
@Autowired
private MapManager mapManager;

private String checkNPCDistance(UserCharacter userCharacter, String npcId) {
    // 调用mapManager.scanNpcs()
    List<NpcsConfig.NpcDetail> nearbyNpcs = mapManager.scanNpcs(userCharacter);
    // 距离检查逻辑
}

// 在交易方法中调用
String distanceCheck = checkNPCDistance(character, npcId);
```

### 重构后的结构
```java
// MapManager.java
public String checkNPCDistance(UserCharacter userCharacter, String npcId) {
    // 直接调用本类的scanNpcs()
    List<NpcsConfig.NpcDetail> nearbyNpcs = scanNpcs(userCharacter);
    // 距离检查逻辑
}

// TradeManager.java
// 调用mapManager的公共方法
String distanceCheck = mapManager.checkNPCDistance(character, npcId);
```

## 技术实现细节

### 1. 方法签名保持不变
```java
public String checkNPCDistance(UserCharacter userCharacter, String npcId)
```

### 2. 访问修饰符变更
- **重构前**: `private` (TradeManager内部方法)
- **重构后**: `public` (MapManager对外提供的服务)

### 3. 实现逻辑不变
```java
public String checkNPCDistance(UserCharacter userCharacter, String npcId) {
    try {
        // 获取附近的NPC列表（距离10米内）
        List<NpcsConfig.NpcDetail> nearbyNpcs = scanNpcs(userCharacter);
        
        // 检查指定的NPC是否在附近
        for (NpcsConfig.NpcDetail npc : nearbyNpcs) {
            if (npcId.equals(npc.getNpcNo())) {
                log.debug("NPC距离检查通过: 角色{}与NPC{}在交易距离内", 
                         userCharacter.getId(), npcId);
                return null; // 距离检查通过
            }
        }
        
        log.debug("NPC距离检查失败: 角色{}与NPC{}距离过远或NPC不存在", 
                 userCharacter.getId(), npcId);
        return "🚶‍♂️ 距离太远了！请靠近NPC再进行交易！";
        
    } catch (Exception e) {
        log.error("NPC距离检查异常: characterId={}, npcId={}", 
                 userCharacter.getId(), npcId, e);
        return "❌ 位置检查失败，请稍后再试！";
    }
}
```

## 调用方式更新

### TradeManager中的调用更新
```java
// 重构前
String distanceCheck = checkNPCDistance(character, npcId);

// 重构后  
String distanceCheck = mapManager.checkNPCDistance(character, npcId);
```

### 涉及的方法
1. `queryNPCShop()` - 查询NPC商店
2. `buyFromNPC()` - 从NPC购买物品
3. `sellToNPC()` - 向NPC出售物品

## 架构优势

### 1. 更清晰的职责分工
```
MapManager:
├── 地图管理
├── 位置计算
├── 距离检查 ← checkNPCDistance
└── NPC扫描

TradeManager:
├── 交易逻辑
├── 价格计算
├── 库存管理
└── 交易会话
```

### 2. 更好的代码复用
```java
// 其他Manager也可以使用距离检查
public class QuestManager {
    @Autowired
    private MapManager mapManager;
    
    public String acceptQuest(UserCharacter character, String npcId) {
        // 复用距离检查逻辑
        String distanceCheck = mapManager.checkNPCDistance(character, npcId);
        if (distanceCheck != null) {
            return distanceCheck;
        }
        // 任务接受逻辑
    }
}
```

### 3. 更简洁的依赖关系
```
重构前:
TradeManager → MapManager.scanNpcs()
TradeManager → 自己的checkNPCDistance()

重构后:
TradeManager → MapManager.checkNPCDistance()
MapManager → 自己的scanNpcs()
```

## 扩展性提升

### 1. 支持不同的距离要求
```java
// 可以扩展为支持自定义距离
public String checkNPCDistance(UserCharacter userCharacter, String npcId, double maxDistance) {
    // 使用自定义距离进行检查
}

// 或者针对不同场景的距离检查
public String checkNPCDistanceForTrade(UserCharacter userCharacter, String npcId) {
    return checkNPCDistance(userCharacter, npcId, TRADE_DISTANCE);
}

public String checkNPCDistanceForQuest(UserCharacter userCharacter, String npcId) {
    return checkNPCDistance(userCharacter, npcId, QUEST_DISTANCE);
}
```

### 2. 支持批量距离检查
```java
public Map<String, String> checkMultipleNPCDistance(UserCharacter userCharacter, List<String> npcIds) {
    Map<String, String> results = new HashMap<>();
    for (String npcId : npcIds) {
        results.put(npcId, checkNPCDistance(userCharacter, npcId));
    }
    return results;
}
```

### 3. 支持距离信息返回
```java
public class DistanceCheckResult {
    private boolean inRange;
    private double distance;
    private String message;
    // getters and setters
}

public DistanceCheckResult checkNPCDistanceWithInfo(UserCharacter userCharacter, String npcId) {
    // 返回详细的距离信息
}
```

## 性能优化

### 1. 减少方法调用层次
```
重构前: TradeManager.method() → TradeManager.checkNPCDistance() → MapManager.scanNpcs()
重构后: TradeManager.method() → MapManager.checkNPCDistance() → MapManager.scanNpcs()
```

### 2. 更好的缓存利用
```java
// MapManager中可以更好地管理NPC扫描的缓存
private final Map<Long, List<NpcsConfig.NpcDetail>> npcScanCache = new ConcurrentHashMap<>();

public String checkNPCDistance(UserCharacter userCharacter, String npcId) {
    // 可以利用缓存优化性能
    List<NpcsConfig.NpcDetail> nearbyNpcs = getCachedOrScanNpcs(userCharacter);
    // 检查逻辑
}
```

## 测试更新

### 1. 单元测试移动
```java
// 从TradeManagerTest移动到MapManagerTest
@Test
public void testCheckNPCDistance() {
    // 测试距离检查功能
    String result = mapManager.checkNPCDistance(testCharacter, "songwusao");
    assertNull(result); // 距离检查通过
}
```

### 2. 集成测试更新
```java
// TradeManagerTest中更新测试
@Test
public void testBuyFromNPCWithDistance() {
    // 模拟距离检查
    when(mapManager.checkNPCDistance(any(), any())).thenReturn(null);
    
    String result = tradeManager.buyFromNPC(characterId, npcId, itemName, quantity);
    // 验证交易逻辑
}
```

## 向后兼容性

### 1. 接口保持不变
- 方法签名完全相同
- 返回值格式不变
- 错误处理逻辑一致

### 2. 行为保持一致
- 距离检查逻辑不变
- 错误提示信息不变
- 日志记录格式不变

## 总结

这次重构带来的好处：

1. **职责更清晰**: 位置相关逻辑统一在MapManager中
2. **代码更复用**: 其他模块可以复用距离检查功能
3. **架构更合理**: 减少跨模块的复杂依赖
4. **扩展更容易**: 为未来的位置相关功能提供基础
5. **维护更简单**: 位置逻辑集中管理，便于维护

这是一次成功的重构，提高了代码质量和系统架构的合理性。
