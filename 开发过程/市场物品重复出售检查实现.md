# 市场物品重复出售检查实现

## 概述

为了防止玩家将已经上架到交易市场的物品再次出售给NPC，实现了市场物品状态检查功能。这个功能确保了游戏经济系统的完整性和逻辑一致性。

## 问题背景

### 潜在问题
1. **重复出售**: 玩家可能将已上架的物品再次出售给NPC
2. **数据不一致**: 同一物品同时存在于市场和NPC交易中
3. **经济漏洞**: 玩家可能利用此漏洞获得额外收益
4. **用户困惑**: 物品状态不明确，影响用户体验

### 解决方案
- 在向NPC出售物品前检查该物品是否已在市场上架
- 如果已上架，提示玩家先下架再出售
- 提供友好的错误提示和操作指导

## 技术实现

### 1. 核心检查方法

#### isAssetInMarket方法
```java
private boolean isAssetInMarket(Long assetId) {
    if (assetId == null) {
        return false;
    }
    
    // 遍历所有交易会话，检查是否有使用该资产的交易
    for (TradeSession session : tradeSessions.values()) {
        if (session.getStatus() == TradeStatus.PENDING && 
            assetId.equals(session.getAssetId())) {
            return true;
        }
    }
    
    return false;
}
```

**功能特点**:
- 检查指定资产ID是否在活跃的交易会话中
- 只检查状态为PENDING的交易（进行中的交易）
- 空值安全处理

#### getAssetTradeSession方法
```java
public TradeSession getAssetTradeSession(Long assetId) {
    if (assetId == null) {
        return null;
    }
    
    for (TradeSession session : tradeSessions.values()) {
        if (session.getStatus() == TradeStatus.PENDING && 
            assetId.equals(session.getAssetId())) {
            return session;
        }
    }
    
    return null;
}
```

**扩展功能**:
- 返回资产对应的交易会话详情
- 可用于获取交易ID、价格等信息
- 为后续功能扩展提供支持

### 2. 集成到出售流程

#### 在sellToNPC方法中的集成
```java
// 检查数量
if(asset.getCharacterId()<quantity){
    return "📦 行囊中的 " + itemName + " 不足 " + quantity + " 件！";
}

// 检查是不是上架玩家市场了，上架玩家市场后不能出售NPC
if (isAssetInMarket(asset.getId())) {
    return "🚫 此物品已在交易市场上架，请先下架后再出售给商人！";
}
```

**集成位置**:
- 在数量检查之后
- 在实际出售逻辑之前
- 确保所有前置条件都满足后再执行检查

## 使用场景示例

### 场景1: 正常出售流程
```
玩家操作: 卖 青锋剑 1
系统检查:
1. ✅ 背包中有青锋剑
2. ✅ 数量足够
3. ✅ 未在市场上架
4. ✅ 执行出售

系统回复: 💰 出售成功！青锋剑 x1 换得 800 银两
          😊 掌柜满意地点点头！
```

### 场景2: 物品已上架的情况
```
玩家操作: 
1. 卖 青锋剑 1 1000 silver  (上架到市场)
2. 卖 青锋剑 1              (尝试出售给NPC)

系统回复: 🚫 此物品已在交易市场上架，请先下架后再出售给商人！

玩家需要:
1. 取消 T1                  (先下架)
2. 卖 青锋剑 1              (再出售给NPC)
```

### 场景3: 部分上架的情况
```
玩家背包: 青锋剑 x3
玩家操作:
1. 卖 青锋剑 2 1000 silver  (上架2个到市场)
2. 卖 青锋剑 1              (尝试出售剩余1个给NPC)

系统行为:
- 检查asset.getId()对应的资产记录
- 发现该资产ID已在市场中
- 阻止出售操作

系统回复: 🚫 此物品已在交易市场上架，请先下架后再出售给商人！
```

## 技术细节

### 1. 检查逻辑
- **资产级别检查**: 基于UserAsset的ID进行检查
- **状态过滤**: 只检查PENDING状态的交易
- **精确匹配**: 使用equals方法确保ID完全匹配

### 2. 性能考虑
- **时间复杂度**: O(n)，其中n是活跃交易数量
- **空间复杂度**: O(1)，不需要额外存储空间
- **优化潜力**: 可以建立assetId到tradeId的索引提升性能

### 3. 数据一致性
- **实时检查**: 每次出售前都进行检查
- **状态同步**: 基于内存中的交易会话状态
- **原子操作**: 检查和出售在同一事务中进行

## 错误处理

### 1. 空值处理
```java
if (assetId == null) {
    return false;
}
```

### 2. 状态检查
```java
if (session.getStatus() == TradeStatus.PENDING && 
    assetId.equals(session.getAssetId())) {
    return true;
}
```

### 3. 友好提示
```java
return "🚫 此物品已在交易市场上架，请先下架后再出售给商人！";
```

## 扩展功能

### 1. 详细信息提示
可以扩展为提供更详细的信息：
```java
if (isAssetInMarket(asset.getId())) {
    TradeSession session = getAssetTradeSession(asset.getId());
    return String.format("🚫 此物品已在交易市场上架(交易号:%s，价格:%d)，请先下架后再出售给商人！", 
                        session.getTradeId(), session.getPrice());
}
```

### 2. 自动下架选项
可以提供自动下架的选项：
```java
if (isAssetInMarket(asset.getId())) {
    return "🚫 此物品已在交易市场上架，是否先下架？输入'下架并出售'确认操作。";
}
```

### 3. 批量检查
可以扩展为批量检查多个物品：
```java
public List<Long> getAssetsInMarket(List<Long> assetIds) {
    List<Long> inMarketAssets = new ArrayList<>();
    for (Long assetId : assetIds) {
        if (isAssetInMarket(assetId)) {
            inMarketAssets.add(assetId);
        }
    }
    return inMarketAssets;
}
```

## 性能优化建议

### 1. 索引优化
```java
// 可以维护一个assetId到tradeId的映射
private final Map<Long, String> assetToTradeMap = new ConcurrentHashMap<>();

// 在创建交易时添加映射
assetToTradeMap.put(assetId, tradeId);

// 在交易完成时移除映射
assetToTradeMap.remove(assetId);

// 检查时直接查询映射
private boolean isAssetInMarket(Long assetId) {
    return assetToTradeMap.containsKey(assetId);
}
```

### 2. 缓存优化
```java
// 可以缓存检查结果，减少重复计算
private final Map<Long, Boolean> assetMarketStatusCache = new ConcurrentHashMap<>();

// 在交易状态变化时清理缓存
private void clearAssetStatusCache(Long assetId) {
    assetMarketStatusCache.remove(assetId);
}
```

## 测试用例

### 1. 基本功能测试
```java
@Test
public void testAssetInMarket() {
    // 1. 上架物品到市场
    String tradeId = tradeManager.publishItem(sellerId, "青锋剑", 1, 1000, "silver");
    
    // 2. 获取资产ID
    Long assetId = getAssetIdByName(sellerId, "青锋剑");
    
    // 3. 检查是否在市场中
    assertTrue(tradeManager.isAssetInMarket(assetId));
    
    // 4. 尝试出售给NPC
    String result = tradeManager.sellToNPC(sellerId, npcId, "青锋剑", 1);
    assertTrue(result.contains("已在交易市场上架"));
}
```

### 2. 边界条件测试
```java
@Test
public void testAssetInMarketBoundary() {
    // 测试null值
    assertFalse(tradeManager.isAssetInMarket(null));
    
    // 测试不存在的资产ID
    assertFalse(tradeManager.isAssetInMarket(999999L));
    
    // 测试已完成的交易
    // ... 测试逻辑
}
```

### 3. 并发测试
```java
@Test
public void testConcurrentAssetCheck() {
    // 并发上架和检查
    // 验证线程安全性
}
```

## 监控和日志

### 1. 日志记录
```java
if (isAssetInMarket(asset.getId())) {
    log.info("阻止重复出售: 用户{}尝试出售已上架的物品{}", characterId, itemName);
    return "🚫 此物品已在交易市场上架，请先下架后再出售给商人！";
}
```

### 2. 统计信息
```java
// 可以统计被阻止的重复出售次数
private final AtomicLong blockedSellCount = new AtomicLong(0);

public long getBlockedSellCount() {
    return blockedSellCount.get();
}
```

## 总结

这个市场物品重复出售检查功能：

1. **保护游戏经济**: 防止物品重复出售导致的经济漏洞
2. **提升用户体验**: 提供清晰的错误提示和操作指导
3. **确保数据一致性**: 维护物品状态的逻辑一致性
4. **性能友好**: 简单高效的检查逻辑
5. **易于扩展**: 为后续功能扩展提供基础

这个实现有效地解决了交易系统中的一个重要边界情况，提升了系统的健壮性和用户体验。
