# scanNpcs方法实现说明

## 概述

`scanNpcs`方法是MapManager中的一个重要方法，用于获取玩家附近可交易的NPC列表。该方法主要服务于交易系统，帮助玩家找到附近的商人NPC。

## 方法签名

```java
public List<NpcsConfig.NpcDetail> scanNpcs(UserCharacter character)
```

## 功能描述

### 主要功能
- 扫描玩家附近的NPC
- 筛选距离小于10米的NPC
- 返回NPC的详细配置信息

### 筛选条件
1. **同区同地图**: 只扫描玩家当前所在区域和地图的NPC
2. **距离限制**: 只返回距离小于10米的NPC
3. **配置完整**: 只返回有完整配置信息的NPC

## 实现逻辑

### 1. 获取角色位置
```java
Map<String, Object> position = getCharacterPosition(character.getId());
String mapId = (String) position.get("mapId");
Integer playerX = (Integer) position.get("x");
Integer playerY = (Integer) position.get("y");
```

### 2. 扫描地图NPC
```java
List<TempObject> npcObjects = scanNPCs(character, mapId, playerX, playerY);
```

### 3. 距离筛选和配置匹配
```java
for (TempObject npcObj : npcObjects) {
    if (npcObj.getDistance() <= 10.0) {
        NpcsConfig.NpcDetail npcDetail = npcsConfig.getNpcs().get(npcObj.getObjectId());
        if (npcDetail != null) {
            nearbyNpcs.add(npcDetail);
        }
    }
}
```

## 与现有系统的集成

### 1. 与scanNPCs方法的关系
- `scanNPCs(character, mapId, playerX, playerY)`: 私有方法，从缓存中获取TempObject列表
- `scanNpcs(character)`: 公共方法，返回NpcDetail配置列表
- 两者配合使用，实现从缓存到配置的转换

### 2. 与交易系统的集成
- TradeCommandHandler使用此方法查找附近的商人NPC
- 支持自动识别最近的可交易NPC
- 提供完整的NPC配置信息供交易系统使用

## 使用场景

### 1. 交易命令处理
```java
// TradeCommandHandler中的使用
private String getCurrentNpcId(UserCharacter character) {
    List<NpcsConfig.NpcDetail> nearbyNpcs = mapManager.scanNpcs(character);
    
    // 查找有商店功能的NPC
    for (NpcsConfig.NpcDetail npc : nearbyNpcs) {
        if (npc.getFunctions() != null && 
            npc.getFunctions().contains(NpcsConfig.FUNCTION_SHOP)) {
            return npc.getNpcNo();
        }
    }
    
    return null;
}
```

### 2. 玩家查询附近商人
```java
// 可以扩展为玩家命令
public String listNearbyMerchants(UserCharacter character) {
    List<NpcsConfig.NpcDetail> nearbyNpcs = mapManager.scanNpcs(character);
    
    StringBuilder result = new StringBuilder("🏪 附近的商人：\n");
    for (NpcsConfig.NpcDetail npc : nearbyNpcs) {
        if (npc.getFunctions() != null && 
            npc.getFunctions().contains(NpcsConfig.FUNCTION_SHOP)) {
            result.append("- ").append(npc.getName())
                  .append(" (").append(npc.getNpcNo()).append(")\n");
        }
    }
    
    return result.toString();
}
```

## 返回数据结构

### NpcsConfig.NpcDetail包含的信息
- `npcNo`: NPC编号
- `name`: NPC名称
- `description`: NPC描述
- `location`: 所在地图
- `coordinates`: NPC坐标
- `dialogue`: 对话内容
- `functions`: NPC功能列表
- `shopId`: 商店ID（如果是商人）
- `properties`: NPC属性

### 示例返回数据
```java
[
    NpcDetail{
        npcNo="songwusao",
        name="宋五嫂",
        description="西湖边的鱼羹摊主",
        location="xihu_lakeside",
        functions=["shop"],
        shopId="shop_songwusao"
    },
    NpcDetail{
        npcNo="dingren",
        name="丁仁",
        description="药材商人",
        location="xihu_lakeside", 
        functions=["shop", "quest"],
        shopId="shop_dingren"
    }
]
```

## 性能考虑

### 1. 缓存利用
- 利用现有的mapObjectsCache缓存系统
- 避免重复的数据库查询
- 快速的内存查找

### 2. 距离计算优化
- 使用已有的calculateDistance方法
- 在scanNPCs中已经计算过距离，直接使用
- 避免重复计算

### 3. 配置查找优化
- 使用Map结构的npcs配置，O(1)查找时间
- 只对距离符合条件的NPC进行配置查找

## 错误处理

### 1. 位置信息缺失
```java
if (mapId == null || playerX == null || playerY == null) {
    log.debug("角色位置信息不完整");
    return nearbyNpcs; // 返回空列表
}
```

### 2. 配置缺失
```java
if (npcsConfig == null || npcsConfig.getNpcs() == null) {
    log.warn("NPC配置不存在");
    return nearbyNpcs; // 返回空列表
}
```

### 3. 异常处理
```java
try {
    // 主要逻辑
} catch (Exception e) {
    log.error("扫描附近NPC失败: characterId={}", character.getId(), e);
}
```

## 日志记录

### 调试日志
- 角色位置信息不完整时的调试日志
- 找到可交易NPC时的调试日志
- 扫描结果统计的调试日志

### 错误日志
- NPC配置不存在的警告日志
- 扫描过程异常的错误日志

## 扩展性

### 1. 距离参数化
可以将10米的距离限制作为参数：
```java
public List<NpcsConfig.NpcDetail> scanNpcs(UserCharacter character, double maxDistance) {
    // 使用maxDistance替代硬编码的10.0
}
```

### 2. 功能筛选
可以添加功能筛选参数：
```java
public List<NpcsConfig.NpcDetail> scanNpcsByFunction(UserCharacter character, String function) {
    // 只返回具有指定功能的NPC
}
```

### 3. 排序选项
可以添加排序选项：
```java
public List<NpcsConfig.NpcDetail> scanNpcs(UserCharacter character, boolean sortByDistance) {
    // 按距离排序返回
}
```

## 测试建议

### 1. 单元测试
- 测试正常情况下的NPC扫描
- 测试位置信息缺失的情况
- 测试配置缺失的情况
- 测试距离筛选的准确性

### 2. 集成测试
- 测试与scanNPCs方法的集成
- 测试与交易系统的集成
- 测试缓存系统的正确性

### 3. 性能测试
- 测试大量NPC情况下的性能
- 测试并发访问的性能
- 测试内存使用情况

## 注意事项

1. **线程安全**: 方法使用了共享的缓存，需要确保线程安全
2. **数据一致性**: 确保缓存中的NPC数据与配置文件一致
3. **距离精度**: 距离计算使用double类型，注意精度问题
4. **配置更新**: 当NPC配置更新时，需要同步更新缓存

## 总结

`scanNpcs`方法是交易系统的重要支撑方法，它：
- 提供了高效的附近NPC查找功能
- 与现有的缓存系统完美集成
- 支持灵活的距离筛选
- 返回完整的NPC配置信息
- 具有良好的错误处理和扩展性

该方法的实现大大简化了交易系统中NPC查找的复杂性，为玩家提供了更好的交易体验。
