# 市场物品重复出售检查测试示例

## 测试场景演示

### 场景1: 正常出售流程（无冲突）

#### 初始状态
```
玩家背包: 青锋剑 x2, 回血丹 x5
交易市场: 空
```

#### 操作序列
```
1. 玩家输入: 卖 回血丹 3
   系统检查: 
   - ✅ 背包中有回血丹
   - ✅ 数量足够(5 >= 3)
   - ✅ 回血丹未在市场上架
   
   系统回复: 💰 出售成功！回血丹 x3 换得 120 银两
            😊 掌柜满意地点点头！

2. 玩家输入: 卖 青锋剑 1 1000 silver
   系统回复: 🎺 听好了！听好了！新宝贝上架啦！
            🆔 编号：T1
            🎁 宝贝名称：青锋剑
            ...
```

#### 最终状态
```
玩家背包: 青锋剑 x1, 回血丹 x2
交易市场: T1-青锋剑 x1
玩家银两: +120
```

### 场景2: 重复出售检测（有冲突）

#### 初始状态
```
玩家背包: 青锋剑 x1
交易市场: 空
```

#### 操作序列
```
1. 玩家输入: 卖 青锋剑 1 1000 silver
   系统回复: 🎺 听好了！听好了！新宝贝上架啦！
            🆔 编号：T1
            ...

2. 玩家输入: 卖 青锋剑 1
   系统检查:
   - ✅ 背包中有青锋剑记录
   - ✅ 数量足够(1 >= 1)
   - ❌ 青锋剑已在市场上架(assetId匹配T1交易)
   
   系统回复: 🚫 此物品已在交易市场上架，请先下架后再出售给商人！
```

#### 正确操作流程
```
3. 玩家输入: 取消 T1
   系统回复: ✅ 宝物已下架，物归原主！

4. 玩家输入: 卖 青锋剑 1
   系统检查:
   - ✅ 背包中有青锋剑
   - ✅ 数量足够
   - ✅ 青锋剑未在市场上架(T1已取消)
   
   系统回复: 💰 出售成功！青锋剑 x1 换得 800 银两
            😊 掌柜满意地点点头！
```

### 场景3: 部分上架情况

#### 初始状态
```
玩家背包: 回血丹 x10
交易市场: 空
```

#### 操作序列
```
1. 玩家输入: 卖 回血丹 6 50 silver
   系统回复: 🎺 听好了！听好了！新宝贝上架啦！
            🆔 编号：T1
            🎁 宝贝名称：回血丹
            📦 数量：6 件
            ...

2. 玩家输入: 卖 回血丹 4
   系统检查:
   - ✅ 背包中有回血丹记录
   - ✅ 数量足够(4 >= 4，剩余4个)
   - ❌ 回血丹资产已在市场上架(同一个UserAsset记录)
   
   系统回复: 🚫 此物品已在交易市场上架，请先下架后再出售给商人！
```

**说明**: 即使玩家背包中还有剩余的回血丹，但由于是同一个UserAsset记录的一部分已经上架，系统会阻止出售操作。

### 场景4: 交易完成后的正常出售

#### 初始状态
```
玩家A背包: 青锋剑 x1
玩家B银两: 1000
交易市场: T1-青锋剑 x1 (玩家A上架)
```

#### 操作序列
```
1. 玩家B输入: 买 T1 1
   系统处理: 交易完成，T1被移除
   
2. 玩家A输入: 卖 青锋剑 1
   系统检查:
   - ❌ 背包中没有青锋剑(已被卖出)
   
   系统回复: 🔍 行囊中没有 青锋剑 这等物品！
```

**说明**: 交易完成后，物品已经转移，玩家无法再出售。

### 场景5: 多物品混合情况

#### 初始状态
```
玩家背包: 青锋剑 x1, 回血丹 x5, 护甲 x1
交易市场: 空
```

#### 操作序列
```
1. 玩家输入: 卖 青锋剑 1 1000 silver
   系统回复: T1上架成功

2. 玩家输入: 卖 回血丹 3
   系统检查: ✅ 回血丹未上架
   系统回复: 💰 出售成功！回血丹 x3 换得 120 银两

3. 玩家输入: 卖 护甲 1 800 silver
   系统回复: T2上架成功

4. 玩家输入: 卖 青锋剑 1
   系统回复: 🚫 此物品已在交易市场上架，请先下架后再出售给商人！

5. 玩家输入: 卖 护甲 1
   系统回复: 🚫 此物品已在交易市场上架，请先下架后再出售给商人！

6. 玩家输入: 卖 回血丹 2
   系统检查: ✅ 回血丹未上架(剩余2个)
   系统回复: 💰 出售成功！回血丹 x2 换得 80 银两
```

## 技术验证点

### 1. 数据结构验证
```java
// 验证交易会话中的assetId正确存储
TradeSession session = tradeManager.getTradeSession("T1");
assertEquals(expectedAssetId, session.getAssetId());

// 验证检查方法正确工作
assertTrue(tradeManager.isAssetInMarket(expectedAssetId));
assertFalse(tradeManager.isAssetInMarket(nonExistentAssetId));
```

### 2. 状态同步验证
```java
// 上架前
assertFalse(tradeManager.isAssetInMarket(assetId));

// 上架后
tradeManager.publishItem(sellerId, itemName, quantity, price, currency);
assertTrue(tradeManager.isAssetInMarket(assetId));

// 交易完成后
tradeManager.buyItem(buyerId, tradeId, quantity);
assertFalse(tradeManager.isAssetInMarket(assetId));

// 取消交易后
tradeManager.cancelTrade(sellerId, tradeId);
assertFalse(tradeManager.isAssetInMarket(assetId));
```

### 3. 边界条件验证
```java
// null值处理
assertFalse(tradeManager.isAssetInMarket(null));

// 不存在的assetId
assertFalse(tradeManager.isAssetInMarket(999999L));

// 已完成的交易
// 确保COMPLETED状态的交易不影响检查结果
```

## 性能测试

### 1. 基准测试
```java
@Test
public void testIsAssetInMarketPerformance() {
    // 创建1000个交易会话
    for (int i = 0; i < 1000; i++) {
        createTestTradeSession();
    }
    
    long startTime = System.currentTimeMillis();
    
    // 执行10000次检查
    for (int i = 0; i < 10000; i++) {
        tradeManager.isAssetInMarket(testAssetId);
    }
    
    long endTime = System.currentTimeMillis();
    
    // 验证性能要求: 平均每次检查 < 0.1ms
    assertTrue((endTime - startTime) < 1000);
}
```

### 2. 并发测试
```java
@Test
public void testConcurrentAssetCheck() {
    ExecutorService executor = Executors.newFixedThreadPool(10);
    CountDownLatch latch = new CountDownLatch(100);
    
    // 100个并发检查
    for (int i = 0; i < 100; i++) {
        executor.submit(() -> {
            try {
                boolean result = tradeManager.isAssetInMarket(testAssetId);
                // 验证结果一致性
            } finally {
                latch.countDown();
            }
        });
    }
    
    latch.await();
    // 验证无死锁，结果一致
}
```

## 错误场景测试

### 1. 异常数据处理
```java
// 测试交易会话数据异常
TradeSession corruptedSession = new TradeSession();
corruptedSession.setAssetId(null);
corruptedSession.setStatus(TradeStatus.PENDING);

// 系统应该能正常处理，不抛异常
assertFalse(tradeManager.isAssetInMarket(testAssetId));
```

### 2. 状态不一致处理
```java
// 模拟交易会话状态不一致的情况
// 验证系统的容错能力
```

## 用户体验验证

### 1. 错误提示友好性
```java
String result = tradeManager.sellToNPC(sellerId, npcId, itemName, quantity);

// 验证错误提示包含必要信息
assertTrue(result.contains("已在交易市场上架"));
assertTrue(result.contains("请先下架"));
assertTrue(result.contains("🚫")); // 包含emoji增强视觉效果
```

### 2. 操作指导清晰性
```java
// 错误提示应该告诉用户如何解决问题
assertTrue(result.contains("下架后再出售"));
```

## 集成测试

### 1. 与TradeCommandHandler集成
```java
@Test
public void testIntegrationWithCommandHandler() {
    // 通过命令处理器执行操作
    String result1 = tradeCommandHandler.handle(createSellCommand("卖 青锋剑 1 1000 silver"));
    assertTrue(result1.contains("上架"));
    
    String result2 = tradeCommandHandler.handle(createSellCommand("卖 青锋剑 1"));
    assertTrue(result2.contains("已在交易市场上架"));
}
```

### 2. 与数据库集成
```java
@Test
public void testDatabaseConsistency() {
    // 验证数据库中的UserAsset状态与内存中的交易会话一致
    // 确保检查逻辑基于正确的数据源
}
```

## 监控指标

### 1. 业务指标
- 被阻止的重复出售次数
- 用户下架后重新出售的成功率
- 错误提示的有效性（用户是否按提示操作）

### 2. 技术指标
- 检查方法的平均响应时间
- 并发检查的成功率
- 内存使用情况

## 总结

这个市场物品重复出售检查功能通过以下测试验证：

1. **功能正确性**: 准确识别已上架的物品
2. **用户体验**: 提供友好的错误提示和操作指导
3. **性能表现**: 满足响应时间要求
4. **并发安全**: 支持多用户并发操作
5. **数据一致性**: 确保检查结果与实际状态一致

这个功能有效地保护了游戏经济系统的完整性，防止了物品重复出售的漏洞，同时提供了良好的用户体验。
