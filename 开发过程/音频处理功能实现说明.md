# 音频处理功能实现说明

## 功能概述

在VoiceController中实现了完整的音频数据处理功能，能够将Base64编码的PCM音频数据解码并添加WAV文件头，保存为标准的WAV音频文件。

## 技术实现

### 1. 音频参数配置
根据提供的音频参数进行配置：
- **采样率**: 16000Hz
- **位深**: 16-bit
- **声道**: 1 (单声道)
- **格式**: PCM

### 2. 核心处理流程

#### processAudioData方法
```java
private String processAudioData(String audioDataBase64, String requestId) {
    // 1. 解码Base64数据
    byte[] pcmData = Base64.getDecoder().decode(audioDataBase64);
    
    // 2. 创建WAV文件头
    byte[] wavHeader = createWavHeader(pcmData.length);
    
    // 3. 合并WAV头和PCM数据
    byte[] wavData = new byte[wavHeader.length + pcmData.length];
    System.arraycopy(wavHeader, 0, wavData, 0, wavHeader.length);
    System.arraycopy(pcmData, 0, wavData, wavHeader.length, pcmData.length);
    
    // 4. 保存文件
    String filePath = saveAudioFile(wavData, requestId);
    
    return filePath;
}
```

### 3. WAV文件头结构

#### WAV文件头格式 (44字节)
```
偏移  大小  字段名称        值
0     4     ChunkID        "RIFF"
4     4     ChunkSize      文件大小-8
8     4     Format         "WAVE"
12    4     Subchunk1ID    "fmt "
16    4     Subchunk1Size  16
20    2     AudioFormat    1 (PCM)
22    2     NumChannels    1 (单声道)
24    4     SampleRate     16000
28    4     ByteRate       32000 (SampleRate * NumChannels * BitsPerSample/8)
32    2     BlockAlign     2 (NumChannels * BitsPerSample/8)
34    2     BitsPerSample  16
36    4     Subchunk2ID    "data"
40    4     Subchunk2Size  PCM数据大小
44    N     Data           PCM音频数据
```

#### createWavHeader方法实现
```java
private byte[] createWavHeader(int pcmDataLength) {
    byte[] header = new byte[44];
    
    // 音频参数
    int sampleRate = 16000;    // 采样率
    int bitsPerSample = 16;    // 位深
    int channels = 1;          // 声道数
    int byteRate = sampleRate * channels * bitsPerSample / 8;  // 字节率
    int blockAlign = channels * bitsPerSample / 8;             // 块对齐
    
    // RIFF头
    header[0] = 'R'; header[1] = 'I'; header[2] = 'F'; header[3] = 'F';
    writeInt(header, 4, 36 + pcmDataLength);
    header[8] = 'W'; header[9] = 'A'; header[10] = 'V'; header[11] = 'E';
    
    // fmt子块
    header[12] = 'f'; header[13] = 'm'; header[14] = 't'; header[15] = ' ';
    writeInt(header, 16, 16);           // fmt子块大小
    writeShort(header, 20, (short) 1); // PCM格式
    writeShort(header, 22, (short) channels);
    writeInt(header, 24, sampleRate);
    writeInt(header, 28, byteRate);
    writeShort(header, 32, (short) blockAlign);
    writeShort(header, 34, (short) bitsPerSample);
    
    // data子块头
    header[36] = 'd'; header[37] = 'a'; header[38] = 't'; header[39] = 'a';
    writeInt(header, 40, pcmDataLength);
    
    return header;
}
```

### 4. 字节序处理

WAV文件使用小端序(Little Endian)，需要专门的方法处理：

#### writeInt方法 (小端序)
```java
private void writeInt(byte[] data, int offset, int value) {
    data[offset] = (byte) (value & 0xFF);
    data[offset + 1] = (byte) ((value >> 8) & 0xFF);
    data[offset + 2] = (byte) ((value >> 16) & 0xFF);
    data[offset + 3] = (byte) ((value >> 24) & 0xFF);
}
```

#### writeShort方法 (小端序)
```java
private void writeShort(byte[] data, int offset, short value) {
    data[offset] = (byte) (value & 0xFF);
    data[offset + 1] = (byte) ((value >> 8) & 0xFF);
}
```

## 文件保存机制

### 1. 目录结构
```
项目根目录/
└── audio_files/
    ├── audio_test-001_1640995200000.wav
    ├── audio_test-002_1640995201000.wav
    └── ...
```

### 2. 文件命名规则
```
audio_{requestId}_{timestamp}.wav
```
- requestId: 请求唯一标识
- timestamp: 当前时间戳
- 扩展名: .wav

### 3. 自动目录创建
```java
String audioDir = "audio_files";
Path audioDirPath = Paths.get(audioDir);
if (!Files.exists(audioDirPath)) {
    Files.createDirectories(audioDirPath);
}
```

## 响应数据更新

### 新增字段
在响应的data中新增了`savedAudioPath`字段：
```json
{
    "requestId": "test-001",
    "success": true,
    "data": {
        "text": "你好，我想查询天气",
        "audioUrl": "http://server/audio/response_test-001.mp3",
        "savedAudioPath": "audio_files/audio_test-001_1640995200000.wav",
        "businessData": {
            "processTime": 150,
            "confidence": 0.95,
            "language": "zh-CN"
        }
    }
}
```

## 使用示例

### 1. 请求示例
```bash
curl -X POST http://localhost:8080/voice/receive \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "test-001",
    "deviceId": "device001",
    "audioData": "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="
  }'
```

### 2. 处理过程
1. **Base64解码**: 将audioData解码为二进制PCM数据
2. **WAV头创建**: 根据音频参数创建44字节的WAV文件头
3. **数据合并**: 将WAV头和PCM数据合并
4. **文件保存**: 保存为.wav文件到audio_files目录

### 3. 控制台输出
```
音频文件已保存: audio_files/audio_test-001_1640995200000.wav (大小: 1024 bytes)
```

## 音频参数计算

### 1. 字节率计算
```
ByteRate = SampleRate × NumChannels × BitsPerSample ÷ 8
         = 16000 × 1 × 16 ÷ 8
         = 32000 bytes/second
```

### 2. 块对齐计算
```
BlockAlign = NumChannels × BitsPerSample ÷ 8
           = 1 × 16 ÷ 8
           = 2 bytes
```

### 3. 文件大小计算
```
WAV文件总大小 = WAV头大小 + PCM数据大小
              = 44 + PCM数据长度
```

## 错误处理

### 1. Base64解码异常
```java
try {
    byte[] pcmData = Base64.getDecoder().decode(audioDataBase64);
} catch (IllegalArgumentException e) {
    System.err.println("Base64解码失败: " + e.getMessage());
    return null;
}
```

### 2. 文件IO异常
```java
try {
    Files.write(Paths.get(filePath), wavData);
} catch (IOException e) {
    System.err.println("文件保存失败: " + e.getMessage());
    return null;
}
```

### 3. 目录创建异常
```java
try {
    Files.createDirectories(audioDirPath);
} catch (IOException e) {
    System.err.println("目录创建失败: " + e.getMessage());
    return null;
}
```

## 验证方法

### 1. 文件验证
生成的WAV文件可以用以下工具验证：
- **Windows**: Windows Media Player
- **macOS**: QuickTime Player
- **Linux**: VLC Player
- **在线工具**: 各种音频播放器

### 2. 技术验证
```bash
# 使用ffprobe查看音频信息
ffprobe audio_files/audio_test-001_1640995200000.wav

# 预期输出
# Stream #0:0: Audio: pcm_s16le, 16000 Hz, mono, s16, 256 kb/s
```

## 注意事项

1. **内存使用**: 大音频文件会占用较多内存，建议添加大小限制
2. **磁盘空间**: 定期清理旧的音频文件
3. **并发处理**: 多个请求同时处理时注意文件名冲突
4. **安全性**: 验证Base64数据的合法性
5. **性能**: 大量音频处理可能影响服务器性能

## 扩展建议

### 1. 添加音频格式验证
```java
private boolean validateAudioData(byte[] pcmData) {
    // 验证数据长度、格式等
    return pcmData.length > 0 && pcmData.length % 2 == 0;
}
```

### 2. 添加文件大小限制
```java
private static final int MAX_AUDIO_SIZE = 10 * 1024 * 1024; // 10MB

if (pcmData.length > MAX_AUDIO_SIZE) {
    throw new IllegalArgumentException("音频文件过大");
}
```

### 3. 添加文件清理机制
```java
@Scheduled(fixedRate = 3600000) // 每小时执行一次
public void cleanupOldAudioFiles() {
    // 删除超过24小时的音频文件
}
```

这个实现提供了完整的PCM到WAV转换功能，生成的WAV文件完全符合标准格式，可以被各种音频播放器正常播放。
