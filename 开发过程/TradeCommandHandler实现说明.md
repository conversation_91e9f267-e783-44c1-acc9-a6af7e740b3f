# TradeCommandHandler实现说明

## 概述

TradeCommandHandler是交易系统的命令处理器，负责处理所有与交易相关的用户命令，并调用TradeManager的相应方法执行具体的业务逻辑。

## 支持的命令

根据CommandConfig中的定义，TradeCommandHandler支持以下5个命令：

### 1. COMMAND_BUY (购买)
- **命令别名**: `["60","买","购买","buy"]`
- **功能**: 从市场/NPC购买物品
- **支持格式**:
  - `买 物品名 数量` - 从当前位置NPC购买
  - `买 交易ID 数量` - 从玩家市场购买

### 2. COMMAND_SELL (出售)
- **命令别名**: `["61","卖","出售","sell"]`
- **功能**: 向市场/NPC出售物品
- **支持格式**:
  - `卖 物品名 数量` - 向当前位置NPC出售
  - `卖 物品名 数量 价格 货币` - 上架到玩家市场

### 3. COMMAND_ASK_NPC (询问NPC)
- **命令别名**: `["6","询问","咨询","问"]`
- **功能**: 向NPC询问信息，查看商店
- **支持格式**:
  - `询问 NPC编号` - 查看NPC商店信息

### 4. COMMAND_CANCEL_SELL (取消出售)
- **命令别名**: `["取消","下架","撤销"]`
- **功能**: 取消在市场上的售卖物品
- **支持格式**:
  - `取消 交易ID` - 下架指定的交易

### 5. COMMAND_VIEW_MARKET (查看市场)
- **命令别名**: `["市场","交易市场","查看市场"]`
- **功能**: 查看交易市场中的物品
- **支持格式**:
  - `市场` - 查看市场列表
  - `市场 交易ID` - 查看具体交易详情

## 实现架构

### 类结构
```java
@Slf4j
@Component
public class TradeCommandHandler extends AbstractCommandHandler {
    @Autowired
    private TradeManager tradeManager;
    
    // 命令验证
    protected void validate(Command command)
    
    // 命令分发
    protected String doHandle(Command command)
    
    // 具体处理方法
    private String handleBuy(Command command, UserCharacter character)
    private String handleSell(Command command, UserCharacter character)
    private String handleAskNpc(Command command, UserCharacter character)
    private String handleCancelSell(Command command, UserCharacter character)
    private String handleViewMarket(Command command, UserCharacter character)
    
    // 辅助方法
    private String[] parseParams(String target)
    private String getCurrentNpcId(UserCharacter character)
}
```

### 命令验证逻辑
1. **角色存在检查**: 所有交易命令都需要角色存在
2. **角色状态检查**: 除了查看市场，其他命令需要角色活着
3. **目标存在检查**: 询问NPC命令需要检查目标NPC存在

### 命令分发逻辑
使用switch语句根据命令名称分发到对应的处理方法，统一异常处理。

## 具体实现细节

### 1. 购买命令处理 (handleBuy)

**参数解析**:
- 解析 `物品名/交易ID` 和 `数量`
- 验证数量为正整数

**业务逻辑**:
- 判断第一个参数是否为交易ID（以"TRADE_"开头）
- 如果是交易ID：调用 `tradeManager.buyItem()`
- 如果是物品名：获取当前NPC，调用 `tradeManager.buyFromNPC()`

**错误处理**:
- 参数不足：返回格式提示
- 数量无效：返回数字格式错误
- 附近无NPC：返回无商人提示

### 2. 出售命令处理 (handleSell)

**参数解析**:
- 解析 `物品名`、`数量`、可选的 `价格` 和 `货币`
- 验证数量和价格为正整数

**业务逻辑**:
- 如果有价格参数：上架到玩家市场，调用 `tradeManager.publishItem()`
- 如果无价格参数：向NPC出售，调用 `tradeManager.sellToNPC()`

**默认值**:
- 货币默认为 "silver"

### 3. 询问NPC处理 (handleAskNpc)

**参数解析**:
- 解析NPC编号

**业务逻辑**:
- 直接调用 `tradeManager.queryNPCShop()`

### 4. 取消出售处理 (handleCancelSell)

**参数解析**:
- 解析交易ID

**业务逻辑**:
- 直接调用 `tradeManager.cancelTrade()`

### 5. 查看市场处理 (handleViewMarket)

**参数解析**:
- 可选的交易ID参数

**业务逻辑**:
- 如果有交易ID：调用 `tradeManager.getTradeDetail()`
- 如果无参数：调用 `tradeManager.getTradeList()`

## 辅助方法

### parseParams(String target)
- 解析命令参数字符串
- 按空格分割参数
- 处理空值和空字符串

### getCurrentNpcId(UserCharacter character)
- 获取角色当前位置的NPC ID
- **TODO**: 需要根据地图系统实现
- 当前返回临时默认值 "NPC_SHOP_01"

## 使用示例

### NPC交易示例
```
用户输入: 询问 NPC_SHOP_01
系统回复: 🏪 杂货铺 商品列表...

用户输入: 买 回血丹 5
系统回复: 🎉 交易成功！获得 回血丹 x5...

用户输入: 卖 破剑 1
系统回复: 💰 出售成功！破剑 x1 换得 10 银两...
```

### 玩家市场示例
```
用户输入: 卖 青锋剑 1 1000 silver
系统回复: 🎺 听好了！听好了！新宝贝上架啦！...

用户输入: 市场
系统回复: 🏪 ═══ 西湖交易市场 ═══...

用户输入: 买 TRADE_1234567890_123 1
系统回复: 🎉 交易成功！获得 青锋剑 x1...

用户输入: 取消 TRADE_1234567890_456
系统回复: ✅ 宝物已下架，物归原主！
```

## 错误处理

### 参数错误
- 参数不足：返回格式提示
- 数字格式错误：返回友好提示
- 参数值无效：返回验证错误

### 业务错误
- 通过TradeManager抛出的GameException
- 统一在doHandle中捕获并返回错误消息

### 系统错误
- 捕获所有未预期异常
- 记录日志并返回通用错误消息

## 扩展点

### 1. NPC定位系统
当前的 `getCurrentNpcId()` 方法需要与地图系统集成：
- 根据角色位置查找附近的NPC
- 支持多个NPC的选择
- 处理NPC不存在的情况

### 2. 命令格式扩展
可以支持更多的命令格式：
- 支持物品编号购买
- 支持批量操作
- 支持条件查询

### 3. 权限控制
可以添加更细粒度的权限控制：
- VIP用户特殊权限
- 等级限制
- 地区限制

## 测试建议

### 单元测试
- 测试各种参数组合
- 测试边界条件
- 测试异常情况

### 集成测试
- 与TradeManager的集成
- 与地图系统的集成
- 完整的交易流程测试

### 性能测试
- 高并发交易测试
- 大量数据查询测试
- 内存使用测试

## 注意事项

1. **线程安全**: TradeManager中的交易会话使用ConcurrentHashMap，保证线程安全
2. **事务处理**: 涉及数据库操作的方法已添加@Transactional注解
3. **日志记录**: 重要操作都有详细的日志记录
4. **异常处理**: 统一的异常处理机制，保证系统稳定性
