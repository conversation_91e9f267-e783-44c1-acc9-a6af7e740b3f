# 交易号系统测试用例

## 测试目标
验证新的交易号计数器系统的正确性，确保号码分配、回收和区域隔离功能正常工作。

## 测试场景

### 场景1: 基本号码分配测试

#### 测试步骤
1. 区域1玩家A上架物品
2. 区域1玩家B上架物品  
3. 区域1玩家C上架物品

#### 预期结果
```
玩家A上架: 分配交易号 T1
玩家B上架: 分配交易号 T2
玩家C上架: 分配交易号 T3

交易市场显示:
🆔 编号：T1
🎁 宝贝：青锋剑
...
🆔 编号：T2  
🎁 宝贝：回血丹
...
🆔 编号：T3
🎁 宝贝：护甲
...
```

#### 验证点
- ✅ 交易号从T1开始分配
- ✅ 交易号连续递增
- ✅ 格式为"T+数字"

### 场景2: 号码回收测试

#### 测试步骤
1. 区域1有交易T1, T2, T3
2. 玩家购买T2的物品（交易完成）
3. 新玩家上架物品

#### 预期结果
```
初始状态: T1, T2, T3
T2交易完成后: T1, T3 (T2被释放)
新物品上架: 分配T2 (复用释放的号码)

最终状态: T1, T2, T3
```

#### 验证点
- ✅ 交易完成后号码被释放
- ✅ 新交易优先使用最小的可用号码
- ✅ 号码序列保持紧凑

### 场景3: 区域隔离测试

#### 测试步骤
1. 区域1玩家上架3个物品
2. 区域2玩家上架2个物品
3. 区域3玩家上架1个物品

#### 预期结果
```
区域1交易: T1, T2, T3
区域2交易: T1, T2 (与区域1独立)
区域3交易: T1 (与其他区域独立)

各区域交易号互不影响
```

#### 验证点
- ✅ 不同区域的交易号独立计数
- ✅ 相同号码可以在不同区域同时存在
- ✅ 区域间不会产生冲突

### 场景4: 取消交易测试

#### 测试步骤
1. 区域1有交易T1, T2, T3
2. 玩家取消T2交易
3. 新玩家上架物品

#### 预期结果
```
初始状态: T1, T2, T3
取消T2后: T1, T3 (T2被释放)
新物品上架: 分配T2 (复用释放的号码)

系统提示: "✅ 宝物已下架，物归原主！"
```

#### 验证点
- ✅ 取消交易后号码被释放
- ✅ 物品返回玩家背包
- ✅ 号码可以被新交易复用

### 场景5: 并发安全测试

#### 测试步骤
1. 同一区域10个玩家同时上架物品
2. 同时有5个交易完成
3. 同时有3个新交易创建

#### 预期结果
```
并发上架: 分配T1-T10，无重复
并发完成: 释放5个号码
并发创建: 复用最小的3个可用号码

最终状态: 8个活跃交易，号码连续
```

#### 验证点
- ✅ 并发操作不产生重复号码
- ✅ 线程安全，无竞态条件
- ✅ 号码分配和释放正确

### 场景6: 用户体验测试

#### 测试步骤
1. 玩家查看交易市场
2. 玩家购买物品
3. 玩家上架物品

#### 预期结果
```
查看市场:
🏪 ═══ 西湖交易市场 ═══
🆔 编号：T1
🎁 宝贝：青锋剑
🛒 购买指令：买 T1 数量

购买物品:
用户输入: 买 T1 1
系统回复: 🎉 交易成功！获得 青锋剑 x1...

上架物品:
用户输入: 卖 回血丹 5 100 silver
系统回复: 🎺 听好了！听好了！新宝贝上架啦！
          🆔 编号：T2
          ...
```

#### 验证点
- ✅ 交易号简短易输入
- ✅ 购买命令简化
- ✅ 用户体验流畅

## 边界条件测试

### 测试1: 大量交易测试
```
创建1000个交易，验证:
- 号码分配正确 (T1-T1000)
- 内存使用合理
- 性能表现良好
```

### 测试2: 频繁回收测试
```
创建100个交易，然后全部完成，再创建100个:
- 第二批交易复用T1-T100
- 号码不会无限增长
- 内存得到释放
```

### 测试3: 空区域清理测试
```
区域1有交易，区域2无交易:
- 调用cleanupTradeIdCounters()
- 区域2的计数器被清理
- 区域1的计数器保留
```

## 性能测试

### 测试1: 分配性能
```java
// 测试10000次号码分配的耗时
long startTime = System.currentTimeMillis();
for (int i = 0; i < 10000; i++) {
    String tradeId = tradeManager.generateTradeId(1L);
}
long endTime = System.currentTimeMillis();
// 预期: < 100ms
```

### 测试2: 并发性能
```java
// 100个线程同时分配号码
ExecutorService executor = Executors.newFixedThreadPool(100);
CountDownLatch latch = new CountDownLatch(100);

for (int i = 0; i < 100; i++) {
    executor.submit(() -> {
        try {
            String tradeId = tradeManager.generateTradeId(1L);
            // 验证号码唯一性
        } finally {
            latch.countDown();
        }
    });
}

latch.await();
// 预期: 无死锁，号码无重复
```

### 测试3: 内存使用
```java
// 创建大量交易后检查内存使用
for (int i = 0; i < 10000; i++) {
    // 创建交易
    // 完成交易
}

// 调用清理方法
tradeManager.cleanupTradeIdCounters();

// 检查内存使用情况
Runtime runtime = Runtime.getRuntime();
long usedMemory = runtime.totalMemory() - runtime.freeMemory();
// 预期: 内存使用稳定，无内存泄漏
```

## 集成测试

### 测试1: 与TradeCommandHandler集成
```
用户输入: 卖 青锋剑 1 1000 silver
预期流程:
1. TradeCommandHandler.handleSell()
2. TradeManager.publishItem()
3. generateTradeId(areaId) -> "T1"
4. 返回包含T1的上架消息

用户输入: 买 T1 1
预期流程:
1. TradeCommandHandler.handleBuy()
2. TradeManager.buyItem()
3. releaseTradeId() 释放T1
4. 返回购买成功消息
```

### 测试2: 与数据库集成
```
验证点:
- 交易会话正确存储
- 交易完成后数据正确更新
- 号码释放不影响数据一致性
```

### 测试3: 与缓存系统集成
```
验证点:
- 交易号计数器与交易会话缓存同步
- 服务器重启后状态恢复
- 缓存清理不影响活跃交易
```

## 错误处理测试

### 测试1: 异常输入处理
```java
// 测试无效的交易ID格式
tradeManager.releaseTradeId(1L, "INVALID_ID");
// 预期: 不抛异常，记录警告日志

tradeManager.releaseTradeId(1L, "T999999999999999999");
// 预期: 捕获NumberFormatException，记录警告
```

### 测试2: 并发异常处理
```java
// 模拟并发访问时的异常情况
// 预期: 系统稳定，不影响其他操作
```

### 测试3: 内存不足处理
```java
// 模拟内存不足的情况
// 预期: 优雅降级，不影响核心功能
```

## 监控测试

### 测试1: 日志记录
```
验证日志内容:
- 号码分配: "为区域1分配交易号: T1"
- 号码释放: "释放区域1的交易号: T1"  
- 清理操作: "清理区域2的交易号计数器"
```

### 测试2: 统计信息
```java
String stats = tradeManager.getTradeIdStats();
验证输出格式:
📊 交易号使用统计：
区域 1: 使用中 3 个交易号
  使用的号码: [1, 2, 5]
```

## 验收标准

### 功能完整性
- ✅ 交易号格式为"T+数字"
- ✅ 号码从1开始分配
- ✅ 交易完成后号码被释放
- ✅ 号码可以被复用
- ✅ 不同区域号码独立

### 性能要求
- ✅ 号码分配时间 < 1ms
- ✅ 并发安全，无死锁
- ✅ 内存使用合理，支持清理

### 用户体验
- ✅ 交易号简短易输入
- ✅ 购买命令简化
- ✅ 错误处理友好

### 代码质量
- ✅ 线程安全实现
- ✅ 异常处理完善
- ✅ 日志记录详细
- ✅ 代码结构清晰

## 回归测试

确保新的交易号系统不影响现有功能:
1. NPC交易功能正常
2. 玩家交易创建正常
3. 交易查询功能正常
4. 货币兑换功能正常
5. 其他Manager功能正常

## 总结

新的交易号计数器系统通过以下测试验证:
- 基本功能测试确保核心逻辑正确
- 边界条件测试确保系统稳定
- 性能测试确保满足要求
- 集成测试确保与其他系统协作正常
- 用户体验测试确保操作简便

这个系统大大提升了文字游戏中交易操作的便利性，让玩家能够使用简短的交易号进行快速交易。
