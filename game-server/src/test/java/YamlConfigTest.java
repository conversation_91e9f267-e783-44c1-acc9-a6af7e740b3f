import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.io.InputStream;

/**
 * Simple YAML config test
 */
public class YamlConfigTest {

    public static void main(String[] args) {
        try {
            // Load YAML file
            InputStream inputStream = YamlConfigTest.class.getClassLoader()
                .getResourceAsStream("config/system/command.yml");

            if (inputStream == null) {
                System.err.println("Config file not found: config/system/command.yml");
                return;
            }

            // Create YAML parser
            Yaml yaml = new Yaml(new Constructor(CommandConfig.class));

            // Parse config
            CommandConfig config = yaml.load(inputStream);

            System.out.println("SUCCESS: YAML config file parsed successfully!");
            System.out.println("Command count: " + (config.getCommands() != null ? config.getCommands().size() : 0));

            if (config.getGlobalLimits() != null) {
                System.out.println("Global limits config:");
                System.out.println("  Max commands per minute: " + config.getGlobalLimits().getMaxCommandsPerMinute());
                System.out.println("  Max commands per hour: " + config.getGlobalLimits().getMaxCommandsPerHour());
            }

            // Test a specific command config
            if (config.getCommands() != null && config.getCommands().containsKey("view_status")) {
                CommandConfig.CommandDetail viewStatus = config.getCommands().get("view_status");
                System.out.println("view_status command config:");
                System.out.println("  Aliases: " + viewStatus.getAliases());
                System.out.println("  Description: " + viewStatus.getDescription());
                if (viewStatus.getRateLimit() != null) {
                    System.out.println("  Rate limit config:");
                    System.out.println("    Time window: " + viewStatus.getRateLimit().getSeconds() + " seconds");
                    System.out.println("    Max count: " + viewStatus.getRateLimit().getMaxCount());
                    System.out.println("    Message: " + viewStatus.getRateLimit().getMessage());
                }
            }

        } catch (Exception e) {
            System.err.println("ERROR: YAML config file parsing failed:");
            e.printStackTrace();
        }
    }
}
