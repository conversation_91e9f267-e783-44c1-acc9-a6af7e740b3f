# 宝箱道具配置
# 基于需求文档3.5抽奖系统和item.sql
# 配置格式：treasure下以宝箱物品编号(itemNo)为key，配置该宝箱能开出的物品
treasure:
  # 初级神奇盒子配置
  ITEM_BOX_01:
    itemNo: "ITEM_BOX_01"  # 初级神奇盒子
    name: "初级神奇盒子"
    description: "可以开出基础道具的神奇盒子"
    cost: 1           # 开启消耗(金币)
    rewards:
      - group: "common"
        rate: 1.0       # 100%概率
        items:
          - itemNo: "ITEM_GOLD"  # 金币
            min: 50
            max: 200
            rate: 0.6
          - itemNo: "ITEM_DRUG_HP_01"  # 回气丹
            count: 2
            rate: 0.4
          - itemNo: "ITEM_DRUG_MP_01"  # 回法丹
            count: 2
            rate: 0.3

      - group: "rare"
        rate: 0.5       # 50%概率
        items:
          - itemNo: "ITEM_MAT_SILK"  # 天蚕丝
            count: 1
            rate: 0.1
          - itemNo: "ITEM_MAT_STONE"  # 雨花石
            count: 1
            rate: 0.1

  # 中级神奇盒子配置
  ITEM_BOX_02:
    itemNo: "ITEM_BOX_02"  # 中级神奇盒子
    name: "中级神奇盒子"
    description: "可以开出中级道具的神奇盒子"
    cost: 1           # 开启消耗(银两)
    rewards:
      - group: "common"
        rate:  1.0       # 100%概率
        items:
          - itemNo: "ITEM_GOLD"  # 金币
            min: 100
            max: 500
            rate: 0.5
          - itemNo: "ITEM_DRUG_HP_01"  # 回气丹
            count: 5
            rate: 0.3
          - itemNo: "ITEM_DRUG_MP_01"  # 回法丹
            count: 5
            rate: 0.1

      - group: "rare"
        rate: 0.8       # 70%概率
        items:
          - itemNo: "EQ_YOUHU_WQ_01"  # 青锋剑
            count: 1
            rate: 0.3
          - itemNo: "EQ_HUGUANG_WQ_01"  # 湖光杖
            count: 1
            rate: 0.3
          - itemNo: "EQ_YINYUE_WQ_01"  # 印月禅杖
            count: 1
            rate: 0.3
          - itemNo: "ITEM_MAT_STONE"  # 雨花石
            count: 2
            rate: 0.1
          - itemNo: "ITEM_MAT_METEOR"  # 天外陨石
            count: 2
            rate: 0.05

  # 高级神奇盒子配置
  ITEM_BOX_03:
    itemNo: "ITEM_BOX_03"  # 高级神奇盒子
    name: "高级神奇盒子"
    description: "可以开出高级道具和技能的神奇盒子"
    cost: 5           # 开启消耗(银两)
    rewards:
      - group: "common"
        rate:  1.0       # 100%概率
        items:
          - itemNo: "ITEM_GOLD"  # 金币
            min: 500
            max: 10000
            rate: 0.4
          - itemNo: "ITEM_DRUG_SP_01"  # 龙井仙露
            count: 2
            rate: 0.3

      - group: "rare"
        rate: 0.3       # 30%概率
        items:
          - itemNo: "ITEM_SKILL_JK_01"  # 破军剑法
            count: 1
            rate: 0.3
          - itemNo: "ITEM_SKILL_XS_01"  # 玄雷术
            count: 1
            rate: 0.3
          - itemNo: "ITEM_SKILL_SS_01"  # 金刚掌
            count: 1
            rate: 0.3
          - itemNo: "ITEM_MAT_STONE_02"  # 天外陨石
            count: 1
            rate: 0.1
          - itemNo: "ITEM_MAT_HUANLING"  # 日月同辉
            count: 1
            rate: 0.05

      - group: "epic"
        rate: 0.1       # 10%概率
        items:
          - itemNo: "ITEM_DRUG_SP_02"  # 灵芝仙草
            count: 1
            rate: 1.0

  # 稀有神奇盒子配置
  ITEM_BOX_04:
    itemNo: "ITEM_BOX_04"  # 稀有神奇盒子
    name: "稀有神奇盒子"
    description: "可以开出稀有道具和高级技能的神奇盒子"
    cost: 100         # 开启消耗(银两)
    rewards:
      - group: "guaranteed"
        rate: 1.0       # 100%概率
        items:
          - itemNo: "ITEM_GOLD"  # 金币
            min: 1000
            max: 50000
            rate: 1.0

      - group: "rare"
        rate: 0.8       # 80%概率
        items:
          - itemNo: "ITEM_MAT_STONE_02"  # 天外陨石
            count: 3
            rate: 0.5
          - itemNo: "ITEM_DRUG_SP_02"  # 灵芝仙草
            count: 2
            rate: 0.5
          - itemNo: "ITEM_MAT_HUANLING"  # 日月同辉
            count: 1
            rate: 0.5

      - group: "epic"
        rate: 0.8       # 30%概率
        items:
          - itemNo: "ITEM_SKILL_JK_01"  # 破军剑法
            count: 1
            rate: 0.3
          - itemNo: "ITEM_SKILL_XS_01"  # 玄雷术
            count: 1
            rate: 0.3
          - itemNo: "ITEM_SKILL_SS_01"  # 金刚掌
            count: 1
            rate: 0.4
