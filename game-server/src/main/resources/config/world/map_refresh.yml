# 地图刷怪配置
# 基于数据库monster.sql的怪物刷新配置
mapRefresh:
  # 苏堤刷怪配置
  sudi:
    mapId: "sudi"
    mapName: "苏堤"
    refreshPoints:
      - monsterNo: "MON_WILLOW"
        position: {x: -1, y: 0}
        interval: 300  # 刷新间隔(秒)
        count: 5       # 刷新数量
      - monsterNo: "MON_FROG"
        position: {x: -1, y: 1}
        interval: 300
        count: 5

  # 断桥刷怪配置
  duanqiao:
    mapId: "duanqiao"
    mapName: "断桥"
    refreshPoints:
      - monsterNo: "MON_GHOST"
        position: {x: 0, y: 1}
        interval: 600
        count: 5

  # 孤山刷怪配置
  gushan:
    mapId: "gushan"
    mapName: "孤山"
    refreshPoints:
      - monsterNo: "MON_SOUL"
        position: {x: 1, y: 0}
        interval: 900
        count: 5
      - monsterNo: "MON_WOLF"
        position: {x: 1, y: 1}
        interval: 900
        count: 5

  # 太子湾刷怪配置
  taiziwwan:
    mapId: "taiziwwan"
    mapName: "太子湾"
    refreshPoints:
      - monsterNo: "MON_FLOWER"
        position: {x: 1, y: 1}
        interval: 1200
        count: 5
      - monsterNo: "MON_MONK"
        position: {x: 2, y: 1}
        interval: 1200
        count: 5

  # 雷峰塔刷怪配置
  leifengta:
    mapId: "leifengta"
    mapName: "雷峰塔底"
    refreshPoints:
      - monsterNo: "MON_FAHAI"  # BOSS怪物
        position: {x: 0, y: -1}
        interval: 3600  # 1小时刷新一次
        count: 1
      - monsterNo: "MON_WARRIOR"
        position: {x: -1, y: -1}
        interval: 1800
        count: 5
      - monsterNo: "MON_TURTLE"
        position: {x: 1, y: -1}
        interval: 1800
        count: 5
      - monsterNo: "MON_CRAB"
        position: {x: 0, y: -2}
        interval: 1800
        count: 5

  # 湖心亭刷怪配置
  huxinting:
    mapId: "huxinting"
    mapName: "湖心亭"
    refreshPoints:
      - monsterNo: "MON_DRAGON"  # BOSS怪物
        position: {x: 0, y: -2}
        interval: 7200  # 2小时刷新一次
        count: 1
      - monsterNo: "MON_CRAB_ELITE"
        position: {x: -1, y: -2}
        interval: 3600
        count: 5
      - monsterNo: "MON_FISH"
        position: {x: 1, y: -2}
        interval: 3600
        count: 5
