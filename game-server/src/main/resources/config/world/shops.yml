# NPC商店配置
# 基于需求文档4.9 NPC商店配置和item.sql
shops:
  qianchuShop:
    shopId: "qianchu_shop"
    name: "钱镠商店"
    npcNo: "qianchu"
    location: "wulin"
    description: "买卖天蚕丝、桑叶"
    items:
      ITEM_MAT_SILK:  # 天蚕丝
        price: 100
        currency: "gold"
        stock: -1
      ITEM_MAT_PLANT_SAN:  # 桑叶
        price: 10
        currency: "gold"
        stock: -1
      ITEM_MAT_PLANT_CHA:  # 茶叶
        price: 15
        currency: "gold"
        stock: -1
    buyRate: 1.0
    sellRate: 0.8

  songwusaoShop:
    shopId: "songwusao_shop"
    name: "宋五嫂鱼店"
    npcNo: "songwusao"
    location: "duanqiao"
    description: "买卖鱼类"
    items:
      ITEM_MAT_FISH_01:  # 白条
        price: 1
        currency: "gold"
        stock: -1
      ITEM_MAT_FISH_02:  # 鲫鱼
        price: 10
        currency: "gold"
        stock: -1
      ITEM_MAT_FISH_03:  # 鲤鱼
        price: 20
        currency: "gold"
        stock: -1
      ITEM_MAT_FISH_04:  # 鳜鱼
        price: 80
        currency: "gold"
        stock: -1
      ITEM_MAT_FISH_05:  # 鳗鱼
        price: 200
        currency: "gold"
        stock: -1
    buyRate: 1.0
    sellRate: 0.8

  yuefeiShop:
    shopId: "yuefei_shop"
    name: "岳飞装备店"
    npcNo: "yuefei"
    location: "wulin"
    description: "买卖装备"
    items:
      # 1-29级武器 (5000金币)
      EQ_YOUHU_WQ_01:  # 青锋剑
        price: 5000
        currency: "gold"
        stock: -1
      EQ_HUGUANG_WQ_01:  # 湖光杖
        price: 5000
        currency: "gold"
        stock: -1
      EQ_YINYUE_WQ_01:  # 印月禅杖
        price: 5000
        currency: "gold"
        stock: -1

      # 1-29级头部装备 (4000金币)
      EQ_YOUHU_TG_01:  # 游湖冠
        price: 4000
        currency: "gold"
        stock: -1
      EQ_HUGUANG_TG_01:  # 湖光冠
        price: 4000
        currency: "gold"
        stock: -1
      EQ_YINYUE_TG_01:  # 印月冠
        price: 4000
        currency: "gold"
        stock: -1

      # 1-29级衣服装备 (4800金币，接近武器价格)
      EQ_YOUHU_YF_01:  # 游湖袍
        price: 4800
        currency: "gold"
        stock: -1
      EQ_HUGUANG_YF_01:  # 湖光法袍
        price: 4800
        currency: "gold"
        stock: -1
      EQ_YINYUE_YF_01:  # 印月袈裟
        price: 4800
        currency: "gold"
        stock: -1

      # 1-29级护手装备 (3500金币)
      EQ_YOUHU_HS_01:  # 游湖护腕
        price: 3500
        currency: "gold"
        stock: -1
      EQ_HUGUANG_HS_01:  # 湖光护腕
        price: 3500
        currency: "gold"
        stock: -1
      EQ_YINYUE_HS_01:  # 印月护腕
        price: 3500
        currency: "gold"
        stock: -1

      # 1-29级护膝装备 (3500金币)
      EQ_YOUHU_HX_01:  # 游湖护膝
        price: 3500
        currency: "gold"
        stock: -1
      EQ_HUGUANG_HX_01:  # 湖光护膝
        price: 3500
        currency: "gold"
        stock: -1
      EQ_YINYUE_HX_01:  # 印月护膝
        price: 3500
        currency: "gold"
        stock: -1

      # 1-29级裤子装备 (3000金币)
      EQ_YOUHU_KZ_01:  # 游湖长裤
        price: 3000
        currency: "gold"
        stock: -1
      EQ_HUGUANG_KZ_01:  # 湖光长裤
        price: 3000
        currency: "gold"
        stock: -1
      EQ_YINYUE_KZ_01:  # 印月长裤
        price: 3000
        currency: "gold"
        stock: -1

      # 1-29级鞋子装备 (2500金币)
      EQ_YOUHU_XZ_01:  # 游湖靴
        price: 2500
        currency: "gold"
        stock: -1
      EQ_HUGUANG_XZ_01:  # 湖光靴
        price: 2500
        currency: "gold"
        stock: -1
      EQ_YINYUE_XZ_01:  # 印月靴
        price: 2500
        currency: "gold"
        stock: -1

      # 1-29级玉佩装备 (6000金币，最贵)
      EQ_YUPEI_01:  # 明珠玉佩
        price: 6000
        currency: "gold"
        stock: -1
    buyRate: 1.0
    sellRate: 0.5

  dingrenShop:
    shopId: "dingren_shop"
    name: "丁仁技能书店"
    npcNo: "dingren"
    location: "wulin"
    description: "买卖技能书"
    items:
      ITEM_SKILL_JK_01:  # 破军剑法
        price: 100000
        currency: "gold"
        stock: -1
      ITEM_SKILL_XS_01:  # 玄雷术
        price: 100000
        currency: "gold"
        stock: -1
      ITEM_SKILL_SS_01:  # 金刚掌
        price: 100000
        currency: "gold"
        stock: -1
    buyRate: 1.0
    sellRate: 0.5

  xuxianShop:
    shopId: "xuxian_shop"
    name: "许仙药店"
    npcNo: "xuxian"
    location: "duanqiao"
    description: "买卖药品"
    items:
      ITEM_DRUG_HP_01:  # 回气丹
        price: 100
        currency: "gold"
        stock: -1
      ITEM_DRUG_MP_01:  # 回法丹
        price: 100
        currency: "gold"
        stock: -1
      ITEM_DRUG_SP_01:  # 龙井仙露
        price: 5
        currency: "silver"
        stock: 10
      ITEM_DRUG_SP_02:  # 灵芝仙草
        price: 100
        currency: "silver"
        stock: 5
    buyRate: 1.0
    sellRate: 0.8

  jigongShop:
    shopId: "jigong_shop"
    name: "济公神奇盒子店"
    npcNo: "jigong"
    location: "wulin"
    description: "出售神奇盒子"
    items:
      ITEM_BOX_01:  # 初级神奇盒子
        price: 10000
        currency: "gold"
        stock: -1
      ITEM_BOX_02:  # 中级神奇盒子
        price: 1
        currency: "silver"
        stock: -1
      ITEM_BOX_03:  # 高级神奇盒子
        price: 50
        currency: "silver"
        stock: -1
      ITEM_BOX_04:  # 稀有神奇盒子
        price: 500
        currency: "silver"
        stock: -1
    buyRate: 1.0
    sellRate: 0.0  # 不回收
