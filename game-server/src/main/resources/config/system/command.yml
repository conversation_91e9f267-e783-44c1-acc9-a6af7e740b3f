# 命令别名和限流配置
# 基于需求文档2.7命令系统设计
commands:
  # ==================== 查看类命令 ====================
  # 查看状态
  viewStatus:
    aliases: ["2","查看状态", "状态", "st", "我","zt"]
    description: "显示角色当前行动、状态、位置、死亡信息"
    rateLimit:
      seconds: 1
      maxCount: 3
      message: "查看信息过于频繁，请稍作休息 👀"

  # 查看地图
  viewMap:
    aliases: ["21","查看地图", "地图", "map","dt"]
    description: "显示地图、人物分布、boss分布，返回列表，并有编号"
    rateLimit:
      seconds: 2
      maxCount: 2
      message: "地图查看过于频繁 🗺️"

  # 查看装备
  viewEquipment:
    aliases: ["22","查看装备", "装备", "zb"]
    description: "显示装备属性和装备情况"
    rateLimit:
      seconds: 1
      maxCount: 3
      message: "装备查看过于频繁 🎽"

  # 查看背包
  viewInventory:
    aliases: ["23","查看背包", "背包", "bb"]
    description: "显示背包内容，支持@玩家和序号参数"
    rateLimit:
      seconds: 1
      maxCount: 3
      message: "背包查看过于频繁 🎒"

  # 查看排名
  viewRanking:
    aliases: ["25","查看排名", "排名"]
    description: "显示战力、财富、恶人、降妖排行"
    rateLimit:
      seconds: 5
      maxCount: 1
      message: "排名查看过于频繁 🏆"

  # 查看怪物/玩家
  viewTarget:
    aliases: ["24","查看"]
    description: "查看指定目标的介绍和属性"
    rateLimit:
      seconds: 1
      maxCount: 5
      message: "目标查看过于频繁 👁️"

  # ==================== 移动类命令 ====================
  # 前往地点
  moveTo:
    aliases: ["3","前往", "去","q"]
    description: "移动到指定地点、坐标或目标编号"
    rateLimit:
      seconds: 5
      maxCount: 1
      message: "移动需要时间，请稍等片刻 🏃‍♂️"

  # ==================== 战斗类命令 ====================
  # 普通攻击
  attack:
    aliases: ["1","攻击", "打", "杀", "砍","干","kill","fuck","操","s"]
    description: "对目标发起普通攻击"
    rateLimit:
      seconds: 3
      maxCount: 1
      message: "少侠功夫了得，手脚太快了，请停下来休息下喝杯龙井茶 🍵"

  # 技能攻击
  castSkill:
    aliases: ["11","施", "放","技能"]
    description: "对目标使用技能"
    rateLimit:
      seconds: 3
      maxCount: 1
      message: "技能施放需要时间恢复 ⚡"
  # 逃跑
  run:
    aliases: ["12","跑", "逃跑","逃离","逃","p"]
    description: "逃跑，脱离战斗"
    rateLimit:
      seconds: 15
      maxCount: 1
      message: "你已经夹着尾巴离战斗 ⚡"
  # 复活
  revive:
    aliases: ["99","复活"]
    description: "死亡后复活，复活后回到武林主城"
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "复活需要时间，请耐心等待 💀"

  # ==================== 采集类命令 ====================
  # 钓鱼
  fishing:
    aliases: ["4","钓鱼", "鱼","抛竿"]
    description: "在可钓鱼的地图进行钓鱼"
    rateLimit:
      seconds: 2
      maxCount: 1
      message: "钓鱼需要耐心，请稍作休息 🎣"

  # 采桑
  mulberry:
    aliases: ["41","采桑","桑"]
    description: "在可采桑的地图进行采桑"
    rateLimit:
      seconds: 2
      maxCount: 1
      message: "采桑需要耐心，请稍作休息 🌿"

  # 采茶
  teaPicking:
    aliases: ["42","采茶","茶"]
    description: "在可采茶的地图进行采茶"
    rateLimit:
      seconds: 2
      maxCount: 1
      message: "采茶需要耐心，请稍作休息 🍃"

  # 挖矿
  mining:
    aliases: ["43","挖矿","挖","矿"]
    description: "在可挖矿的地图进行挖矿"
    rateLimit:
      seconds: 2
      maxCount: 1
      message: "挖矿需要耐心，请稍作休息 ⛏️"

  # ==================== 挂机类命令 ====================
  # 挂机挖矿
  idleMining:
    aliases: ["430","挂机挖矿"]
    description: "挂机挖矿，每10分钟计算一次结果"
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "挂机设置过于频繁 ⛏️"

  # 挂机钓鱼
  idleFishing:
    aliases: ["410","挂机钓鱼"]
    description: "挂机钓鱼，每10分钟计算一次结果"
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "挂机设置过于频繁 🎣"

  # 挂机采桑
  idleMulberry:
    aliases: ["420","挂机采桑"]
    description: "挂机采桑，每10分钟计算一次结果"
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "挂机设置过于频繁 🌿"

  # 挂机打怪
  idleCombat:
    aliases: ["400","挂机打怪"]
    description: "挂机自动打怪，技能可选"
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "挂机设置过于频繁 ⚔️"

  idleExit:
    aliases: ["退出挂机","结束挂机","不挂机"]
    description: "退出挂机玩法，由人工手动操作"
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "不要频繁挂机和退出挂机 ⚔️"

  # ==================== 人物操作命令 ====================
  # 创建角色
  createCharacter:
    aliases: ["建号", "创建号", "创建账号","加入","加入江湖"]
    description: "创建对应职业名称的角色"
    rateLimit:
      seconds: 60
      maxCount: 1
      message: "角色创建过于频繁 👤"

  # 使用物品
  useItem:
    aliases: ["10","用","服","吃","使用"]
    description: "使用消耗品，比如药品"
    rateLimit:
      seconds: 2
      maxCount: 3
      message: "使用物品需要时间消化 💊"

  # 装备物品
  equipItem:
    aliases: ["12","穿上","带上","穿","带","戴","代"]
    description: "穿上装备"
    rateLimit:
      seconds: 2
      maxCount: 2
      message: "装备更换需要时间 🎽"

  # 存取物品
  storeItem:
    aliases: ["13","存","放","存入","放入","寄存"]
    description: "将物品放入仓库"
    rateLimit:
      seconds: 1
      maxCount: 5
      message: "仓库操作过于频繁 📦"

  retrieveItem:
    aliases: ["14","取","拿","取出","拿出"]
    description: "从仓库取出物品"
    rateLimit:
      seconds: 1
      maxCount: 5
      message: "仓库操作过于频繁 📦"

  # 兑现银两为金币
  exchangeCurrency:
    aliases: ["15","兑现","兑换"]
    description: "按配置比例兑换银两为金币"
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "兑现操作过于频繁 💱"

  # ==================== 交易类命令 ====================
  # 购买 - 从市场/NPC购买物品
  buy:
    aliases: ["60","买","购买","buy"]
    description: "从市场/NPC购买物品"
    rateLimit:
      seconds: 2
      maxCount: 3
      message: "🛒 购买操作过于频繁，稍后再试！"

  # 出售 - 向市场/NPC出售物品
  sell:
    aliases: ["61","卖","出售","sell"]
    description: "向市场/NPC出售物品"
    rateLimit:
      seconds: 2
      maxCount: 3
      message: "💰 出售操作过于频繁，稍后再试！"

  # 询问NPC - 向NPC询问信息
  askNpc:
    aliases: ["6","询问","咨询","问"]
    description: "向NPC询问信息，NPC自我介绍"
    rateLimit:
      seconds: 3
      maxCount: 2
      message: "💬 询问过于频繁，让NPC休息一下！"

  # 取出市场售卖
  cancelSell:
    aliases: ["取消","下架","撤销"]
    description: "取消在市场上的售卖物品"
    rateLimit:
      seconds: 3
      maxCount: 2
      message: "📤 下架操作过于频繁！"

  # 查看市场
  viewMarket:
    aliases: ["市场","交易市场","查看市场"]
    description: "查看交易市场中的物品"
    rateLimit:
      seconds: 1
      maxCount: 5
      message: "🏪 查看市场过于频繁！"

  # ==================== 装备操作命令 ====================
  # 装备升品
  upgradeEquipment:
    aliases: ["升品","升级","升"]
    description: "对身上对应的道具进行升品"
    rateLimit:
      seconds: 5
      maxCount: 2
      message: "升品操作需要时间 ⬆️"

  # 玉佩浣灵
  refineJade:
    aliases: ["浣灵","洗","洗练","洗炼"]
    description: "只对身上的玉佩进行浣灵"
    rateLimit:
      seconds: 5
      maxCount: 2
      message: "浣灵操作需要时间 ✨"


  # ==================== 帮助类命令 ====================
  # 游戏帮助
  help:
    aliases: ["帮助","帮助","help","游戏说明","说明","介绍","游戏介绍","怎么玩","什么游戏","西子江湖"]
    description: "显示游戏帮助信息"
    rateLimit:
      seconds: 5
      maxCount: 1
      message: "帮助信息查看过于频繁 📖"

  # ==================== 游戏模式命令 ====================
  # 进入游戏模式
  enterGame:
    aliases: ["游戏模式", "游戏模式","进入江湖","进入游戏","开始游戏"]
    description: "进入游戏状态"
    rateLimit:
      seconds: 5
      maxCount: 1
      message: "游戏模式切换过于频繁 🎮"

  # 退出游戏模式
  exitGame:
    aliases: ["退出游戏","结束游戏"]
    description: "退出游戏状态"
    rateLimit:
      seconds: 5
      maxCount: 1
      message: "游戏模式切换过于频繁 🚪"

  # ==================== 管理员命令 ====================
  # 创建分区
  adminCreateArea:
    aliases: [ "创建分区", "建分区", "ca" ]
    description: "创建分区"
    adminOnly: true
    rateLimit:
      seconds: 10
      maxCount: 1
      message: "配置重载操作过于频繁 ⚙️"

  # 重载配置
  adminloadArea:
    aliases: ["加载分区", "读取分区"]
    description: "重新加载分区群聊信息（管理员专用）"
    adminOnly: true
    rateLimit:
      seconds: 30
      maxCount: 1
      message: "配置重载操作过于频繁 ⚙️"

  # 重载配置
  adminReload:
    aliases: ["重载配置", "reload"]
    description: "重新加载配置文件（管理员专用）"
    adminOnly: true
    rateLimit:
      seconds: 30
      maxCount: 1
      message: "配置重载操作过于频繁 ⚙️"

  # 踢出玩家
  adminKick:
    aliases: ["踢出", "kick"]
    description: "踢出指定玩家（管理员专用）"
    adminOnly: true
    rateLimit:
      seconds: 5
      maxCount: 3
      message: "踢出操作过于频繁 🚫"

  # 封禁玩家
  adminBan:
    aliases: ["封禁", "ban"]
    description: "封禁指定玩家（管理员专用）"
    adminOnly: true
    rateLimit:
      seconds: 10
      maxCount: 2
      message: "封禁操作过于频繁 🔒"

  # 系统公告
  adminAnnounce:
    aliases: ["公告", "announce"]
    description: "发送系统公告（管理员专用）"
    adminOnly: true
    rateLimit:
      seconds: 60
      maxCount: 1
      message: "公告发送过于频繁 📢"

# 全局限流配置
globalLimits:
  # 每用户每分钟最大命令数
  maxCommandsPerMinute: 60
  # 每用户每小时最大命令数
  maxCommandsPerHour: 1000
  # 超出限制的提示消息
  limitExceededMessage: "您的操作过于频繁，请稍后再试 ⏰"
