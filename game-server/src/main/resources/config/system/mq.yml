# 消息队列配置
mq:
  # 是否启用消息队列
  enabled: false
  
  # 队列配置
  queues:
    # 战斗消息队列
    battle:
      name: "battle_queue"
      maxSize: 1000
      timeout: 5000
    
    # 系统消息队列
    system:
      name: "system_queue"
      maxSize: 500
      timeout: 3000
    
    # 聊天消息队列
    chat:
      name: "chat_queue"
      maxSize: 2000
      timeout: 1000
  
  # 消费者配置
  consumers:
    # 并发消费者数量
    concurrent: 2
    # 最大消费者数量
    maxConcurrent: 5
    # 消费超时时间(毫秒)
    timeout: 10000
