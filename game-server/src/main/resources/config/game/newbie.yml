# 新手初始化配置
# 基于需求文档4.2新手初始化配置

# 保护等级配置
protection:
  endLevel: 10  # 保护期结束等级
  attributeBonus:
    # 保护期属性增加
    defenseW: 100  # 物防增加100
    defenseM: 100  # 法防增加100
    defenseF: 100  # 佛防增加100
    attackW: 10    # 物攻增加10
    attackM: 10    # 法攻增加10
    attackF: 10    # 佛攻增加10

# 初始化资源
initialResources:
  gold: 1000  # 初始化金币数量
  silver: 0   # 初始化银两数量

# 初始化职业武器(基于数据库item.sql)
initialWeapons:
  1:  # 剑客
    itemNo: "EQ_YOUHU_WQ_01"  # 使用数据库道具编号
    position: 1  # 装备位置
    degree: 0    # 品级
  2:  # 仙师
    itemNo: "EQ_HUGUANG_WQ_01"  # 使用数据库道具编号
    position: 1
    degree: 0
  3:  # 圣僧
    itemNo: "EQ_YINYUE_WQ_01"  # 使用数据库道具编号
    position: 1
    degree: 0

# 新手任务配置
tutorialQuests:
  # 必做新手任务序列
  mandatorySequence:
    - questId: "TUTORIAL_001"  # 角色创建引导
      autoAccept: true
      autoComplete: false
    - questId: "TUTORIAL_002"  # 基础操作教学
      autoAccept: true
      autoComplete: false
    - questId: "TUTORIAL_003"  # 第一次战斗
      autoAccept: true
      autoComplete: false
    - questId: "TUTORIAL_004"  # 装备系统介绍
      autoAccept: true
      autoComplete: false
    - questId: "TUTORIAL_005"  # 地图移动教学
      autoAccept: true
      autoComplete: false

  # 新手任务奖励
  tutorialRewards:
    TUTORIAL_001:
      exp: 100
      silver: 100
      items: []
    TUTORIAL_002:
      exp: 200
      silver: 200
      items: [{"item_no": "ITEM_EXP", "count": 100}]  # 经验
    TUTORIAL_003:
      exp: 300
      silver: 300
      items: [{"item_no": "ITEM_GOLD", "count": 5000}]  # 金币
    TUTORIAL_004:
      exp: 400
      silver: 400
      items: [{"item_no": "ITEM_BOX_01", "count": 1}]  # 初级盒子
    TUTORIAL_005:
      exp: 500
      silver: 500
      items: [{"item_no": "ITEM_BOX_02", "count": 1}]  # 高级盒子

# 新手引导配置
guidance:
  # 强制引导步骤
  forcedSteps:
    - step: "character_creation"
      description: "角色创建引导"
      skippable: false
    - step: "basic_ui"
      description: "界面介绍"
      skippable: true
    - step: "first_battle"
      description: "第一次战斗"
      skippable: false
    - step: "equipment_tutorial"
      description: "装备教学"
      skippable: true
    - step: "map_navigation"
      description: "地图导航"
      skippable: true

  # 提示消息配置
  hintMessages:
    levelUp: "恭喜升级！记得查看属性变化哦 🎉"
    firstEquipment: "获得了新装备！快去装备界面穿上吧 ⚔️"
    firstSkill: "学会了新技能！在战斗中试试看吧 ✨"
    protectionEnd: "新手保护期即将结束，要小心其他玩家哦 ⚠️"
