# 装备升品配置
# 基于需求文档3.4装备升品系统
# 装备升品配置
equipment:
  # 1-3品升品配置
  level13:
    materials:
      ITEM_STONE: "level * 2"  # 所需雨花石数量公式
      ITEM_SILVER: "level * 100"      # 所需银两公式
    successRate: 0.8            # 成功率
    attributeIncrease: 0.1      # 属性提升比例

  # 4-6品升品配置
  level46:
    materials:
      ITEM_STONE: "level * 3"  # 雨花石
      ITEM_METEOR: "level * 1"  # 天外陨石
      ITEM_SILVER: "level * 200"      # 银两
    successRate: 0.6
    attributeIncrease: 0.15

  # 7-9品升品配置
  level79:
    materials:
      ITEM_STONE: "level * 5"  # 雨花石
      ITEM_METEOR: "level * 2"  # 天外陨石
      ITEM_SILVER: "level * 500"      # 银两
    successRate: 0.4
    attributeIncrease: 0.2

  # 10品以上升品配置
  level10Plus:
    materials:
      ITEM_STONE: "level * 10"  # 雨花石
      ITEM_METEOR: "level * 5"   # 天外陨石
      ITEM_GOLD: "level * 10"  # 金币
    successRate: 0.2
    attributeIncrease: 0.25

# 玉佩浣灵配置
jadeRefine:
  # 1-3品浣灵配置
  level13:
    materials:
      ITEM_MONEY_GOLD: "level * level * 1000"  # 金币1000*(品数*品数)
      ITEM_MAT_HUANLING: "level * 2"  # 日月同辉*(品数*2)
    successRate: 1.0  # 100%成功率
    attributeRange:
      min: 1.001  # 最小属性倍率
      max: 1.01   # 最大属性倍率

  # 4-6品浣灵配置
  level46:
    materials:
      ITEM_MONEY_GOLD: "level * level * 10000"  # 金币10000*(品数*品数)
      ITEM_MAT_HUANLING: "level * 3"  # 日月同辉*(品数*3)
    successRate: 1.0
    attributeRange:
      min: 1.001
      max: 1.05

  # 7-9品浣灵配置
  level79:
    materials:
      ITEM_MONEY_SILVER: "level * 5"  # 银两5*(品数)
      ITEM_MAT_HUANLING: "level * 4"  # 日月同辉*(品数*4)
    successRate: 1.0
    attributeRange:
      min: 1.001
      max: 1.2

  # 10品以上浣灵配置
  level10Plus:
    materials:
      ITEM_MONEY_SILVER: "level * 2 * 10"  # 银两10*(品数*2)
      ITEM_MAT_HUANLING: "level * 5"  # 日月同辉*(品数*5)
    successRate: 1.0
    attributeRange:
      min: 1.001
      max: 1.3
