# 等级称号升级配置
# 基于需求文档4.3角色升级属性配置
# 1级角色基础属性
baseAttributes:
  swordsman:  # 剑客
    hp: 20
    mp: 10
    attackM: 0
    attackW: 10
    attackF: 0
    defenseW: 10
    defenseM: 0
    defenseF: 0
    baseExp: 100
  mage:  # 仙师
    hp: 10
    mp: 20
    attackM: 10
    attackW: 0
    attackF: 0
    defenseW: 0
    defenseM: 10
    defenseF: 0
    baseExp: 100
  monk:  # 圣僧
    hp: 15
    mp: 15
    attackM: 0
    attackW: 0
    attackF: 10
    defenseW: 0
    defenseM: 0
    defenseF: 10
    baseExp: 100

# 升级属性配置
levelRanges:
  - range: [1, 10]
    hpMpIncrease: 0.20    # 血量法气每级增加20%
    otherIncrease: 0.08    # 其他属性每级增加8%
  - range: [11, 30]
    hpMpIncrease: 0.15    # 血量法气每级增加15%
    otherIncrease: 0.04    # 其他属性每级增加4%
  - range: [31, 60]
    hpMpIncrease: 0.10    # 血量法气每级增加10%
    otherIncrease: 0.02    # 其他属性每级增加2%
  - range: [61, 100]
    hpMpIncrease: 0.05    # 血量法气每级增加5%
    otherIncrease: 0.015   # 其他属性每级增加1.5%
  - range: [101, 999]
    hpMpIncrease: 0.03    # 血量法气每级增加3%
    otherIncrease: 0.01    # 其他属性每级增加1%
# 升级经验计算配置
levelExp:
  # 经验计算公式：下级经验 = (上级经验 * 1.03) + (当前等级 * 100)
  formula: "(prev_exp * 1.02) + (current_level * 100)"
  baseExp: 100  # 1级基础经验
  multiplier: 1.02  # 经验增长倍数
  levelFactor: 100  # 等级影响因子

# 等级称号配置（基于需求文档）
titles:
  - range: [1, 9]
    title: "初出茅庐"
    description: "刚刚踏入江湖的新人"
  - range: [10, 29]
    title: "江湖新手"
    description: "对江湖有了初步了解"
  - range: [30, 59]
    title: "江湖有名"
    description: "在江湖中有了一定声望"
  - range: [60, 99]
    title: "武林高手"
    description: "武功高强的江湖好手"
  - range: [100, 999]
    title: "西湖传说"
    description: "西湖江湖的不朽传说"
