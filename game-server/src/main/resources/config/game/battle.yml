# 战斗系统配置
# 基于需求文档2.2角色系统和2.8战斗系统
classDescriptions:
  swordsman:  # 剑客
    name: "剑客"
    description: "物理输出型职业，物理攻击高、物理防御高"
    primaryAttribute: "physical"
    specialty: "近战物理攻击"
  mage:  # 仙师
    name: "仙师"
    description: "法术输出型职业，法术攻击高、法术防御高"
    primaryAttribute: "magical"
    specialty: "远程法术攻击"
  monk:  # 圣僧
    name: "圣僧"
    description: "佛法输出型职业，佛法攻击高、佛法防御高"
    primaryAttribute: "buddhist"
    specialty: "佛法攻击与治疗"

# 职业克制关系
classRestraint:
  swordsman:  # 剑客
    restrains: "monk"      # 克制圣僧
    restrainedBy: "mage"   # 被仙师克制
    damageBonus: 1.5       # 克制时伤害加成
    damageReduction: 0.7   # 被克制时伤害减免
  mage:  # 仙师
    restrains: "swordsman" # 克制剑客
    restrainedBy: "monk"   # 被圣僧克制
    damageBonus: 1.5
    damageReduction: 0.7
  monk:  # 圣僧
    restrains: "mage"      # 克制仙师
    restrainedBy: "swordsman" # 被剑客克制
    damageBonus: 1.5
    damageReduction: 0.7

# 经验计算配置
expCalculation:
  baseDamage: 10  # 基础伤害
  attackFactor: 0.5   # 攻击力影响因子
  defenseFactor: 0.5 # 防御力影响因子
  levelFactor: 0.02   # 等级差影响因子
  criticalRate: 0.08  # 基础暴击率5%
  criticalDamage: 2.0 # 暴击伤害倍数
  maxLevelDifference: 10  # 最大等级差
  minExpRate: 0.1     # 最小经验倍率

  damageVariance: 0.1  # 10%的伤害波动
