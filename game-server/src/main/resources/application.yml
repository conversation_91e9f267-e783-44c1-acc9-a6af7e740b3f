server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: xiziworld-game-server
  profiles:
    active: dev
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: xiziworld
    password: xiziworld
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # Redis配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: 
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.xiziworld.gameserver.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true                                    # 启用MyBatis二级缓存
    local-cache-scope: statement                           # 本地缓存作用域
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      field-strategy: NOT_NULL
      db-type: MYSQL

logging:
  level:
    root: info
    com.xiziworld.gameserver: debug
  file:
    path: logs/${spring.application.name} 