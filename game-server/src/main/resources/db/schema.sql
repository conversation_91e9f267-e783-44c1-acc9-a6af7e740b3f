-- 创建数据库
CREATE DATABASE IF NOT EXISTS xiziworld DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE xiziworld;

-- 区服表
CREATE TABLE IF NOT EXISTS `area` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `open_id` varchar(32) NOT NULL COMMENT '外部区服识别ID，如微信群聊ID',
    `area_name` varchar(32) NOT NULL COMMENT '区服名称(区服别名)',
    `app_id` varchar(32) NOT NULL COMMENT '应用标识，0-微信',
    `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '区服状态(0:正常 1:维护 2:关闭)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_open_id` (`open_id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='区服表';

-- 道具表
CREATE TABLE IF NOT EXISTS `item` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `item_no` varchar(32) NOT NULL COMMENT '道具编号',
    `type` tinyint(4) NOT NULL COMMENT '类型(0:装备 1:药品 2:技能书 3:材料 9:神奇盒子)',
    `sub_type` tinyint(4) NOT NULL COMMENT '子类型(装备部位/药品类型/材料类型/盒子等级等)',
    `name` varchar(32) NOT NULL COMMENT '名称',
    `description` varchar(255) NOT NULL COMMENT '描述',
    `attributes` json DEFAULT NULL COMMENT '属性JSON，装备物品见GameAttributeConstant定义的key',
    `role_limit` tinyint(4) DEFAULT NULL COMMENT '职业限制',
    `level_limit` int(11) DEFAULT NULL COMMENT '等级限制',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_item_no` (`item_no`),
    KEY `idx_type` (`type`),
    KEY `idx_sub_type` (`sub_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='道具表';

-- 技能表
CREATE TABLE IF NOT EXISTS `skill` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `skill_no` varchar(32) NOT NULL COMMENT '技能编号',
    `name` varchar(32) NOT NULL COMMENT '技能名称',
    `type` tinyint(4) NOT NULL COMMENT '技能类型（0-物理攻击，1-魔法攻击，2-佛法攻击，3-物理防御，4-魔法防御，5-佛法防御,6-反伤）',
    `description` varchar(255) NOT NULL COMMENT '描述',
    `power` int(11) NOT NULL COMMENT '威力',
    `cooldown` int(11) NOT NULL COMMENT '冷却时间',
    `cost` int(11) NOT NULL COMMENT '消耗',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_skill_no` (`skill_no`),
    KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能表';

-- 怪物表
CREATE TABLE IF NOT EXISTS `monster` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `monster_no` varchar(32) NOT NULL COMMENT '怪物编号',
    `name` varchar(32) NOT NULL COMMENT '怪物名称',
    `type` varchar(32) NOT NULL COMMENT '怪物类型(normal/elite/boss)',
    `level` int(11) NOT NULL COMMENT '等级',
    `hp` int(11) NOT NULL COMMENT '血量',
    `attack_m` int(11) NOT NULL COMMENT '魔法攻击',
    `attack_f` int(11) NOT NULL COMMENT '佛法攻击',
    `attack_w` int(11) NOT NULL COMMENT '物理攻击',
    `defense_m` int(11) NOT NULL COMMENT '魔法防御',
    `defense_f` int(11) NOT NULL COMMENT '佛法防御',
    `defense_w` int(11) NOT NULL COMMENT '物理防御',
    `description` varchar(255) NOT NULL COMMENT '描述',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_monster_no` (`monster_no`),
    KEY `idx_type` (`type`),
    KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='怪物表';

-- 用户角色表
CREATE TABLE IF NOT EXISTS `user_character` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` varchar(32) NOT NULL COMMENT '用户ID',
    `app_id` tinyint(4) NOT NULL DEFAULT 0 COMMENT '应用ID(0-微信)',
    `open_id` varchar(64) NOT NULL COMMENT '外部区服识别ID，如微信群聊ID',
    `area_id` int(11) NOT NULL DEFAULT 1 COMMENT '区服ID',
    `name` varchar(32) NOT NULL COMMENT '角色名',
    `type` tinyint(4) NOT NULL COMMENT '职业类型(1:剑客 2:仙师 3:圣僧)',
    `level` int(11) NOT NULL DEFAULT 1 COMMENT '等级',
    `exp` bigint(20) NOT NULL DEFAULT 0 COMMENT '当前经验值',
    `max_exp` bigint(20) NOT NULL DEFAULT 0 COMMENT '升级所需经验',
    `hp` int(11) NOT NULL COMMENT '当前血量',
    `mp` int(11) NOT NULL COMMENT '当前法力值',
    `max_hp` int(11) NOT NULL COMMENT '最大血量',
    `max_mp` int(11) NOT NULL COMMENT '最大法力值',
    `skills` json DEFAULT NULL COMMENT '习得的技能列表，存放技能编号',
    `attributes` json DEFAULT NULL COMMENT '属性JSON，见GameAttributeConstant定义的key属性',
    `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态(0:正常 1:死亡)',
    `game_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '游戏状态(0:离线 1:正常在线 2:账号被禁)',
    `position` json NOT NULL COMMENT '位置信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_app_area` (`user_id`,`app_id`,`area_id`),
    KEY `idx_open_id` (`open_id`),
    KEY `idx_type` (`type`),
    KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表';

-- 用户资产表
CREATE TABLE IF NOT EXISTS `user_asset` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `character_id` int(11) NOT NULL COMMENT '角色ID',
    `item_no` varchar(32) NOT NULL COMMENT '道具编号',
    `count` int(11) NOT NULL DEFAULT 1 COMMENT '数量',
    `attributes` json DEFAULT NULL COMMENT '属性JSON，装备属性见GameAttributeConstant定义的key属性',
    `position` tinyint(4) NOT NULL COMMENT '位置(1:身上 2:背包 3:仓库)',
    `bag_slot` tinyint(4) DEFAULT NULL COMMENT '背包格子序号(1-50，仅position=2时有效)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_character_id` (`character_id`),
    KEY `idx_item` (`item_no`),
    KEY `idx_position` (`position`),
    KEY `idx_bag_slot` (`character_id`, `position`, `bag_slot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资产表';

-- 排行榜表
CREATE TABLE IF NOT EXISTS `rank` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `type` tinyint(4) NOT NULL COMMENT '排行类型(1:等级 2:战力 3:财富 4：击杀怪物数量 5：击杀玩家数)',
    `character_id` int(11) NOT NULL COMMENT '角色ID',
    `character_name` varchar(32) NOT NULL COMMENT '角色名',
    `area_id` int(11) NOT NULL DEFAULT 1 COMMENT '区服ID',
    `amount` bigint(20) NOT NULL COMMENT '数值',
    `rank` int(11) NOT NULL COMMENT '排名',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_character_id` (`type`,`character_id`),
    KEY `idx_area_id` (`area_id`),
    KEY `idx_amount` (`amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜表';

-- 充值记录表
CREATE TABLE IF NOT EXISTS `recharge_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `character_id` int(11) NOT NULL COMMENT '角色ID',
    `amount` decimal(10,2) NOT NULL COMMENT '充值金额',
    `silver` int(11) NOT NULL COMMENT '银两数量',
    `status` tinyint(4) NOT NULL COMMENT '状态(0:处理中 1:成功 2:失败)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_character_id` (`character_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- 任务表
CREATE TABLE IF NOT EXISTS `quest` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(32) NOT NULL COMMENT '任务名称',
    `type` tinyint(4) NOT NULL COMMENT '任务类型(1:主线 2:支线 3:日常)',
    `description` varchar(255) NOT NULL COMMENT '描述',
    `rewards` json DEFAULT NULL COMMENT '奖励JSON',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- 任务进度表
CREATE TABLE IF NOT EXISTS `quest_progress` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `character_id` int(11) NOT NULL COMMENT '角色ID',
    `quest_id` bigint(20) NOT NULL COMMENT '任务ID',
    `progress` int(11) NOT NULL DEFAULT 0 COMMENT '进度',
    `status` tinyint(4) NOT NULL COMMENT '状态(0:进行中 1:已完成 2:已失败)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_character_id` (`character_id`),
    KEY `idx_quest_id` (`quest_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务进度表'; 