-- 任务表初始化SQL

-- 主线任务
INSERT INTO quest (name, type, description, rewards) VALUES
('初入西湖', 1, '来到西湖，开始你的江湖之旅', '{"exp":100,"gold":100,"items":[{"item_no":"ITEM_DRUG_HP_01","count":5}]}'),
('拜访苏小小', 1, '前往孤山，了解苏小小的故事', '{"exp":200,"gold":200,"items":[{"item_no":"ITEM_DRUG_MP_01","count":5}]}'),
('雷峰塔历练', 1, '前往雷峰塔，探索白蛇传的传说', '{"exp":500,"gold":500,"items":[{"item_no":"ITEM_BOX_01","count":1}]}');

-- 支线任务
INSERT INTO quest (name, type, description, rewards) VALUES
('龙井问茶', 2, '为西湖龙井茶园寻找优质茶叶', '{"exp":300,"gold":300,"items":[{"item_no":"ITEM_MAT_PLANT_02","count":10}]}'),
('断桥寻缘', 2, '帮助断桥边的游客寻找白娘子丢失的珠钗', '{"exp":300,"gold":300,"items":[{"item_no":"ITEM_MAT_01","count":5}]}');

-- 日常任务
INSERT INTO quest (name, type, description, rewards) VALUES
('西湖捕鱼', 3, '在西湖中捕捉鱼类', '{"exp":100,"gold":100,"items":[{"item_no":"ITEM_MAT_FISH_01","count":3}]}'),
('梅家坞采桑', 3, '在梅家坞后山采集桑叶', '{"exp":100,"gold":100,"items":[{"item_no":"ITEM_DRUG_HP_01","count":3}]}'),
('除妖卫道', 3, '铲除西湖周边的妖魔', '{"exp":100,"gold":100,"items":[{"item_no":"ITEM_MAT_03","count":1}]}'); 