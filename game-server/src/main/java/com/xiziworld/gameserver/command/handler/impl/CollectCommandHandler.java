package com.xiziworld.gameserver.command.handler.impl;

import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import org.springframework.stereotype.Component;

/**
 * 采集命令处理器，处理采集相关命令，包括：
 *   CommandConfig.COMMAND_FISHING
 *   CommandConfig.COMMAND_MULBERRY
 *   CommandConfig.COMMAND_TEA_PICKING
 *   CommandConfig.COMMAND_MINING
 * 
 * <AUTHOR>
 */
@Component
public class CollectCommandHandler extends AbstractCommandHandler {


    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_COLLECT;
    }

    @Override
    protected void validate(Command command) {
        // 检查角色是否存在
        checkCharacterExists(command);

        // 检查角色是否死亡
        checkCharacterDead(command);
    }

    @Override
    protected String doHandle(Command command) {
        String cmd = command.getCommandName();
        UserCharacter character = getCharacter(command);
        switch (cmd) {
            case CommandConfig.COMMAND_FISHING:
                return collectManager.fishing(character.getId());
            case CommandConfig.COMMAND_MULBERRY:
                return collectManager.mulberry(character.getId());
            case CommandConfig.COMMAND_TEA_PICKING:
                return collectManager.tea(character.getId());
            case CommandConfig.COMMAND_MINING:
                return collectManager.mining(character.getId());
            default:
                return "未知的采集命令: " + cmd;
        }
    }
}
