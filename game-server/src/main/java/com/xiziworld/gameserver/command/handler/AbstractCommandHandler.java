package com.xiziworld.gameserver.command.handler;

import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.*;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.mapper.SkillMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 抽象命令处理器
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCommandHandler implements CommandHandler {

    @Autowired
    protected ConfigManager configManager;

    @Autowired
    protected PlayerManager playerManager;

    @Autowired
    protected BattleManager battleManager;

    @Autowired
    protected AssetManager assetManager;

    @Autowired
    protected TradeManager tradeManager;

    @Autowired
    protected SkillMapper skillMapper;

    @Autowired
    protected MapManager mapManager;

    @Autowired
    protected CollectManager collectManager;


    @Override
    public String handle(Command command) {
        try {
            // 验证命令
            validate(command);

            // 执行命令
            return doHandle(command);
        } catch (GameException e) {
            log.error("命令执行失败: {}", e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("命令执行异常", e);
            return "系统错误";
        }
    }

    /**
     * 具体的命令处理逻辑，由子类实现
     */
    protected abstract String doHandle(Command command);

    @Override
    public boolean support(Command command) {
        return command.getType() == getCommandType();
    }
    /**
     * 验证命令
     */
    protected abstract void validate(Command command);

    /**
     * 查询玩家信息
     */

    protected UserCharacter getCharacter(Command command) {
        return playerManager.getCharacterByUser(
                command.getUserId(),
                command.getAppId(),
                command.getOpenId(),
                command.getAreaId());
    }

    /**
     * 检查角色是否存在，是否正常
     */
    protected void checkCharacterExists(Command command) {
         UserCharacter userCharacter =getCharacter(command);
         if (userCharacter == null) {
             throw new GameException(GameException.CHARACTER_NOT_EXISTS);
         }
         if (userCharacter.getGameStatus()==UserCharacter.GAME_STATUS_BANNED){
             throw new GameException(GameException.CHARACTER_BANNED);
         }
    }

    /**
     * 检查角色是否死亡
     */
    protected void checkCharacterDead(Command command) {
        UserCharacter userCharacter = playerManager.getCharacterByUser(command.getUserId(), command.getAppId(), command.getOpenId(), command.getAreaId());
        if (userCharacter != null && userCharacter.getStatus() == UserCharacter.STATUS_DEAD) {
            throw new GameException(GameException.CHARACTER_DEAD);
        }
    }

    /**
     * 检查目标是否存在
     */
    protected void checkTargetExists(Command command) {
        if (command.getTarget() == null) {
            throw new GameException(GameException.TARGET_REQUIRED);
        }
    }

    /**
     * 检查@目标是否存在
     */
    protected void checkAtTargetExists(Command command) {
        if (command.getAtUsers() == null || command.getAtUsers().isEmpty()) {
            throw new GameException(GameException.AT_TARGET_REQUIRED);
        }
    }
} 