package com.xiziworld.gameserver.command.handler;

import com.xiziworld.gameserver.command.Command;

/**
 * 命令处理器接口
 *
 * <AUTHOR>
 */
public interface CommandHandler {

    /**
     * 是否支持该命令
     *
     * @param command 命令
     * @return 是否支持
     */
    boolean support(Command command);
    /**
     * 处理命令
     *
     * @param command 命令
     * @return 处理结果
     */
    String handle(Command command);

    int getCommandType();
} 