package com.xiziworld.gameserver.command;

import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import com.xiziworld.gameserver.domain.manager.player.UserCharacterCacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 命令解析器
 *
 * <AUTHOR>
 */
@Component
public class CommandParser {

    @Autowired
    private ConfigManager configManager;

    @Autowired
    private UserCharacterCacheManager userCharacterCacheManager;

    /**
     * @人物正则表达式
     */
    private static final Pattern AT_PATTERN = Pattern.compile("@([^ ]+)");

    /**
     * 解析命令
     */
    public Command parse(Command cmd) {
        if (StringUtils.isBlank(cmd.getRawText())) {
            return null;
        }

        String text = cmd.getRawText();
        // 判断是否强命令
        if (text.startsWith("!")) {
            cmd.setStrong(true);
            text = text.substring(1);
        }

        // 提取@人物
        if(cmd.getAtUsers()==null || cmd.getAtUsers().isEmpty()) {
            List<String> atUsers = new ArrayList<>();
            Matcher matcher = AT_PATTERN.matcher(text);
            String atName = null;
            while (matcher.find()) {
                atName = matcher.group(1);
                text = text.replace(matcher.group(), "");
                UserCharacter character = userCharacterCacheManager.getCharacterByAreaAndName(cmd.getAreaId(), atName);
                if (character != null) {
                    atUsers.add(character.getUserId());
                }
            }
            cmd.setAtUsers(atUsers);
        }

        // 去除多余空格
        text = text.trim().replaceAll("\\s+", " ");
        if (StringUtils.isBlank(text)) {
            return cmd;
        }

        // 分割命令
        String[] parts = text.split(" ");
        if (parts.length == 0) {
            return cmd;
        }

        // 解析操作词
        String inputAction = parts[0];
        cmd.setAction(inputAction);

        // 解析标准命令名称（通过别名映射）
        String commandName = resolveCommandName(inputAction);
        cmd.setCommandName(commandName);

        // 解析命令类型
        cmd.setType(CommandConfig.parseCommandType(commandName));

        // 解析操作对象和参数
        if (parts.length > 1) {
            cmd.setTarget(parts[1]);
            if (parts.length > 2) {
                List<String> params = new ArrayList<>(Arrays.asList(parts).subList(2, parts.length));
                cmd.setArguments(params);
            }
        //如查用户输入的操作命令和对象没有用空格分融，则根据命令关键词去掉命令部分，余下的是参数
        }else{
           cmd.setTarget(parseTarget(inputAction,commandName));
        }

        return cmd;
    }

    /**
     * 获取命令后面带的参数（如果用户没有使用空格分融命令和目标对象）
     * @param inputAction
     * @param commandName
     * @return 去除命令字符后的余下参数
     */
    private String parseTarget(String inputAction, String commandName) {
        if (StringUtils.isBlank(inputAction) || commandName==null) {return null;}
        CommandConfig commandConfig = configManager.getCommandConfig();
        CommandConfig.CommandDetail detail = commandConfig.getCommands().get(commandName);
        if (detail == null)return null;
        // 按别名长度从长到短排序，优先匹配最长的别名
        List<String> sortedAliases = detail.getAliases().stream()
                .sorted((a, b) -> Integer.compare(b.length(), a.length()))
                .collect(Collectors.toList());

        for (String alias : sortedAliases) {
            if (inputAction.startsWith(alias)) {
                String target = inputAction.substring(alias.length()).trim();
                return target.isEmpty() ? null : target;
            }
        }
        return null;
    }
    /**
     * 通过别名解析标准命令名称
     * @param inputAction 用户输入的命令词（可能是别名）
     * @return 标准命令名称，如果未找到返回原输入
     */
    private String resolveCommandName(String inputAction) {
        if (StringUtils.isBlank(inputAction)) {
            return inputAction;
        }

        CommandConfig commandConfig = configManager.getCommandConfig();
        if (commandConfig == null || commandConfig.getCommands() == null) {
            return inputAction;
        }

        String lowerAction = inputAction.toLowerCase();

        // 遍历所有命令配置，查找匹配的别名
        for (Map.Entry<String, CommandConfig.CommandDetail> entry : commandConfig.getCommands().entrySet()) {
            String commandName = entry.getKey();
            CommandConfig.CommandDetail detail = entry.getValue();

            // 检查是否为命令名本身
            if (commandName.equalsIgnoreCase(lowerAction)) {
                return commandName;
            }

            // 检查别名列表
            if (detail.getAliases() != null) {
                for (String alias : detail.getAliases()) {
                    if (alias.equalsIgnoreCase(lowerAction)) {
                        return commandName;
                    }
                }
            }
        }

        return inputAction; // 未找到匹配的命令，返回原输入
    }
} 