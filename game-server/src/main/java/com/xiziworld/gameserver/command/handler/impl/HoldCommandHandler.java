package com.xiziworld.gameserver.command.handler.impl;

import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import org.springframework.stereotype.Component;
import com.xiziworld.gameserver.common.constant.CmdConstant;

/**
 * 挂机命令处理器
 *
 * <AUTHOR>
 */
@Component
public class HoldCommandHandler extends AbstractCommandHandler {

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_HOLD;
    }

    @Override
    protected void validate(Command command) {
        // 检查角色是否存在
        checkCharacterExists(command);

        // 检查角色是否死亡
        checkCharacterDead(command);

        //检查是否可以挂机
    }

    @Override
    protected String doHandle(Command command) {

        return "功能待实现";
    }



}