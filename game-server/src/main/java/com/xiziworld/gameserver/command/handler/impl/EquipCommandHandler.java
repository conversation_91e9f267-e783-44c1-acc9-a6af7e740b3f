package com.xiziworld.gameserver.command.handler.impl;

import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import org.springframework.stereotype.Component;

/**
 * 装修升级命令处理器
 *   CommandConfig.COMMAND_UPGRADE_EQUIPMENT:
 *   CommandConfig.COMMAND_REFINE_JADE:
 *
 * <AUTHOR>
 */
@Component
public class EquipCommandHandler extends AbstractCommandHandler {

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_EQUIPMENT;
    }

    @Override
    protected void validate(Command command) {
        // 检查角色是否存在
        checkCharacterExists(command);

        // 检查角色是否死亡
        checkCharacterDead(command);

        //检查...
    }

    @Override
    protected String doHandle(Command command) {

        return "功能待实现";
    }



}