package com.xiziworld.gameserver.command.handler;

import com.xiziworld.gameserver.command.Command;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 命令处理器工厂
 *
 * <AUTHOR>
 */
@Component
public class CommandHandlerFactory {

    @Autowired
    private List<CommandHandler> handlers;

    /**
     * 获取命令处理器
     */
    public CommandHandler getHandler(Command command) {
        return handlers.stream()
                .filter(handler -> handler.support(command))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未知的命令类型"));
    }
} 