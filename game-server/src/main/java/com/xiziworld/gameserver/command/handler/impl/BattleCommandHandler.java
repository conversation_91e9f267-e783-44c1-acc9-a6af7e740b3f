package com.xiziworld.gameserver.command.handler.impl;

import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.Skill;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 战斗命令处理器，包括：
 *   CommandConfig.COMMAND_ATTACK
 *   CommandConfig.COMMAND_CAST_SKILL
 *   CommandConfig.COMMAND_REVIVE
 *
 * <AUTHOR>
 */
@Component
public class BattleCommandHandler extends AbstractCommandHandler {

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_BATTLE;
    }

    @Override
    protected void validate(Command command) {
        // 检查角色是否存在
        checkCharacterExists(command);
    }

    @Override
    protected String doHandle(Command command) {
        String commandName = command.getCommandName();
        if (CommandConfig.COMMAND_ATTACK.equals(commandName)) {
            // 检查角色是否死亡
            checkCharacterDead(command);
            return handleAttack(command);
        }
        if(CommandConfig.COMMAND_CAST_SKILL.equals(commandName)){
            // 检查角色是否死亡
            checkCharacterDead(command);
            // 检查技能是否存在
            checkSkillExists(command);
            return handleCastSkill(command);
        }
        if(CommandConfig.COMMAND_REVIVE.equals(commandName)){
            return handleRevive(command);
        }
        return "未知战斗命令："+command.getCommandName();
    }
    /*
     * 普通攻击
     */
    private String handleAttack(Command command) {
        // 攻击目标
        String target = command.getTarget();
        UserCharacter character = getCharacter(command);
        // 根据目标临时编号攻击
        if(StringUtils.isNumeric(target)){
            return battleManager.attackByTempId(character.getId(), Integer.parseInt(target));
        }
        // 根据目标名称攻击
        int tempId = mapManager.getTempIdByObjectName(character, target);
        if(tempId==0){
            return "🎯 大侠，你打啥呢？\n💡 发送'查看地图'目标列表";
        }
        return battleManager.attackByTempId(character.getId(), tempId);
    }
    /*
       技能攻击
     */
    private String handleCastSkill(Command command) {
        // 攻击目标
        String target = command.getTarget();
        UserCharacter character = getCharacter(command);

        String skillName = command.getArguments().get(0);
        Skill skill = skillMapper.getSkillByName(skillName);
        if(skill == null){
            return "❌ 江湖还没有这种绝技，大侠真的学了吗？";
        }
        // 根据目标临时编号攻击
        if(StringUtils.isNumeric(target)){
            return battleManager.useSkillAttack(character.getId(), skill.getSkillNo(), Integer.parseInt(target));
        }
        // 根据目标名称攻击
        int tempId = mapManager.getTempIdByObjectName(character, target);
        if(tempId==0){
            return "🎯 大侠，你打啥呢？\n💡 发送'查看地图'目标列表";
        }

        return battleManager.useSkillAttack(character.getId(), skill.getSkillNo(), tempId);
    }

    /*
     * 复活
     */
    private String handleRevive(Command command) {
        UserCharacter character = getCharacter(command);
        return playerManager.reviveCharacter(character.getId());
    }

    /*
     * 检查技能参数
     */
    private void checkSkillExists(Command command) {
        if(command.getArguments() == null || command.getArguments().isEmpty()){
            throw new GameException(GameException.SKILL_NOT_EXISTS);
        }
    }

} 