package com.xiziworld.gameserver.command.handler.impl;


import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


/**
 * 移动命令处理器，包括：
 *   CommandConfig.COMMAND_MOVE_TO
 *
 * <AUTHOR>
 */
@Component
public class MoveCommandHandler extends AbstractCommandHandler {

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_MOVE;
    }


    @Override
    protected void validate(Command command) {
        // 检查角色是否存在
        checkCharacterExists(command);

        // 检查角色是否死亡
        checkCharacterDead(command);

        // 检查目标是否存在
        checkTargetExists(command);
    }

    @Override
    protected String doHandle(Command command) {
        // 验证用户是否在游戏模式
        if (!command.isGameMode()) {
            return "请先进入游戏模式";
        }

        // 获取目标地点
        String destination = command.getTarget();
        if (destination == null || destination.isEmpty()) {
            return "请指定要前往的地点，例如：前往 苏堤";
        }
        UserCharacter userCharacter = getCharacter(command);
        //根据目标临时编号前往 目标附近
        if(StringUtils.isNumeric(destination)){
            return mapManager.moveToTempObject(userCharacter.getId(), Integer.parseInt(destination.trim()));
        }else{
            return mapManager.moveToMapByName(userCharacter.getId(), destination);
        }

    }

}