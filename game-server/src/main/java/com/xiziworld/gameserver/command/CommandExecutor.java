package com.xiziworld.gameserver.command;

import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.command.handler.CommandHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 命令执行器
 * 负责管理和调用命令处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandExecutor {

    @Autowired
    private List<CommandHandler> commandHandlers;

    private final Map<Integer, CommandHandler> handlerMap = new HashMap<>();

    /**
     * 初始化命令处理器映射
     */
    @PostConstruct
    public void init() {
        for (CommandHandler handler : commandHandlers) {
            handlerMap.put(handler.getCommandType(), handler);
            log.debug("注册命令处理器: {} -> {}", handler.getCommandType(), handler.getClass().getSimpleName());
        }
        log.info("命令执行器初始化完成，注册了{}个命令处理器", handlerMap.size());
    }

    /**
     * 执行命令
     */
    public String execute(Command command) {
        // 获取命令处理器
        CommandHandler handler = handlerMap.get(command.getType());
        if (handler == null) {
            return "未实现的命令：" + command.getAction();
        }

        // 检查是否可以执行
        if (!handler.support(command)) {
            return "当前无法执行命令：" + command.getAction();
        }

        try {
            // 执行命令
            return handler.handle(command);
        } catch (Exception e) {
            log.error("命令执行异常: {}", command.getType(), e);
            return "命令执行失败：" + e.getMessage();
        }
    }
}