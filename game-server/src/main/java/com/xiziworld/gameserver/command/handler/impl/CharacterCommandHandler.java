package com.xiziworld.gameserver.command.handler.impl;

import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.entity.UserAsset;
import com.xiziworld.gameserver.domain.manager.AssetManager;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.xiziworld.gameserver.common.constant.CmdConstant;

import java.util.List;

import static com.xiziworld.gameserver.common.GameException.CHARACTER_NOT_EXISTS;

/**
 *  玩家物品命令处理器，包括
 *  CommandConfig.COMMAND_CREATE_CHARACTER:
 *  CommandConfig.COMMAND_USE_ITEM:
 *  CommandConfig.COMMAND_EQUIP_ITEM:
 *  CommandConfig.COMMAND_STORE_ITEM:
 *  CommandConfig.COMMAND_RETRIEVE_ITEM:
 *  CommandConfig.COMMAND_EXCHANGE_CURRENCY
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CharacterCommandHandler extends AbstractCommandHandler {

    @Autowired
    private PlayerManager playerManager;

    @Autowired
    private AssetManager assetManager;

    @Autowired
    private ItemMapper itemMapper;

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_CHARACTER;
    }

    @Override
    protected void validate(Command command) {

        // 创建角色命令不需要检查角色是否存在
        if (!CommandConfig.COMMAND_CREATE_CHARACTER.equals(command.getCommandName())) {
            // 检查角色是否存在
            checkCharacterExists(command);
            // 检查角色是否死亡
            checkCharacterDead(command);
        }

        // 需要目标的命令检查目标是否存在
        if (CommandConfig.COMMAND_USE_ITEM.equals(command.getCommandName()) ||
            CommandConfig.COMMAND_EQUIP_ITEM.equals(command.getCommandName()) ||
            CommandConfig.COMMAND_STORE_ITEM.equals(command.getCommandName()) ||
            CommandConfig.COMMAND_RETRIEVE_ITEM.equals(command.getCommandName())) {
            checkTargetExists(command);
        }
    }

    @Override
    protected String doHandle(Command command) {
        try {
            String cmd = command.getCommandName();
            log.info("处理角色命令: cmd={}, userId={}, target={}", cmd, command.getUserId(), command.getTarget());

            switch (cmd) {
                case CommandConfig.COMMAND_CREATE_CHARACTER:
                    return handleCreateCharacter(command);
                case CommandConfig.COMMAND_USE_ITEM:
                    return handleUseItem(command);
                case CommandConfig.COMMAND_EQUIP_ITEM:
                    return handleEquipItem(command);
                case CommandConfig.COMMAND_STORE_ITEM:
                    return handleStoreItem(command);
                case CommandConfig.COMMAND_RETRIEVE_ITEM:
                    return handleRetrieveItem(command);
                case CommandConfig.COMMAND_EXCHANGE_CURRENCY:
                    return handleExchangeCurrency(command);
                default:
                    return "未知的角色操作命令: " + cmd;
            }
        } catch (GameException e) {
            log.error("角色命令处理失败: {}", e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("角色命令处理异常", e);
            return GameException.SYSTEM_ERROR;
        }
    }

    // ==================== 具体处理方法 ====================

    /**
     * 处理创建角色命令
     * 命令格式：创建剑客、创建仙师、创建圣僧
     */
    private String handleCreateCharacter(Command command) {
        String target = command.getTarget();
        if (target == null || target.trim().isEmpty()) {
            return "请指定要创建的角色职业，如：创建剑客";
        }

        // 解析职业类型
        Integer roleType = parseRoleType(target);
        if (roleType == null) {
            return "大侠闯荡江湖，需要选个合适的门派：剑客、仙师、圣僧";
        }
        if(StringUtils.isBlank(command.getUserName())){
            return "大侠闯荡江湖，岂能无名？";
        }

        try {
            // 检查是否已经创建过角色
            UserCharacter existingCharacter = getCharacter(command);
            if (existingCharacter != null) {
                return "你已经创建过角色了，角色名：" + existingCharacter.getName();
            }

            // 创建角色
            UserCharacter character = playerManager.createCharacter(
                command.getUserId(),
                command.getAppId(),
                command.getOpenId(),
                command.getAreaId(),
                command.getUserName().trim(),
                roleType
            );

            String roleTypeName = getRoleTypeName(roleType);
            return String.format("🎉 恭喜你！成功创建了%s角色：%s\n" +
                               "💪 初始等级：%d级\n" +
                               "❤️ 血量：%d\n" +
                               "💙 法力：%d\n" +
                               "📍 出生地：武林主城\n" +
                               "💡 输入 '状态' 查看详细属性",
                               roleTypeName, character.getName(), character.getLevel(),
                               character.getHp(), character.getMp());

        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("创建角色失败", e);
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 处理使用物品命令
     * 命令格式：用+物品名 或 使用+物品名
     */
    private String handleUseItem(Command command) {
        String itemName = command.getTarget().trim();
        if (itemName.isEmpty()) {
            return "请指定要使用的物品名称，如：用回气丹";
        }

        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);
            if (character == null) {
                throw new GameException(CHARACTER_NOT_EXISTS);
            }

            // 查找背包中的物品
            UserAsset targetAsset = findItemInInventory(character.getId(), itemName);
            if (targetAsset == null) {
                return "背包中没有找到物品：" + itemName;
            }

            // 使用物品
            return playerManager.useItem(character.getId(), targetAsset.getId());

        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("使用物品失败: itemName={}", itemName, e);
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 处理装备物品命令
     * 命令格式：装备+物品名
     */
    private String handleEquipItem(Command command) {
        String itemName = command.getTarget().trim();
        if (itemName.isEmpty()) {
            return "请指定要装备的物品名称，如：装备青锋剑";
        }

        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);
            if (character == null) {
                throw new GameException(CHARACTER_NOT_EXISTS);
            }

            // 查找背包中的装备
            UserAsset targetAsset = findItemInInventory(character.getId(), itemName);
            if (targetAsset == null) {
                return "背包中没有找到装备：" + itemName;
            }

            // 装备物品
            return assetManager.equipEquipment(character, targetAsset);
        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("装备物品失败: itemName={}", itemName, e);
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 处理存储物品命令
     * 命令格式：存+物品名
     */
    private String handleStoreItem(Command command) {
        String itemName = command.getTarget().trim();
        if (itemName.isEmpty()) {
            return "请指定要存储的物品名称，如：存青锋剑";
        }

        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);
            if (character == null) {
                throw new GameException(CHARACTER_NOT_EXISTS);
            }

            // 查找背包中的物品
            UserAsset targetAsset = findItemInInventory(character.getId(), itemName);
            if (targetAsset == null) {
                return "背包中没有找到物品：" + itemName;
            }

            // 存储物品到仓库
            return assetManager.moveItemToStorage(character.getId(), targetAsset.getId());

        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("存储物品失败: itemName={}", itemName, e);
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 处理取出物品命令
     * 命令格式：取+物品名
     */
    private String handleRetrieveItem(Command command) {
        String itemName = command.getTarget().trim();
        if (itemName.isEmpty()) {
            return "请指定要取出的物品名称，如：取青锋剑";
        }

        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);
            if (character == null) {
                throw new GameException(CHARACTER_NOT_EXISTS);
            }

            // 查找仓库中的物品
            UserAsset targetAsset = findItemInStorage(character.getId(), itemName);
            if (targetAsset == null) {
                return "仓库中没有找到物品：" + itemName;
            }

            // 从仓库取出物品
            return assetManager.moveItemFromStorage(character.getId(), targetAsset.getId());

        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("取出物品失败: itemName={}", itemName, e);
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 处理兑换货币命令
     * 命令格式：兑现+数量 (将银两兑换为金币)
     */
    private String handleExchangeCurrency(Command command) {
        String target = command.getTarget();
        if (target == null || target.trim().isEmpty()) {
            return "请指定要兑换的银两数量，如：兑现100";
        }

        try {
            // 解析兑换数量
            int silverAmount = Integer.parseInt(target.trim());
            if (silverAmount <= 0) {
                return "兑换数量必须大于0";
            }

            // 获取角色信息
            UserCharacter character = getCharacter(command);
            if (character == null) {
                throw new GameException(CHARACTER_NOT_EXISTS);
            }

            // 检查银两是否足够
            int currentSilver = assetManager.getSilverCount(character.getId());
            if (currentSilver < silverAmount) {
                return String.format("银两不足，当前银两：%d，需要：%d", currentSilver, silverAmount);
            }

            // 获取兑换比例配置
            int exchangeRate = getExchangeRate();
            int goldAmount = silverAmount * exchangeRate;

            if (goldAmount <= 0) {
                return String.format("兑换数量太少，至少需要%d银两才能兑换1金币", exchangeRate);
            }

            // 计算实际消耗的银两（可能有余数）
            int actualSilverCost = goldAmount * exchangeRate;

            // 执行兑换
            boolean consumeSuccess = assetManager.changeSilver(character.getId(), -actualSilverCost);
            if (!consumeSuccess) {
                return "扣除银两失败，请稍后重试";
            }

            boolean addSuccess = assetManager.changeGold(character.getId(), goldAmount);
            if (!addSuccess) {
                // 如果添加金币失败，需要回滚银两
                assetManager.changeSilver(character.getId(), actualSilverCost);
                return "兑换失败，请稍后重试";
            }

            return String.format("💰 兑换成功！\n" +
                               "消耗银两：%d\n" +
                               "获得金币：%d\n" +
                               "当前银两：%d\n" +
                               "当前金币：%d",
                               actualSilverCost, goldAmount,
                               assetManager.getSilverCount(character.getId()),
                               assetManager.getGoldCount(character.getId()));

        } catch (NumberFormatException e) {
            return "请输入有效的数字，如：兑现100";
        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("兑换货币失败: target={}", target, e);
            return GameException.SYSTEM_ERROR;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 解析职业类型
     */
    private Integer parseRoleType(String roleTypeName) {
        switch (roleTypeName.trim()) {
            case "剑客":
                return 1;
            case "仙师":
                return 2;
            case "圣僧":
                return 3;
            default:
                return null;
        }
    }

    /**
     * 获取职业类型名称
     */
    private String getRoleTypeName(Integer roleType) {
        switch (roleType) {
            case 1:
                return "剑客";
            case 2:
                return "仙师";
            case 3:
                return "圣僧";
            default:
                return "未知职业";
        }
    }

    /**
     * 在背包中查找物品
     */
    private UserAsset findItemInInventory(Long characterId, String itemName) {
        try {
            List<UserAsset> inventoryItems = assetManager.getInventoryItems(characterId);
            return findItemByName(inventoryItems, itemName);
        } catch (Exception e) {
            log.error("查找背包物品失败: characterId={}, itemName={}", characterId, itemName, e);
            return null;
        }
    }

    /**
     * 在仓库中查找物品
     */
    private UserAsset findItemInStorage(Long characterId, String itemName) {
        try {
            List<UserAsset> storageItems = assetManager.getStorageItemInfo(characterId);
            return findItemByName(storageItems, itemName);
        } catch (Exception e) {
            log.error("查找仓库物品失败: characterId={}, itemName={}", characterId, itemName, e);
            return null;
        }
    }

    /**
     * 根据物品名称查找物品
     */
    private UserAsset findItemByName(List<UserAsset> assets, String itemName) {
        if (assets == null || assets.isEmpty()) {
            return null;
        }

        // 首先尝试精确匹配
        for (UserAsset asset : assets) {
            if (itemName.equals(getItemName(asset))) {
                return asset;
            }
        }

        // 如果精确匹配失败，尝试模糊匹配
        for (UserAsset asset : assets) {
            String assetName = getItemName(asset);
            if (assetName != null && assetName.contains(itemName)) {
                return asset;
            }
        }

        return null;
    }

    /**
     * 获取物品名称
     */
    private String getItemName(UserAsset asset) {
        try {
            Item item = itemMapper.selectByItemNo(asset.getItemNo());
            return item==null?null:item.getName();
        } catch (Exception e) {
            log.error("获取物品名称失败: assetId={}", asset.getId(), e);
            return asset.getItemNo();
        }
    }

    /**
     * 获取兑换比例
     */
    private int getExchangeRate() {
        try {
            // 银两兑换金币的比例，默认1银两=1000金币
            return 1000;
        } catch (Exception e) {
            log.error("获取兑换比例失败，使用默认值", e);
            return 1000;
        }
    }
}