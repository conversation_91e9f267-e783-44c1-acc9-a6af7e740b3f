package com.xiziworld.gameserver.command;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 命令日志记录器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandLogger {

    /**
     * 记录命令
     */
    public void logCommand(Command command) {
        log.info("Command received - userId: {}, appId: {}, areaId: {}, command: {}", command.getUserId(), command.getAppId(), command.getAreaId(), JSON.toJSONString(command));
    }

    /**
     * 记录结果
     */
    public void logResult(Command command, String result) {
        log.info("Command executed - userId: {}, appId: {}, areaId: {}, command: {}, result: {}", command.getUserId(), command.getAppId(), command.getAreaId(), JSON.toJSONString(command), result);
    }

    /**
     * 记录错误
     */
    public void logError(String text, String error) {
        log.error("Command error - text: {}, error: {}", text, error);
    }

    /**
     * 记录调试信息
     */
    public void logDebug(String message) {
        log.debug(message);
    }

    /**
     * 记录警告信息
     */
    public void logWarn(String message) {
        log.warn(message);
    }
} 