package com.xiziworld.gameserver.command.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.Area;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import com.xiziworld.gameserver.domain.mapper.AreaMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *  管理员命令处理器，包含
 *  CommandConfig.COMMAND_ADMIN_RELOAD:
 *  CommandConfig.COMMAND_ADMIN_RESTART:
 *
 * <AUTHOR>
 */
@Component
public class AdminCommandHandler extends AbstractCommandHandler {
    @Autowired
    private AreaMapper areaMapper;

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_ADMIN;
    }
    @Override
    protected void validate(Command command) {
        if(CommandConfig.COMMAND_ADMIN_CREATE_AREA.equals(command.getCommandName())){
            String areaName = command.getTarget();
            List<Area> areas = areaMapper.selectAll();
            for(Area area : areas){
                if(area.getAreaName().equals(areaName)){
                    throw new GameException(GameException.AREA_NAME_EXISTS);
                }
                if(area.getOpenId().equals(command.getOpenId())){
                    throw new GameException(GameException.AREA_ALREADY_CREATED + area.getAreaName());
                }
            }
        }
    }

    @Override
    protected String doHandle(Command command) {
        String cmd = command.getCommandName();
        switch (cmd) {
            case CommandConfig.COMMAND_ADMIN_CREATE_AREA:
                return createArea(command);
            case CommandConfig.COMMAND_ADMIN_LOAD_AREA:
                return loadArea(command);
        }
        return "🔧 管理员功能正在开发中，敬请期待...";
    }
    private String loadArea(Command command) {
        List<Area> areas = areaMapper.selectAll();
        JSONObject jsonObject = new JSONObject();
        for(Area area : areas){
            jsonObject.put(area.getOpenId(), area.getId().toString());
        }
        return jsonObject.toString();
    }
    private String createArea(Command command) {
        String areaName = command.getTarget().trim();
        Area area = new Area();
        area.setAreaName(areaName);
        area.setOpenId(command.getOpenId());
        area.setAppId(command.getAppId());
        area.setStatus(0);
        areaMapper.insert(area);

        return "🏛️ 【系统公告】\n" +
               "✨ 新的江湖分区已开启！\n" +
               "🌟 区服名称：" + areaName + "\n" +
               "🎊 恭喜！新的武林世界等待着侠客们的到来！\n" +
               "⚔️ 愿各路英雄豪杰在此书写自己的故事！";
    }

}