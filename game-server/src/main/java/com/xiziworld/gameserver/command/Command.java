package com.xiziworld.gameserver.command;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 命令实体类
 *
 * <AUTHOR>
 */
@Data
public class Command {

    /**
     * 原始命令文本
     */
    private String rawText;

    /**
     * 命令名称（从配置中解析出的标准命令名）
     */
    private String commandName;

    /**
     * 命令类型
     * 1: 查看命令
     * 2: 移动命令
     * 3: 战斗命令
     * 4: 交互命令
     */
    private Integer type;

    /**
     * 操作词（用户输入的原始命令词，可能是别名）
     */
    private String action;

    /**
     * 操作对象
     */
    private String target;

    /**
     * @人物列表
     */
    private List<String> atUsers;

    /**
     * 命令参数
     */
    private List<String> arguments;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名，群名片，游戏名
     */
    private String userName;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 应用open_id
     */
    private String openId;

    /**
     * 区服ID
     */
    private Long areaId;

    /**
     * 是否强命令(以!开头)
     */
    private boolean isStrong;

    /**
     * 是否游戏模式
     */
    private boolean isGameMode;
} 