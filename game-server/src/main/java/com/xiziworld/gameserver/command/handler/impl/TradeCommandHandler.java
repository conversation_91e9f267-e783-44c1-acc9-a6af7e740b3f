package com.xiziworld.gameserver.command.handler.impl;

import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.MapManager;
import com.xiziworld.gameserver.domain.manager.TradeManager;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.config.NpcsConfig;
import com.xiziworld.gameserver.domain.manager.config.ShopsConfig;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import com.xiziworld.gameserver.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 交易命令处理器，包含
 *  CommandConfig.COMMAND_BUY: 购买 - 从市场/NPC购买物品
 *  CommandConfig.COMMAND_SELL: 出售 - 向市场/NPC出售物品
 *  CommandConfig.COMMAND_ASK_NPC: 询问NPC - 向NPC询问信息
 *  CommandConfig.COMMAND_CANCEL_SELL: 取出市场售卖
 *  CommandConfig.COMMAND_VIEW_MARKET: 查看市场
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TradeCommandHandler extends AbstractCommandHandler {

    @Autowired
    private TradeManager tradeManager;

    @Autowired
    private MapManager mapManager;

    @Autowired
    private ConfigManager configManager;

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_TRADE;
    }

    @Override
    protected void validate(Command command) {
        String cmd = command.getCommandName();

        // 所有交易命令都需要角色存在
        checkCharacterExists(command);

        // 大部分交易命令需要角色活着（除了查看市场）
        if (!CommandConfig.COMMAND_VIEW_MARKET.equals(cmd)) {
            checkCharacterDead(command);
        }

        // 询问NPC需要检查目标存在
        if (CommandConfig.COMMAND_ASK_NPC.equals(cmd)) {
            checkTargetExists(command);
        }
    }

    @Override
    protected String doHandle(Command command) {
        try {
            String cmd = command.getCommandName();
            UserCharacter character = getCharacter(command);
            log.info("处理交易命令: cmd={}, userId={}, target={}", cmd, command.getUserId(), command.getTarget());

            switch (cmd) {
                case CommandConfig.COMMAND_BUY:
                    return handleBuy(command, character);
                case CommandConfig.COMMAND_SELL:
                    return handleSell(command, character);
                case CommandConfig.COMMAND_ASK_NPC:
                    return handleAskNpc(command, character);
                case CommandConfig.COMMAND_CANCEL_SELL:
                    return handleCancelSell(command, character);
                case CommandConfig.COMMAND_VIEW_MARKET:
                    return handleViewMarket(command, character);
                default:
                    return "未知的交易命令: " + cmd;
            }
        } catch (GameException e) {
            log.error("交易命令处理失败: {}", e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("交易命令处理异常", e);
            return "系统错误";
        }
    }

    // ==================== 具体命令处理方法 ====================

    /**
     * 处理购买命令
     * 支持格式：
     * - 买 物品名 数量 (从当前位置NPC购买)
     * - 买 交易ID 数量 (从玩家市场购买)
     */
    private String handleBuy(Command command, UserCharacter character) {
        String itemOrTradeId = command.getTarget();
        List<String> params = command.getArguments();
        if (StringUtils.isBlank(itemOrTradeId) || params.isEmpty()) {
            return "🛒 购买格式：买 物品名/交易ID 数量";
        }

        int quantity;
        try {
            quantity = Integer.parseInt(params.get(0));
        } catch (NumberFormatException e) {
            return "🔢 数量必须是数字！";
        }

        if (quantity <= 0) {
            return "🤔 购买数量必须大于零！";
        }

        // 判断是交易ID还是物品名
        if (isTradeNo(itemOrTradeId)) {
            // 从玩家市场购买
            return tradeManager.buyItem(character.getId(), itemOrTradeId, quantity);
        } else {
            // 从NPC购买，需要获取销售该物品的NPC
            NpcsConfig.NpcDetail npc = mapManager.getNearbyNpcForItem(character, itemOrTradeId);
            if (npc == null) {
                return "🤷‍♂️ 附近没有出售 " + itemOrTradeId + "的掌柜！";
            }
            return tradeManager.buyFromNPC(character.getId(), npc.getNpcNo(), itemOrTradeId, quantity);
        }
    }
    private boolean isTradeNo(String itemOrTradeId) {
        if (itemOrTradeId == null || itemOrTradeId.trim().isEmpty()) {
            return false;
        }
        if (itemOrTradeId.startsWith("T") || itemOrTradeId.startsWith("t")) {
            return StringUtils.isNumeric(itemOrTradeId.substring(1));
        }
        return false;
    }

    /**
     * 处理出售命令
     * 支持格式：
     * - 卖 物品名 数量 (向当前位置NPC出售)
     * - 卖 物品名 数量 价格 货币 (上架到玩家市场)
     */
    private String handleSell(Command command, UserCharacter character) {
        String itemName = command.getTarget();
        List<String> params = command.getArguments();
        if (StringUtils.isBlank(itemName) || params.isEmpty()) {
            return "💰 出售格式：卖 物品名 数量 [价格] [货币]";
        }

        int quantity;
        try {
            quantity = Integer.parseInt(params.get(0));
        } catch (NumberFormatException e) {
            return "🔢 数量必须是数字！";
        }

        if (quantity <= 0) {
            return "🤔 出售数量必须大于零！";
        }

        // 如果有价格参数，则上架到玩家市场
        if (params.size() >= 3) {
            int price;
            try {
                price = Integer.parseInt(params.get(1));
            } catch (NumberFormatException e) {
                return "🔢 价格必须是数字！";
            }

            if (price <= 0) {
                return "🤔 价格必须大于零！";
            }
            String currencyName = params.get(2);
            String currency = "金币".equals(currencyName) ? ShopsConfig.CURRENCY_GOLD : ShopsConfig.CURRENCY_SILVER;
            return tradeManager.publishItem(character.getId(), itemName, quantity, price, currency);
        } else {
            // 向NPC出售，需要获取收购该物品的NPC
            NpcsConfig.NpcDetail npc = mapManager.getNearbyNpcForItem(character, itemName);
            if (npc == null) {
                return "🤷‍♂️ 附近没有收购 " + itemName + "的掌柜！";
            }
            return tradeManager.sellToNPC(character.getId(), npc.getNpcNo(), itemName, quantity);
        }
    }

    /**
     * 处理询问NPC命令
     */
    private String handleAskNpc(Command command, UserCharacter character) {
        String npcNameOrId = command.getTarget();
        if (npcNameOrId == null || npcNameOrId.trim().isEmpty()) {
            return "💬 询问格式：询问 NPC名字/编号";
        }
        if(StringUtils.isNumeric(npcNameOrId)){
            MapManager.TempObject tempObject = mapManager.getTempObject(character.getId(), Integer.parseInt(npcNameOrId));
            if(tempObject==null || tempObject.getType()!=MapManager.TempObject.TYPE_NPC){
                return "🤷‍♂️ 找不到编号为 " + npcNameOrId + " 的人物！";
            }
            npcNameOrId = tempObject.getObjectId();
             return tradeManager.queryNPCShop(character.getId(), npcNameOrId);
        }
        // 根据名字或ID查找NPC
        NpcsConfig.NpcDetail npc = findNpcByName(npcNameOrId);
        if (npc == null) {
            return "🤷‍♂️ 找不到名为 " + npcNameOrId + " 的NPC！";
        }

        return tradeManager.queryNPCShop(character.getId(), npc.getNpcNo());
    }

    /**
     * 处理取消出售命令
     */
    private String handleCancelSell(Command command, UserCharacter character) {
        String tradeId = command.getTarget();
        if (tradeId == null || tradeId.trim().isEmpty()) {
            return "📤 取消格式：下架 交易ID";
        }

        return tradeManager.cancelTrade(character.getId(), tradeId);
    }

    /**
     * 处理查看市场命令
     */
    private String handleViewMarket(Command command, UserCharacter character) {
        String target = command.getTarget();

        if (target != null && !target.trim().isEmpty()) {
            // 查看具体交易详情
            return tradeManager.getTradeDetail(target);
        } else {
            // 查看市场列表
            return tradeManager.getTradeList();
        }
    }

    // ==================== 辅助方法 ====================


    /**
     * 根据NPC名字或ID查找NPC ID
     * @param name NPC名字
     * @return NPC 如果找不到返回null
     */
    private NpcsConfig.NpcDetail findNpcByName(String name) {
        NpcsConfig npcsConfig = configManager.getNpcsConfig();
        if (npcsConfig == null || npcsConfig.getNpcs() == null) {
            return null;
        }

        Map<String, NpcsConfig.NpcDetail> npcs = npcsConfig.getNpcs();

        // 如果直接查找失败，则遍历所有NPC，根据名字查找
        for (Map.Entry<String, NpcsConfig.NpcDetail> entry : npcs.entrySet()) {
            NpcsConfig.NpcDetail npcDetail = entry.getValue();
            if (npcDetail != null && name.equals(npcDetail.getName())) {
                return npcDetail; // 返回npcId
            }
        }

        return null; // 找不到
    }

}