package com.xiziworld.gameserver.command;

import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 命令处理器
 * 负责接收命令并路由到对应的处理器
 * 采用命令模式设计
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandProcessor {

    @Autowired
    private ConfigManager configManager;

    @Autowired
    private CommandParser commandParser;

    @Autowired
    private CommandValidator commandValidator;

    @Autowired
    private CommandExecutor commandExecutor;

    @Autowired
    private CommandLogger commandLogger;

    /**
     * 处理命令
     */
    public String processCommand(Command cmd) {
        try {
            // 1. 解析命令
            Command command = commandParser.parse(cmd);
            if (command == null) {
                log.warn("命令解析失败，不是游戏命令: {}", cmd.getRawText());
                return null;
            }

            // 2. 记录命令
            commandLogger.logCommand(command);

            // 3. 检查命令配置
            String commandName = command.getCommandName();
            CommandConfig.CommandDetail commandDetail = getCommandDetail(commandName);
            if (commandDetail == null) {
                //return GameException.UNKNOWN_COMMAND;
                return null;
            }

            // 4. 权限检查
            String permissionResult = checkPermission(command, commandDetail, cmd.getUserId());
            if (permissionResult != null) {
                return permissionResult;
            }

            // 5. 限流检查
            String rateLimitResult = checkRateLimit(command, commandDetail, cmd.getUserId());
            if (rateLimitResult != null) {
                return rateLimitResult;
            }

            // 6. 验证命令
            commandValidator.validate(command);

            // 7. 执行命令
            String result = commandExecutor.execute(command);

            // 8. 记录结果
            commandLogger.logResult(command, result);

            return result;

        } catch (GameException e) {
            log.warn("命令处理失败: {}, 用户: {}", e.getMessage(), cmd.getUserId());
            commandLogger.logError(cmd.getRawText(), e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("命令处理异常", e);
            commandLogger.logError(cmd.getRawText(), "系统错误");
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 获取命令配置详情
     */
    private CommandConfig.CommandDetail getCommandDetail(String commandName) {
        CommandConfig commandConfig = configManager.getCommandConfig();
        if (commandConfig == null || commandConfig.getCommands() == null) {
            return null;
        }
        return commandConfig.getCommands().get(commandName);
    }

    /**
     * 检查命令权限
     */
    private String checkPermission(Command command, CommandConfig.CommandDetail commandDetail, String userId) {
        // 检查是否为管理员专用命令
        if (Boolean.TRUE.equals(commandDetail.getAdminOnly())) {
            // TODO: 实现管理员权限检查逻辑
            // if (!isAdmin(userId)) {
            //     return "此命令仅限管理员使用";
            // }
            log.debug("管理员命令权限检查: {}, 用户: {}", command.getCommandName(), userId);
        }

        return null; // 权限检查通过
    }

    /**
     * 检查命令限流
     */
    private String checkRateLimit(Command command, CommandConfig.CommandDetail commandDetail, String userId) {
        CommandConfig.RateLimit rateLimit = commandDetail.getRateLimit();
        if (rateLimit == null) {
            return null; // 无限流配置
        }

        // TODO: 实现具体的限流逻辑
        // 这里需要维护用户命令执行记录，检查是否超出限制
        //log.debug("命令限流检查: {}, 用户: {}, 限制: {}秒内最多{}次", command.getCommandName(), userId, rateLimit.getSeconds(), rateLimit.getMaxCount());

        return null; // 限流检查通过
    }
} 