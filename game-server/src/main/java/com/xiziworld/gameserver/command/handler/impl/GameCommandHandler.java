package com.xiziworld.gameserver.command.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.Area;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.mapper.AreaMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 游戏进出帮助等命令处理器，包含
 *  CommandConfig.COMMAND_HELP:
 *  CommandConfig.COMMAND_ENTER_GAME:
 *  CommandConfig.COMMAND_EXIT_GAME:
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GameCommandHandler extends AbstractCommandHandler {

    @Autowired
    private PlayerManager playerManager;
    @Autowired
    private AreaMapper areaMapper;

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_GAME;
    }

    @Override
    protected void validate(Command command) {
        // 游戏命令大部分不需要验证，帮助命令任何人都可以使用
        // 进入/退出游戏模式需要角色存在
        String cmd = command.getCommandName();
        if (CommandConfig.COMMAND_ENTER_GAME.equals(cmd) || CommandConfig.COMMAND_EXIT_GAME.equals(cmd)) {
            checkCharacterExists(command);
        }
    }

    @Override
    protected String doHandle(Command command) {
        try {
            String cmd = command.getCommandName();
            log.info("处理游戏命令: cmd={}, userId={}, target={}", cmd, command.getUserId(), command.getTarget());

            switch (cmd) {
                case CommandConfig.COMMAND_HELP:
                    return doHelp(command);
                case CommandConfig.COMMAND_ENTER_GAME:
                    return enterGame(command);
                case CommandConfig.COMMAND_EXIT_GAME:
                    return exitGame(command);
                default:
                    return GameException.UNKNOWN_COMMAND;
            }
        } catch (GameException e) {
            log.error("游戏命令处理失败: {}", e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("游戏命令处理异常", e);
            return GameException.SYSTEM_ERROR;
        }
    }
    // ==================== 具体处理方法 ====================
    /**
     * 进入游戏模式
     */
    private String enterGame(Command command) {
        try {
            UserCharacter character = getCharacter(command);
            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            // 检查当前游戏状态
            if (character.getGameStatus() != null && character.getGameStatus() == 1) {
                return GameException.ALREADY_IN_GAME;
            }

            // 更新游戏状态为在线
            character.setGameStatus(1);
            playerManager.onCharacterLogin(character.getId());

            return String.format("🎮 欢迎来到西子江湖，%s！\n" +
                               "📍 当前位置：%s\n" +
                               "💡 输入 '状态' 查看角色信息\n" +
                               "💡 输入 '查看地图' 了解周围环境",
                               character.getName(), getCurrentLocationName(character));

        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("进入游戏失败", e);
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 退出游戏模式
     */
    private String exitGame(Command command) {
        try {
            UserCharacter character = getCharacter(command);
            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            // 检查当前游戏状态
            if (character.getGameStatus() == null || character.getGameStatus() == 0) {
                return GameException.NOT_IN_GAME;
            }

            // 更新游戏状态为离线
            character.setGameStatus(0);
            playerManager.onCharacterLogout(character.getId());

            return String.format("👋 %s，江湖路远，后会有期！\n" +
                               "💾 角色数据已保存\n" +
                               "💡 发送 '!游戏模式' 重新进入游戏",
                               character.getName());

        } catch (GameException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("退出游戏失败", e);
            return GameException.SYSTEM_ERROR;
        }
    }
    /**
     * 处理帮助命令
     */
    private String doHelp(Command command) {
        String target = command.getTarget();

        // 只有帮助，返回游戏介绍
        if (StringUtils.isBlank(target)) {
            return "🎮 欢迎来到西子江湖！\n\n" +
                   "📖 游戏背景：\n" +
                   "以杭州西湖的历史文化为背景，融合白蛇传、雷峰塔等传统故事元素的文字RPG游戏。\n\n" +
                   "🎯 游戏特色：\n" +
                   "• 纯文字交互，无需图形界面\n" +
                   "• 三大职业相互克制的战斗系统\n" +
                   "• 丰富的西湖文化地图探索\n" +
                   "• 社交化群聊游戏体验\n" +
                   "• 装备强化与技能学习\n" +
                   "• 打架与打怪双重战斗模式\n\n" +
                   "🚀 开始游戏：\n" +
                   "发送 !游戏模式 进入游戏\n" +
                   "发送 帮助 1  了解职业信息\n" +
                   "发送 帮助 2  查看详细教程";
        }

        // 职业介绍
        if ("1".equals(target)) {
            return "⚔️ 西子江湖三大职业介绍\n\n" +
                   "🗡️ 剑客：\n" +
                   "• 职业特点：攻击力强，速度快\n" +
                   "• 克制关系：克制圣僧，被仙师克制\n" +
                   "• 适合玩家：喜欢近战输出的玩家\n\n" +
                   "🔮 仙师：\n" +
                   "• 职业特点：法术攻击，范围伤害\n" +
                   "• 克制关系：克制剑客，被圣僧克制\n" +
                   "• 适合玩家：喜欢法术输出的玩家\n\n" +
                   "🙏 圣僧：\n" +
                   "• 职业特点：防御力高，血量厚\n" +
                   "• 克制关系：克制仙师，被剑客克制\n" +
                   "• 适合玩家：喜欢坦克防御的玩家\n\n" +
                   "💡 发送 '加入江湖 职业名' 开始你的江湖之路！\n" +
                   "例如：加入江湖 剑客";
        }

        // 新手指南目录
        if ("2".equals(target)) {
            return "📚 西子江湖新手指南\n\n" +
                   "🎯 指南目录：\n" +
                   "2.1 角色创建\n" +
                   "2.2 基础命令\n" +
                   "2.3 战斗系统\n" +
                   "2.4 装备系统\n" +
                   "2.5 地图探索\n" +
                   "2.6 社交功能\n" +
                   "2.7 进阶技巧\n\n" +
                   "💡 发送 帮助 章节 查看具体章节\n" +
                   "例如：帮助 2.1 查看角色创建指南";
        }

        // 角色创建指南
        if ("2.1".equals(target)) {
            return "👤 角色创建指南\n\n" +
                   "🎯 创建步骤：\n" +
                   "1. 选择职业：剑客、仙师、圣僧\n" +
                   "2. 发送命令：加入江湖 职业名\n" +
                   "📝 示例命令：\n" +
                   "• 加入江湖 剑客\n" +
                   "• 加入江湖 仙师\n" +
                   "• 加入江湖 圣僧\n\n" +
                   "🏠 出生地点：武林主城\n" +
                   "💰 初始资源：基础装备+少量金币+1件武器\n\n" +
                   "💡 创建完成后发送 '状态' 查看角色信息";
        }

        // 基础命令
        if ("2.2".equals(target)) {
            return "⌨️ 基础命令指南\n\n" +
                   "🎮 游戏模式：\n" +
                   "• !游戏模式 - 进入游戏，进入游戏后命令不需要再加'!'\n" +
                   "• 退出游戏 - 退出游戏\n\n" +
                   "👤 角色操作：\n" +
                   "• 状态 - 查看角色信息\n" +
                   "• 背包 - ITEM_MAT_STONE物品\n" +
                   "• 装备 - 查看已装备物品\n\n" +
                   "🗺️ 地图操作：\n" +
                   "• 查看地图 - 查看当前地图\n" +
                   "• 前往 地点 - 移动到指定地点\n" +
                   "💡部分命令支持简化输入，如 '2' 代表 '状态'，'1' 代表 '攻击'\"";
        }

        // 战斗系统
        if ("2.3".equals(target)) {
            return "⚔️ 战斗系统指南\n\n" +
                   "🎯 战斗类型：\n" +
                   "• 打怪：攻击怪物获得经验\n" +
                   "• 打架：与其他玩家对战\n\n" +
                   "📝 战斗命令：\n" +
                   "• 攻击 目标编号 - 普通攻击\n" +
                   "• 施 技能名 目标编号 - 使用技能\n" +
                   "• 复活 - 死亡后复活\n\n" +
                   "🔄 职业克制：\n" +
                   "• 剑客 → 圣僧 → 仙师 → 剑客\n" +
                   "• 克制关系影响伤害倍率\n\n" +
                   "💡 战斗有冷却时间，请合理安排攻击节奏";
        }

        // 装备系统
        if ("2.4".equals(target)) {
            return "🛡️ 装备系统指南\n\n" +
                   "📦 装备管理：\n" +
                   "• 穿 装备名 - 穿戴装备\n" +
                   "• 存 物品名 - 存入仓库\n" +
                   "• 取 物品名 - 从仓库取出\n\n" +
                   "⭐ 装备强化：\n" +
                   "• 升品 装备名 - 装备升品\n" +
                   "• 浣灵 - 玉佩浣灵（提升属性倍率）\n\n" +
                   "🎯 装备部位：\n" +
                   "武器、头部、衣服、护手、护膝、裤子、鞋子、玉佩\n\n" +
                   "💡 高品质装备属性更强，合理搭配提升战力";
        }

        // 地图探索
        if ("2.5".equals(target)) {
            return "🗺️ 地图探索指南\n\n" +
                   "🏞️ 探索命令：\n" +
                   "• 查看地图 - 查看当前地图详情\n" +
                   "• 前往 地点名 - 移动到指定地点\n\n" +
                   "🎣 采集活动：\n" +
                   "• 钓鱼 - 在湖边钓鱼获得物品\n" +
                   "• 采桑 - 采集桑叶\n" +
                   "• 采茶 - 采集茶叶\n" +
                   "• 挖矿 - 挖掘矿物\n\n" +
                   "🏃 移动机制：\n" +
                   "• 地图间通过连接点移动\n" +
                   "• 部分地点需要特定条件解锁\n\n" +
                   "💡 多探索不同地图，发现隐藏的宝藏和任务";
        }

        // 社交功能
        if ("2.6".equals(target)) {
            return "👥 社交功能指南\n\n" +
                   "🤝 玩家交易：\n" +
                   "• 买 @卖家 背包序号 数量 货币数量\n" +
                   "• 同意 @买家 \n\n" +
                   "⚔️ 打架pk：\n" +
                   "• 攻击 玩家编号 - 挑战其他玩家\n" +
                   "• 注意：斗殴有风险，谨慎挑战\n\n" +
                   "💡 结交江湖好友，共同闯荡西子江湖";
        }

        // 进阶技巧
        if ("2.7".equals(target)) {
            return "🎯 进阶技巧指南\n\n" +
                   "💰 经济管理：\n" +
                   "• 合理分配银两和金币\n" +
                   "• 兑现银两获得金币\n" +
                   "• 通过交易获得稀有物品\n\n" +
                   "📈 角色发展：\n" +
                   "• 优先提升核心装备品质\n" +
                   "• 学习适合职业的技能\n" +
                   "• 合理分配属性点\n\n" +
                   "🎮 游戏策略：\n" +
                   "• 了解职业克制关系\n" +
                   "• 掌握战斗时机和距离\n" +
                   "• 善用地图环境优势\n\n" +
                   "💡 熟能生巧，多实践才能成为江湖高手";
        }

        return "大侠你想说什么？\n💡 发送 !帮助 查看完整帮助信息";
    }

    // ==================== 工具方法 ====================

    /**
     * 获取当前位置名称
     */
    private String getCurrentLocationName(UserCharacter character) {
        try {
            // 这里可以根据character的position信息获取地图名称
            // 暂时返回默认值
            return "武林主城";
        } catch (Exception e) {
            log.error("获取位置名称失败", e);
            return "未知地点";
        }
    }
}