package com.xiziworld.gameserver.command.handler.impl;

import com.alibaba.fastjson.JSON;
import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.handler.AbstractCommandHandler;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.common.constant.GameAttributeConstant;
import com.xiziworld.gameserver.domain.entity.Rank;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.Helper;
import com.xiziworld.gameserver.domain.manager.AssetManager;
import com.xiziworld.gameserver.domain.manager.MapManager;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.mapper.RankMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import com.alibaba.fastjson.JSONObject;

/**
 * 查看命令处理器，包括
 * CommandConfig.COMMAND_VIEW_STATUS: 查看状态
 * CommandConfig.COMMAND_VIEW_MAP: 查看地图
 * CommandConfig.COMMAND_VIEW_EQUIPMENT: 查看装备
 * CommandConfig.COMMAND_VIEW_INVENTORY: 查看背包
 * CommandConfig.COMMAND_VIEW_RANKING: 查看排名
 * CommandConfig.COMMAND_VIEW_TARGET: 查看目标
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ViewCommandHandler extends AbstractCommandHandler {

    @Autowired
    private PlayerManager playerManager;

    @Autowired
    private AssetManager assetManager;

    @Autowired
    private MapManager mapManager;

    @Autowired
    private RankMapper rankMapper;

    @Override
    public int getCommandType() {
        return CmdConstant.CMD_TYPE_VIEW;
    }

    @Override
    protected void validate(Command command) {
        // 检查角色是否存在
        checkCharacterExists(command);

        // 查看他人信息需要@
        if (command.getTarget() != null && command.getAtUsers().isEmpty()) {
            throw new GameException(GameException.CHARACTER_VIEW_AT_REQUIRED);
        }
    }

    @Override
    protected String doHandle(Command command) {
        try {
            String cmd = command.getCommandName();
            log.info("处理查看命令: cmd={}, userId={}, target={}", cmd, command.getUserId(), command.getTarget());

            switch (cmd) {
                case CommandConfig.COMMAND_VIEW_STATUS:
                    return viewStatus(command);
                case CommandConfig.COMMAND_VIEW_MAP:
                    return viewMap(command);
                case CommandConfig.COMMAND_VIEW_EQUIPMENT:
                    return viewEquipment(command);
                case CommandConfig.COMMAND_VIEW_INVENTORY:
                    return viewInventory(command);
                case CommandConfig.COMMAND_VIEW_RANKING:
                    return viewRanking(command);
                case CommandConfig.COMMAND_VIEW_TARGET:
                    return viewTarget(command);
                default:
                    return "未知的查看命令: " + cmd;
            }
        } catch (GameException e) {
            log.error("查看命令处理失败: {}", e.getMessage());
            return e.getMessage();
        } catch (Exception e) {
            log.error("查看命令处理异常", e);
            return GameException.SYSTEM_ERROR;
        }
    }

    /**
     * 查看状态
     */
    private String viewStatus(Command command) {
        try {
            // 获取角色信息
            UserCharacter character =getCharacter(command);

            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            // 获取位置信息
            Map<String, Object> position = mapManager.getCharacterPosition(character.getId());
            String mapId = (String) position.get("mapId");

            // 获取地图名称
            String mapName = "未知地图";
            if (mapId != null) {
                try {
                    mapName = mapManager.getMapDetail(mapId).getName();
                } catch (Exception e) {
                    log.warn("获取地图名称失败: mapId={}", mapId);
                }
            }

            // 获取货币信息
            int silver = assetManager.getSilverCount(character.getId());
            int gold = assetManager.getGoldCount(character.getId());

            JSONObject attributes = JSON.parseObject(character.getAttributes());
            // 计算下一级所需经验（简单计算）

            Long nextLevelExp = character.getMaxExp();

            // 构建状态信息
            StringBuilder sb = new StringBuilder();
            String roleTypeName = Helper.getRoleTypeName(character.getType());
            sb.append("╭─────────────────────╮\n");
            sb.append(String.format("👤 角色: %s(%s)\n", character.getName(),roleTypeName));
            sb.append(String.format("⭐ 等级: %d级 (%d/%d)\n",
                character.getLevel(),
                character.getExp(),
                nextLevelExp));
            sb.append(String.format("❤️ 血量: %d/%d\n", character.getHp(), character.getMaxHp()));
            sb.append(String.format("💙 法力: %d/%d\n", character.getMp(), character.getMaxMp()));
            sb.append("├─────────────────────┤\n");

            sb.append(String.format("⚔️ 物攻: %d  🛡️ 物防: %d\n",
                attributes.getIntValue(GameAttributeConstant.PHY_ATK),
                attributes.getIntValue(GameAttributeConstant.PHY_DEF)));
            sb.append(String.format("🔮 法攻: %d  🌟 法防: %d\n",
                attributes.getIntValue(GameAttributeConstant.MAG_ATK),
                attributes.getIntValue(GameAttributeConstant.MAG_DEF)));
            sb.append(String.format("🙏 佛攻: %d  ✨ 佛防: %d\n",
                attributes.getIntValue(GameAttributeConstant.BUD_ATK),
                attributes.getIntValue(GameAttributeConstant.BUD_DEF)));
            sb.append("├─────────────────────┤\n");
            sb.append(String.format("💰 银两: %d\n", silver));
            sb.append(String.format("🪙 金币: %d\n", gold));
            sb.append(String.format("🗺️ 位置: %s\n", mapName));
            sb.append(String.format("💚 状态: %s\n", character.getHp() > 0 ? "正常" : "死亡"));
            sb.append("╰─────────────────────╯");

            return sb.toString();
        } catch (Exception e) {
            log.error("查看状态失败", e);
            throw new GameException(GameException.SYSTEM_ERROR);
        }
    }

    /**
     * 查看地图
     */
    private String viewMap(Command command) {
        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);

            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            // 获取地图详细信息
            String mapDetailInfo = mapManager.getMapDetailInfo(character.getId());

            // 环境扫描
            String environmentInfo = mapManager.scanEnvironment(character);

            // 组合地图信息
            StringBuilder sb = new StringBuilder();
            sb.append(mapDetailInfo);
            sb.append("\n\n");
            sb.append(environmentInfo);

            return sb.toString();
        } catch (Exception e) {
            log.error("查看地图失败", e);
            throw new GameException(GameException.SYSTEM_ERROR);
        }
    }

    /**
     * 查看装备
     */
    private String viewEquipment(Command command) {
        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);

            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            // 获取装备显示信息
            return assetManager.getEquipmentDisplay(character.getId());
        } catch (Exception e) {
            log.error("查看装备失败", e);
            throw new GameException(GameException.SYSTEM_ERROR);
        }
    }

    /**
     * 查看背包
     */
    private String viewInventory(Command command) {
        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);

            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            // 获取背包显示信息
            return assetManager.getInventoryDisplay(character.getId());
        } catch (Exception e) {
            log.error("查看背包失败", e);
            throw new GameException(GameException.SYSTEM_ERROR);
        }
    }

    /**
     * 查看排名
     */
    private String viewRanking(Command command) {
        try {
            // 获取角色信息
            UserCharacter character = getCharacter(command);

            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            // 获取排行榜类型，默认显示等级排行
            String rankType = command.getTarget();
            int type = 1; // 默认等级排行

            if ("战力".equals(rankType) || "zl".equals(rankType)) {
                type = 2;
            } else if ("财富".equals(rankType) || "cf".equals(rankType)) {
                type = 3;
            }

            // 获取排行榜数据
            List<Rank> rankings = rankMapper.selectTopRankings(character.getAreaId(), type, 10);

            // 构建排行榜显示
            StringBuilder sb = new StringBuilder();
            sb.append("╭─────────────────────╮\n");
            sb.append(String.format("🏆 %s排行榜\n", getRankTypeName(type)));
            sb.append("├─────────────────────┤\n");

            if (rankings.isEmpty()) {
                sb.append("📊 暂无排行数据\n");
            } else {
                for (int i = 0; i < rankings.size(); i++) {
                    Rank rank = rankings.get(i);
                    String medal = i < 3 ? new String[]{"🥇", "🥈", "🥉"}[i] : String.format("%2d.", i + 1);
                    sb.append(String.format("%s %s - %s\n",
                        medal,
                        rank.getCharacterName(),
                        formatRankAmount(type, rank.getAmount())));
                }
            }

            sb.append("╰─────────────────────╯\n");
            sb.append("💡 查看其他排行：查看排名 战力/财富");

            return sb.toString();
        } catch (Exception e) {
            log.error("查看排名失败", e);
            throw new GameException(GameException.SYSTEM_ERROR);
        }
    }

    /**
     * 获取排行榜类型名称
     */
    private String getRankTypeName(Integer type) {
        switch (type) {
            case 1:
                return "等级";
            case 2:
                return "战力";
            case 3:
                return "财富";
            default:
                return "未知";
        }
    }

    /**
     * 格式化排行榜数值
     */
    private String formatRankAmount(Integer type, Long amount) {
        switch (type) {
            case 1: // 等级
                return "Lv." + amount;
            case 2: // 战力
                return amount + "战力";
            case 3: // 财富
                return amount + "金币";
            default:
                return amount.toString();
        }
    }

    /**
     * 查看玩家信息
     */
    private String viewPlayerInfo(UserCharacter currentCharacter, Command cmd) {
        try {

            // 这里需要从command.getAtUsers()中获取真实的用户信息
            StringBuilder playerInfo = new StringBuilder();
            playerInfo.append("👤 玩家信息：\n");

            // 尝试查询同区服的玩家
            UserCharacter targetCharacter = playerManager.getCharacterByUser(cmd.getAtUsers().get(0),cmd.getAppId(),cmd.getOpenId(),cmd.getAreaId());

            if (targetCharacter == null || targetCharacter.getGameStatus()==UserCharacter.GAME_STATUS_BANNED || targetCharacter.getGameStatus()==UserCharacter.GAME_STATUS_OFFLINE) {
                playerInfo.append("• 未找到该玩家或玩家不在线\n");
                playerInfo.append("💡 只能查看同区服在线玩家");
                return playerInfo.toString();
            }

            // 检查是否在同一地图（隐私保护）
            Map<String, Object> currentPos = mapManager.getCharacterPosition(currentCharacter.getId());
            Map<String, Object> targetPos = mapManager.getCharacterPosition(targetCharacter.getId());
            String currentMapId = (String) currentPos.get("mapId");
            String targetMapId = (String) targetPos.get("mapId");

            if (!currentMapId.equals(targetMapId)) {
                String roleTypeName = Helper.getRoleTypeName(targetCharacter.getType());
                playerInfo.append("• 大名：").append(targetCharacter.getName()).append("(").append(roleTypeName).append(")\n");
                playerInfo.append("• 状态：不在同一地图\n");
                playerInfo.append("💡 只能查看同地图玩家的详细信息");
                return playerInfo.toString();
            }

            // 显示详细信息
            playerInfo.append("• 大名：").append(targetCharacter.getName()).append("\n");
            playerInfo.append("• 职业：").append(Helper.getRoleTypeName(targetCharacter.getType())).append("\n");
            playerInfo.append("• 等级：").append(targetCharacter.getLevel()).append("级\n");
            playerInfo.append("• 血量：").append(targetCharacter.getHp()).append("/").append(targetCharacter.getMaxHp()).append("\n");
            playerInfo.append("• 法力：").append(targetCharacter.getMp()).append("/").append(targetCharacter.getMaxMp()).append("\n");

            // 获取装备属性加成
            JSONObject equipmentAttributes = assetManager.calculateEquipmentAttributes(targetCharacter.getId());
            int baseAttackW = 10 + targetCharacter.getLevel() * 2;
            int totalAttackW = baseAttackW + equipmentAttributes.getIntValue("attack_w");

            playerInfo.append("• 物攻：").append(totalAttackW).append("\n");
            playerInfo.append("• 状态：").append(targetCharacter.getHp() > 0 ? "正常" : "死亡").append("\n");

            // 获取位置信息
            String mapName = "未知地图";
            try {
                mapName = mapManager.getMapDetail(targetMapId).getName();
            } catch (Exception e) {
                log.warn("获取地图名称失败: mapId={}", targetMapId);
            }
            playerInfo.append("• 位置：").append(mapName).append("\n");
            playerInfo.append("💡 详细属性需要更近距离观察");

            return playerInfo.toString();
        } catch (Exception e) {
            log.error("查看玩家信息失败: atUser={}", cmd.getAtUsers().get(0), e);
            return "👤 玩家信息：\n• 查看失败，请稍后重试";
        }
    }

    /**
     * 查看怪物或NPC信息
     */
    private String viewMonsterOrNpcInfo(UserCharacter character, String target) {
        try {
            StringBuilder targetInfo = new StringBuilder();

            // 尝试解析临时编号
            Integer tempId = null;
            try {
                tempId = Integer.parseInt(target);
            } catch (NumberFormatException e) {
                // 不是数字，可能是名称
            }

            if (tempId != null) {
                // 通过临时编号查询
                MapManager.TempObject tempObject = mapManager.getTempObject(character.getId(), tempId);
                if (tempObject == null) {
                    targetInfo.append("🔍 目标信息：\n");
                    targetInfo.append("• 编号").append(tempId).append("不存在或已过期\n");
                    targetInfo.append("💡 使用'地图'命令重新扫描环境");
                    return targetInfo.toString();
                }

                return formatTempObjectInfo(tempObject);
            } else {
                // 通过名称查询（在当前地图环境中搜索）
                return searchTargetByName(character, target);
            }
        } catch (Exception e) {
            log.error("查看怪物/NPC信息失败: target={}", target, e);
            return "🔍 目标信息：\n• 查看失败，请稍后重试";
        }
    }



    /**
     * 格式化临时对象信息
     */
    private String formatTempObjectInfo(MapManager.TempObject tempObject) {
        StringBuilder info = new StringBuilder();

        switch (tempObject.getType()) {
            case MapManager.TempObject.TYPE_MONSTER:
                info.append("🐉 怪物信息：\n");
                info.append("• 名称：").append(tempObject.getName()).append("\n");
                info.append("• 编号：").append(tempObject.getTempId()).append("\n");
                info.append("• 距离：").append(String.format("%.0f", tempObject.getDistance())).append("米\n");
                info.append("• 状态：").append(tempObject.getStatus()).append("\n");
                info.append("• 位置：(").append(tempObject.getX()).append(",").append(tempObject.getY()).append(")\n");
                info.append("💡 使用'攻击 ").append(tempObject.getTempId()).append("'进行战斗");
                break;

            case MapManager.TempObject.TYPE_NPC:
                info.append("🏪 NPC信息：\n");
                info.append("• 名称：").append(tempObject.getName()).append("\n");
                info.append("• 编号：").append(tempObject.getTempId()).append("\n");
                info.append("• 距离：").append(String.format("%.0f", tempObject.getDistance())).append("米\n");
                info.append("• 状态：").append(tempObject.getStatus()).append("\n");
                info.append("• 位置：(").append(tempObject.getX()).append(",").append(tempObject.getY()).append(")\n");
                info.append("💡 使用'对话 ").append(tempObject.getTempId()).append("'进行交互");
                break;

            case MapManager.TempObject.TYPE_PLAYER:
                info.append("👤 玩家信息：\n");
                info.append("• 名称：").append(tempObject.getName()).append("\n");
                info.append("• 编号：").append(tempObject.getTempId()).append("\n");
                info.append("• 距离：").append(String.format("%.0f", tempObject.getDistance())).append("米\n");
                info.append("• 状态：").append(tempObject.getStatus()).append("\n");
                info.append("• 位置：(").append(tempObject.getX()).append(",").append(tempObject.getY()).append(")\n");
                info.append("💡 使用'@玩家名'查看详细信息");
                break;

            case MapManager.TempObject.TYPE_COLLECT:
                info.append("⛏️ 采集点信息：\n");
                info.append("• 名称：").append(tempObject.getName()).append("\n");
                info.append("• 编号：").append(tempObject.getTempId()).append("\n");
                info.append("• 距离：").append(String.format("%.0f", tempObject.getDistance())).append("米\n");
                info.append("• 状态：").append(tempObject.getStatus()).append("\n");
                info.append("• 位置：(").append(tempObject.getX()).append(",").append(tempObject.getY()).append(")\n");
                info.append("💡 使用'采集 ").append(tempObject.getTempId()).append("'进行采集");
                break;

            default:
                info.append("❓ 未知目标：\n");
                info.append("• 名称：").append(tempObject.getName()).append("\n");
                info.append("• 编号：").append(tempObject.getTempId()).append("\n");
                info.append("• 类型：").append(tempObject.getType()).append("\n");
        }

        return info.toString();
    }

    /**
     * 根据名称搜索目标
     */
    private String searchTargetByName(UserCharacter character, String targetName) {
        StringBuilder info = new StringBuilder();
        info.append("🔍 搜索结果：\n");
        info.append("• 目标名称：").append(targetName).append("\n");
        info.append("• 搜索范围：当前地图\n");
        info.append("• 结果：未找到匹配的目标\n");
        info.append("💡 建议：\n");
        info.append("  - 使用'地图'命令查看环境\n");
        info.append("  - 使用临时编号进行精确查看\n");
        info.append("  - 确认目标名称拼写正确");

        return info.toString();
    }
    /**
     * 查看目标
     */
    private String viewTarget(Command command) {
        try {
            String target = command.getTarget();
            if (target == null || target.isEmpty()) {
                throw new GameException(GameException.OPERATION_ITEM_REQUIRED);
            }

            // 获取角色信息
            UserCharacter character = playerManager.getCharacterByUser(command.getUserId(), command.getAppId(), command.getOpenId(), command.getAreaId());

            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            StringBuilder targetInfo = new StringBuilder();
            //targetInfo.append("👁️ 查看目标：").append(target).append("\n\n");

            if (command.getAtUsers() != null && !command.getAtUsers().isEmpty()) {
                // 查看玩家 - 真实的玩家查询
                targetInfo.append(viewPlayerInfo(character,command));
            } else {
                // 查看怪物或NPC - 真实的怪物/NPC查询
                targetInfo.append(viewMonsterOrNpcInfo(character, target));
            }

            return targetInfo.toString();
        } catch (Exception e) {
            log.error("查看目标失败", e);
            throw new GameException(GameException.SYSTEM_ERROR);
        }
    }
}