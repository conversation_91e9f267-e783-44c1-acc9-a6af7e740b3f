package com.xiziworld.gameserver.command;

import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.CmdConstant;
import com.xiziworld.gameserver.domain.entity.Area;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.config.CommandConfig;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.manager.player.UserCharacterCacheManager;
import com.xiziworld.gameserver.domain.mapper.AreaMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 命令验证器
 *
 * <AUTHOR>
 */
@Component
public class CommandValidator {

    @Autowired
    private PlayerManager playerManager;

    @Autowired
    UserCharacterCacheManager userCharacterCacheManager;

    @Autowired
    private AreaMapper areaMapper;

    /**
     * 验证命令
     */
    public void validate(Command command) {
        // 验证命令基本信息
        validateBasic(command);

        // 验证游戏模式
        validateGameMode(command);

        // 验证游戏分区是否开启和正常
        validateAreaStatus(command);

        // 验证角色状态
        validateCharacterStatus(command);

        // 验证是否挂机
        validateHoldStatus(command);

        // 根据命令类型验证
        switch (command.getType()) {
            case 1: // 查看命令
                validateViewCommand(command);
                break;
            case 2: // 移动命令
                validateMoveCommand(command);
                break;
            case 3: // 战斗命令
                validateBattleCommand(command);
                break;
            case 4: // 采集验证
                break;
            case 5:
                break;
            case 6:// 人物操作
                validateCharacterCommand(command);
                break;
            case 7:
                break;
            case 8:
                break;
            case 10:
                break;
            case 11:
                validateAdminCommand(command);
                break;
        }
    }
    private void validateAreaStatus(Command command) {
        if(command.getType()== CmdConstant.CMD_TYPE_GAME || command.getType()== CmdConstant.CMD_TYPE_ADMIN){
            //进入游戏，也要判断角色是否创建，用到分区信息
            Area area = areaMapper.selectByOpenId(command.getOpenId());
            if(area != null){
                command.setAreaId(area.getId());
            }
            return;
        }
        if (command.getOpenId() == null) {
            throw new GameException(GameException.OPEN_ID_REQUIRED_FOR_AREA);
        }
        Area area = areaMapper.selectByOpenId(command.getOpenId());
        if(area == null){
            throw new GameException(GameException.AREA_NOT_CREATED);
        }
        if(area.getStatus() != 0){
            throw new GameException(GameException.AREA_NOT_OPEN);
        }
        command.setAreaId(area.getId());
    }
    /**
     * 验证是否挂机状态，如果挂机状态，游戏的大部分命令都不能使用
     * @param command
     */
    private void validateHoldStatus(Command command) {
        if(command.getType()== CmdConstant.CMD_TYPE_GAME || command.getType()== CmdConstant.CMD_TYPE_ADMIN){
            return;
        }
        // 查询角色
        UserCharacter character = playerManager.getCharacterByUser(command.getUserId(), command.getAppId(), command.getOpenId(), command.getAreaId());
        if(character == null){
            return;
        }
        if(character.getGameStatus() == UserCharacter.GAME_STATUS_HANGING
                &&!CommandConfig.COMMAND_IDLE_EXIT.equals(command.getCommandName())
                &&!CommandConfig.COMMAND_HELP.equals(command.getCommandName())
                &&!CommandConfig.COMMAND_EXIT_GAME.equals(command.getCommandName())){
            throw new GameException(GameException.COLLECT_IN_PROGRESS);
        }

    }
    /**
     * 检查是否有管理员权限
     * @param command
     */
    private void validateAdminCommand(Command command) {
        if(command.getType()== CmdConstant.CMD_TYPE_ADMIN){
            if(!"fengin".equals(command.getUserId())){
                throw new GameException(GameException.PERMISSION_DENIED);
            }
        }
    }

    /**
     * 验证基本信息
     */
    private void validateBasic(Command command) {
        if (command == null) {
            throw new GameException(GameException.INVALID_PARAMETER);
        }
        if (StringUtils.isBlank(command.getUserId())) {
            throw new GameException(GameException.USER_ID_REQUIRED);
        }
        if (command.getAppId() == null) {
            throw new GameException(GameException.APP_ID_REQUIRED);
        }
        if (StringUtils.isBlank(command.getOpenId())) {
            throw new GameException(GameException.OPEN_ID_REQUIRED);
        }
        if (command.getAreaId() == null) {
            //throw new GameException(GameException.AREA_ID_REQUIRED);
        }
        if (StringUtils.isBlank(command.getAction())) {
            throw new GameException(GameException.INVALID_PARAMETER);
        }
    }

    /**
     * 验证游戏模式
     */
    private void validateGameMode(Command command) {
        // 强命令验证
        if (command.isStrong()) {
            if (command.getAction().equals("游戏模式")) {
                command.setGameMode(true);
                return;
            }
            if (command.getAction().equals("退出游戏模式")) {
                command.setGameMode(false);
                return;
            }
        }

        // 非游戏模式下只能使用强命令
        if (!command.isGameMode() && !command.isStrong() && !CommandConfig.COMMAND_HELP.equals(command.getCommandName())) {
            throw new GameException(GameException.NOT_IN_GAME);
        }
    }

    /**
     * 验证角色状态
     */
    private void validateCharacterStatus(Command command) {
        if(command.getType()== CmdConstant.CMD_TYPE_GAME || command.getType()== CmdConstant.CMD_TYPE_ADMIN){
            return;
        }

        // 查询角色
        UserCharacter character = playerManager.getCharacterByUser(command.getUserId(), command.getAppId(), command.getOpenId(), command.getAreaId());

        // 角色不存在
        if (character == null) {
            //只允许创建角色
            if (!command.getCommandName().equals(CommandConfig.COMMAND_CREATE_CHARACTER)
                    && !command.getCommandName().equals(CommandConfig.COMMAND_ENTER_GAME)
                    && !command.getCommandName().equals(CommandConfig.COMMAND_HELP)) {
                throw new GameException(GameException.CHARACTER_CREATE_REQUIRED);
            }
        }
        // 角色被冻结
        if (character != null && character.getGameStatus() == UserCharacter.GAME_STATUS_BANNED) {
            throw new GameException(GameException.CHARACTER_BANNED);
        }
        // 自动根据群名更新游戏角色名
        if(character!=null && !character.getName().equals(command.getUserName())){
            UserCharacter updateCharacter = new UserCharacter();
            updateCharacter.setId(character.getId());
            updateCharacter.setName(command.getUserName().trim());
            userCharacterCacheManager.updateCharacter(updateCharacter);
        }

    }

    /**
     * 验证查看命令
     */
    private void validateViewCommand(Command command) {
        // 查看他人信息需要@
        if (command.getTarget() != null && command.getAtUsers().isEmpty()) {
            throw new GameException(GameException.CHARACTER_VIEW_AT_REQUIRED);
        }
    }

    /**
     * 验证移动命令
     */
    private void validateMoveCommand(Command command) {
        if (StringUtils.isBlank(command.getTarget())) {
            throw new GameException(GameException.MOVE_TARGET_REQUIRED);
        }
    }

    /**
     * 验证战斗命令
     */
    private void validateBattleCommand(Command command) {
        if (StringUtils.isBlank(command.getTarget()) && !CommandConfig.COMMAND_REVIVE.equals(command.getCommandName())) {
            throw new GameException(GameException.BATTLE_TARGET_REQUIRED);
        }
    }

    /**
     * 验证人物操作
     */
    private void validateCharacterCommand(Command command) {
        if (StringUtils.isBlank(command.getTarget())) {
            throw new GameException(GameException.OPERATION_ITEM_REQUIRED);
        }
    }
} 