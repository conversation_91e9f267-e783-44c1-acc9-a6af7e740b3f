package com.xiziworld.gameserver.schedule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基础定时任务
 *
 * <AUTHOR>
 */
public abstract class BaseSchedule {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 执行定时任务
     */
    protected void execute() {
        try {
            doExecute();
        } catch (Exception e) {
            logger.error("定时任务执行异常", e);
        }
    }

    /**
     * 执行具体任务
     */
    protected abstract void doExecute();
} 