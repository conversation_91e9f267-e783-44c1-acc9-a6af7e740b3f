package com.xiziworld.gameserver.schedule;

import com.xiziworld.gameserver.domain.manager.MapManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 怪物刷新定时任务
 *
 * <AUTHOR>
 */
@Component
public class MonsterRefreshSchedule extends BaseSchedule {

    @Autowired
    private MapManager mapManager;

    /**
     * 每5分钟执行一次怪物刷新
     * 修改为更合理的刷新间隔
     */
    @Scheduled(cron = "0 */5 * * * ?")
    @Override
    protected void execute() {
        super.execute();
    }

    @Override
    protected void doExecute() {
        try {
            logger.info("开始执行怪物刷新定时任务");

            // 刷新所有地图的怪物
            mapManager.refreshAllMapMonsters();

            logger.info("怪物刷新定时任务执行完成");
        } catch (Exception e) {
            logger.error("怪物刷新定时任务执行失败", e);
        }
    }

    /**
     * 手动触发怪物刷新（用于测试或紧急情况）
     */
    public void manualRefresh() {
        logger.info("手动触发怪物刷新");

        try {
            mapManager.refreshAllMapMonsters();
            logger.info("手动怪物刷新完成");
        } catch (Exception e) {
            logger.error("手动怪物刷新失败", e);
        }
    }

    /**
     * 刷新指定地图的怪物
     *
     * @param mapId 地图ID
     */
    public void refreshMapMonsters(String mapId) {
        logger.info("手动刷新地图怪物: mapId={}", mapId);

        try {
            mapManager.refreshMapMonsters(mapId);
            logger.info("地图{}怪物刷新完成", mapId);
        } catch (Exception e) {
            logger.error("地图{}怪物刷新失败", mapId, e);
        }
    }
}