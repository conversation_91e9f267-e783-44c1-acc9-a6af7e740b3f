package com.xiziworld.gameserver.schedule;

import com.xiziworld.gameserver.common.constant.GameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 排行榜更新定时任务
 *
 * <AUTHOR>
 */
@Component
public class RankUpdateSchedule extends BaseSchedule {

    /**
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    @Override
    protected void execute() {
        super.execute();
    }

    @Override
    protected void doExecute() {
        // 更新等级排行

        // 更新战力排行

        // 更新财富排行


        logger.info("执行排行榜更新定时任务");
    }
} 