package com.xiziworld.gameserver.domain.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.common.EventPublisher;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.GameAttributeConstant;
import com.xiziworld.gameserver.common.constant.ItemConstant;
import com.xiziworld.gameserver.domain.entity.UserAsset;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.manager.player.UserCharacterCacheManager;
import com.xiziworld.gameserver.domain.mapper.UserAssetMapper;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.config.UpgradeConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sun.font.EAttribute;

import java.util.*;

/**
 * 物品管理器
 * 负责物品的获得、消耗等
 * 负责装备穿戴、背包管理、升品系统、浣灵系统等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssetManager {
    @Autowired
    private UserCharacterCacheManager userCharacterCacheManager;

    @Autowired
    private UserAssetMapper userAssetMapper;
    
    @Autowired
    private ItemMapper itemMapper;
    
    @Autowired
    private ConfigManager configManager;

    @Autowired
    private EventPublisher eventPublisher;

    // 资产位置常量
    public static final int ASSET_TYPE_EQUIPPED = 1;  // 身上装备
    public static final int ASSET_TYPE_INVENTORY = 2; // 背包
    public static final int ASSET_TYPE_WAREHOUSE = 3; // 仓库


    // ==================== 装备穿戴系统 ====================

    /**
     * 穿戴装备
     *
     * @param character 角色ID
     * @param asset 装备资产
     * @return 穿戴结果信息
     */
    @Transactional
    public String equipEquipment(UserCharacter character,  UserAsset asset) {
        log.info("穿戴装备: characterName={}, assetId={}, itemNo={}", character.getName(), asset.getId(), asset.getItemNo());

        if (asset.getPosition() != ASSET_TYPE_INVENTORY) {
            throw new GameException(GameException.EQUIPMENT_INVENTORY_ONLY);
        }

        // 获取装备详细信息
        Item item = itemMapper.selectByItemNo(asset.getItemNo());
        if (item == null || item.getType() != 0) { // type=0表示装备
            throw new GameException(GameException.NOT_EQUIPMENT);
        }

        // 检查职业限制
        if (item.getRoleLimit() != null && !item.getRoleLimit().equals(character.getType())) {
            throw new GameException(GameException.EQUIPMENT_ROLE_MISMATCH);
        }

        // 检查等级限制
        if (item.getLevelLimit() != null && character.getLevel() < item.getLevelLimit()) {
            throw new GameException(GameException.EQUIPMENT_LEVEL_LIMIT_ERROR + item.getLevelLimit());
        }

        // 获取装备位置（使用subType作为装备位置）
        Integer equipPosition = item.getSubType();
        if (equipPosition == null || equipPosition < 1 || equipPosition > 8) {
            throw new GameException(GameException.EQUIPMENT_POSITION_INVALID);
        }

        // 检查是否已有同位置装备（通过缓存管理器查询）
        UserAsset existingEquip = findEquippedItemByPosition(character.getId(), equipPosition);

        if (existingEquip != null) {
            // 卸下原有装备到背包
            existingEquip.setPosition(ASSET_TYPE_INVENTORY);
            userAssetMapper.updateById(existingEquip);
        }

        // 穿戴新装备
        asset.setPosition(ASSET_TYPE_EQUIPPED);
        userAssetMapper.updateById(asset);

        // 重新计算角色属性
        eventPublisher.publishEvent(EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_ATTR, character.getId());

        String itemName = item.getName();
        String result = "✅ 成功穿戴了" + itemName;

        if (existingEquip != null) {
            Item oldItem = itemMapper.selectByItemNo(existingEquip.getItemNo());
            result += "，原装备" + (oldItem != null ? oldItem.getName() : "未知装备") + "已放入背包";
        }

        return result;
    }

    /**
     * 卸下装备
     *
     * @param characterId 角色ID
     * @param position 装备位置
     * @return 卸下结果信息
     */
    @Transactional
    public String unequipItem(Long characterId, Integer position) {
        log.info("卸下装备: characterId={}, position={}", characterId, position);

        // 检查角色是否存在
        UserCharacter character = userCharacterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 检查位置参数
        if (position == null || position < 1 || position > 8) {
            throw new GameException(GameException.EQUIPMENT_POSITION_ERROR);
        }

        // 查找该位置的装备（通过缓存管理器查询）
        UserAsset equippedAsset = findEquippedItemByPosition(characterId, position);
        if (equippedAsset == null) {
            return "该位置没有装备";
        }

        // 检查背包是否有空位
        if (!hasInventorySpace(characterId)) {
            throw new GameException(GameException.INVENTORY_FULL);
        }

        // 卸下装备到背包
        equippedAsset.setPosition(ASSET_TYPE_INVENTORY);
        userAssetMapper.updateById(equippedAsset);

        // 重新计算角色属性
        eventPublisher.publishEvent(EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_ATTR, character.getId());

        // 获取装备名称
        Item item = itemMapper.selectByItemNo(equippedAsset.getItemNo());
        String itemName = item != null ? item.getName() : "未知装备";

        return "✅ 成功卸下了" + itemName + "，已放入背包";
    }

    // ==================== 资产查询 ====================

    /**
     * 获取背包物品列表
     *
     * @param characterId 角色ID
     * @return 背包物品列表
     */
    public List<UserAsset> getInventoryItems(Long characterId) {
        log.info("获取背包物品: characterId={}", characterId);
        return getCharacterAssetsByPosition(characterId, ASSET_TYPE_INVENTORY);
    }

    /**
     * 获取已装备物品列表
     *
     * @param characterId 角色ID
     * @return 已装备物品列表
     */
    public List<UserAsset> getEquippedItems(Long characterId) {
        log.info("获取已装备物品: characterId={}", characterId);
        return getCharacterAssetsByPosition(characterId, ASSET_TYPE_EQUIPPED);
    }


    /**
     * 检查背包是否有空位
     */
    public boolean hasInventorySpace(Long characterId) {
        List<UserAsset> inventoryItems = getCharacterAssetsByPosition(characterId, ASSET_TYPE_INVENTORY);
        return inventoryItems.size()-2 < 50; // 背包最大50格，排除金币和银两
    }

    /**
     * 获取背包使用情况
     */
    public String getInventoryStatus(Long characterId) {
        List<UserAsset> inventoryItems = getCharacterAssetsByPosition(characterId, ASSET_TYPE_INVENTORY);
        return String.format("背包使用情况：%d/50", inventoryItems.size());
    }

    /**
     * 根据角色ID和位置查询资产列表（通过缓存获取角色信息）
     */
    public List<UserAsset> getCharacterAssetsByPosition(Long characterId, Integer position) {
        return userAssetMapper.selectByUserInfoAndPosition(characterId,position);
    }

    /**
     * 保存物品到仓库
     * @param characterId
     * @param targetAssetId
     * @return String 反馈给用户的保存信息
     */
    @Transactional
    public String moveItemToStorage(Long characterId, Long targetAssetId) {
        log.info("保存物品到仓库: characterId={}, targetAssetId={}", characterId, targetAssetId);

        // 检查角色是否存在
        UserCharacter character = userCharacterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 检查物品是否存在
        UserAsset asset = userAssetMapper.selectById(targetAssetId);
        if (asset == null || asset.getCharacterId() != characterId.longValue()) {
            throw new GameException(GameException.ASSET_NOT_EXISTS);
        }

        // 检查物品是否在背包中
        if (asset.getPosition() != ASSET_TYPE_INVENTORY) {
            throw new GameException(GameException.ITEM_NOT_IN_INVENTORY);
        }

        // 检查是否为装备且已穿戴
        Item item = itemMapper.selectByItemNo(asset.getItemNo());
        if (item != null && item.getType() == 0) {
            // 装备类型，检查是否已穿戴
            if (asset.getPosition() == ASSET_TYPE_EQUIPPED) {
                throw new GameException(GameException.ITEM_NOT_IN_INVENTORY);
            }
        }

        // 移动物品到仓库
        asset.setPosition(ASSET_TYPE_WAREHOUSE);
        userAssetMapper.updateById(asset);

        String itemName = item != null ? item.getName() : asset.getItemNo();
        return String.format("✅ 成功将 %s 存入仓库", itemName);
    }

    /**
     * 从仓库取出物品到背包
     * @param characterId
     * @param targetAssetId
     * @return String 反馈给用户的仓库取出信息
     */
    @Transactional
    public String moveItemFromStorage(Long characterId, Long targetAssetId) {
        log.info("从仓库取出物品: characterId={}, targetAssetId={}", characterId, targetAssetId);

        // 检查角色是否存在
        UserCharacter character = userCharacterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 检查物品是否存在
        UserAsset asset = userAssetMapper.selectById(targetAssetId);
        if (asset == null || asset.getCharacterId() != characterId.longValue()) {
            throw new GameException(GameException.ASSET_NOT_EXISTS);
        }

        // 检查物品是否在仓库中
        if (asset.getPosition() != ASSET_TYPE_WAREHOUSE) {
            throw new GameException(GameException.ITEM_NOT_IN_STORAGE);
        }

        // 检查背包是否有空位
        if (!hasInventorySpace(characterId)) {
            throw new GameException(GameException.INVENTORY_FULL_CANNOT_RETRIEVE);
        }

        // 移动物品到背包
        asset.setPosition(ASSET_TYPE_INVENTORY);
        userAssetMapper.updateById(asset);

        Item item = itemMapper.selectByItemNo(asset.getItemNo());
        String itemName = item != null ? item.getName() : asset.getItemNo();
        return String.format("✅ 成功从仓库取出 %s", itemName);
    }

    /**
     * 获取仓库里的物品信息
     * @param characterId
     * @return  List<UserAsset> 物品信息列表
     */
    public List<UserAsset> getStorageItemInfo(Long characterId) {
        log.info("获取仓库物品: characterId={}", characterId);
        return getCharacterAssetsByPosition(characterId, ASSET_TYPE_WAREHOUSE);
    }
    /**
     * 获取银两数量
     * @param characterId
     * @return int 银两数
     */
    public int getSilverCount(Long characterId) {
        log.debug("获取银两数量: characterId={}", characterId);

        // 查找背包中的银两物品
        UserAsset silverAsset = findInventoryItem(characterId, ItemConstant.ITEM_SILVER);
        return silverAsset != null ? silverAsset.getCount() : 0;
    }
    /**
     * 获取金币数量
     * @param characterId
     * @return int 金币数
     */
    public int getGoldCount(Long characterId) {
        log.debug("获取金币数量: characterId={}", characterId);

        // 查找背包中的金币物品
        UserAsset goldAsset = findInventoryItem(characterId, ItemConstant.ITEM_GOLD);
        return goldAsset != null ? goldAsset.getCount() : 0;
    }

    /**
     * 变更银两数量
     * @param characterId
     * @param count 正数为增加，负数为减少，减少时需要判断背包里银两是否足够
     * @return 是否改变成功，扣减时，如果数量不够扣减，会返回失败
     */
    @Transactional
    public boolean changeSilver(Long characterId, int count) {
        log.info("变更银两数量: characterId={}, count={}", characterId, count);

        if (count == 0) {
            return true; // 无需变更
        }

        // 查找背包中的银两物品
        UserAsset silverAsset = findInventoryItem(characterId, ItemConstant.ITEM_SILVER);

        if (count > 0) {
            // 增加银两
            if (silverAsset != null) {
                // 已有银两，增加数量
                silverAsset.setCount(silverAsset.getCount() + count);
                userAssetMapper.updateById(silverAsset);
            } else {
                // 没有银两，创建新的银两物品
                addItemToInventory(characterId, ItemConstant.ITEM_SILVER, count);
            }
            return true;
        } else {
            // 减少银两
            int reduceCount = Math.abs(count);
            if (silverAsset == null || silverAsset.getCount() < reduceCount) {
                log.warn("银两不足: characterId={}, current={}, need={}", characterId, silverAsset != null ? silverAsset.getCount() : 0, reduceCount);
                return false;
            }

            int newCount = silverAsset.getCount() - reduceCount;
            // 更新银两数量
            silverAsset.setCount(newCount);
            userAssetMapper.updateById(silverAsset);
            return true;
        }
    }

    /**
     * 变更金币数量
     * @param characterId
     * @param count 正数为增加，负数为减少，减少时需要判断背包里金币是否足够
     * @return 是否改变成功，扣减时，如果数量不够扣减，会返回失败
     */
    @Transactional
    public boolean changeGold(Long characterId, int count) {
        log.info("变更金币数量: characterId={}, count={}", characterId, count);

        if (count == 0) {
            return true; // 无需变更
        }

        // 查找背包中的金币物品
        UserAsset goldAsset = findInventoryItem(characterId, ItemConstant.ITEM_GOLD);
        if (count > 0) {
            // 增加金币
            if (goldAsset != null) {
                // 已有金币，增加数量
                goldAsset.setCount(goldAsset.getCount() + count);
                userAssetMapper.updateById(goldAsset);
            } else {
                // 没有金币，创建新的金币物品
                addItemToInventory(characterId, ItemConstant.ITEM_GOLD, count);
            }
            return true;
        } else {
            // 减少金币
            int reduceCount = Math.abs(count);
            if (goldAsset == null || goldAsset.getCount() < reduceCount) {
                log.warn("金币不足: characterId={}, current={}, need={}", characterId, goldAsset != null ? goldAsset.getCount() : 0, reduceCount);
                return false;
            }

            int newCount = goldAsset.getCount() - reduceCount;
            // 更新金币数量
            goldAsset.setCount(newCount);
            userAssetMapper.updateById(goldAsset);
            return true;
        }
    }
    /**
     * 添加物品到背包
     */
    @Transactional
    public void addItemToInventory(Long characterId, String itemNo,int quantity){
        addItemToInventory(characterId, itemNo, null, quantity);
    }
    /**
     * 添加物品到背包
     */
    @Transactional
    public void addItemToInventory(Long characterId, String itemNo, String attributes,int quantity) {
        log.info("添加物品到背包: characterId={}, itemNo={}, quantity={}", characterId, itemNo, quantity);

        UserCharacter character = userCharacterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 检查背包空间
        if (!hasInventorySpace(characterId)) {
            throw new GameException(GameException.INVENTORY_SPACE_INSUFFICIENT);
        }

        // 查找是否已有相同物品
        UserAsset existingAsset = findInventoryItem(characterId, itemNo);
        Item item = itemMapper.selectByItemNo(itemNo);
        if(item==null){
            throw new GameException(GameException.ITEM_NOT_FOUND);
        }
        // 有这个物品，且不是装备
        if (existingAsset != null && item.getType()!=0) {
            // 如果已有相同物品，增加数量
            existingAsset.setCount(existingAsset.getCount() + quantity);
            userAssetMapper.updateById(existingAsset);
        } else {
            // 创建新的物品资产
            UserAsset newAsset = new UserAsset();
            newAsset.setCharacterId(characterId);
            newAsset.setItemNo(itemNo);
            newAsset.setCount(quantity);
            newAsset.setPosition(AssetManager.ASSET_TYPE_INVENTORY);
            newAsset.setAttributes(attributes);
            userAssetMapper.insert(newAsset);
        }
    }
    /**
     * 查找背包中的指定物品
     */
    public UserAsset findInventoryItem(Long characterId, String itemNo) {
        List<UserAsset> assets = getCharacterAssetsByPosition(characterId, AssetManager.ASSET_TYPE_INVENTORY);

        for (UserAsset asset : assets) {
            if (itemNo.equals(asset.getItemNo())) {
                return asset;
            }
        }

        return null;
    }
    // ==================== 属性计算系统 ====================

    /**
     * 计算装备属性加成
     *
     * @param characterId 角色ID
     * @return 装备属性加成
     */
    public JSONObject calculateEquipmentAttributes(Long characterId) {
        log.info("计算装备属性加成: characterId={}", characterId);

        JSONObject totalAttributes = new JSONObject();

        // 初始化所有属性为0
        totalAttributes.put(GameAttributeConstant.HP, 0);
        totalAttributes.put(GameAttributeConstant.MP, 0);
        totalAttributes.put(GameAttributeConstant.PHY_ATK, 0);
        totalAttributes.put(GameAttributeConstant.MAG_ATK, 0);
        totalAttributes.put(GameAttributeConstant.BUD_ATK, 0);
        totalAttributes.put(GameAttributeConstant.PHY_DEF, 0);
        totalAttributes.put(GameAttributeConstant.MAG_DEF, 0);
        totalAttributes.put(GameAttributeConstant.BUD_DEF, 0);
        totalAttributes.put(GameAttributeConstant.REFLECT, 0);
        totalAttributes.put(GameAttributeConstant.CRIT, 0);
        totalAttributes.put(GameAttributeConstant.INNER, 0);

        // 获取已装备的物品（通过缓存管理器）
        List<UserAsset> equippedAssets = getCharacterAssetsByPosition(characterId, ASSET_TYPE_EQUIPPED);

        for (UserAsset asset : equippedAssets) {
            Item item = itemMapper.selectByItemNo(asset.getItemNo());
            if (item != null) {
                // 计算单件装备属性
                JSONObject itemAttributes = calculateSingleItemAttributes(asset, item);

                // 累加到总属性
                Helper.addAttributes(totalAttributes, itemAttributes);
            }
        }

        // 计算套装效果
        JSONObject suitAttributes = calculateSuitEffects(characterId, equippedAssets);
        Helper.addAttributes(totalAttributes, suitAttributes);

        return totalAttributes;
    }


    // ==================== 私有辅助方法 ====================


    /**
     * 计算单件装备属性
     */
    private JSONObject calculateSingleItemAttributes(UserAsset asset, Item item) {
        JSONObject attributes = new JSONObject();
        
        // 解析装备基础属性
        if (item.getAttributes() != null && !item.getAttributes().isEmpty()) {
            JSONObject baseAttributes = JSON.parseObject(item.getAttributes());
            attributes.putAll(baseAttributes);
        }
        
        // 应用品质加成
        Integer quality = Helper.getAssetQuality(asset);
        if (quality != null && quality > 1) {
            double qualityBonus = Helper.calculateQualityBonus(quality);
            Helper.applyQualityBonus(attributes, qualityBonus);
        }
        
        // 应用随机属性（如果有）
        if (asset.getAttributes() != null && !asset.getAttributes().isEmpty()) {
            JSONObject randomAttributes = JSON.parseObject(asset.getAttributes());
            Helper.addAttributes(attributes, randomAttributes);
        }
        
        return attributes;
    }


    /**
     * 计算套装效果
     */
    private JSONObject calculateSuitEffects(Long characterId, List<UserAsset> equippedAssets) {
        JSONObject suitAttributes = new JSONObject();
        
        // TODO: 实现套装效果计算
        // 1. 统计各套装装备数量
        // 2. 根据套装配置计算加成
        // 3. 应用套装效果
        
        // 暂时返回空属性，等SuitConfig完善后实现
        suitAttributes.put(GameAttributeConstant.HP, 0);
        suitAttributes.put(GameAttributeConstant.MP, 0);
        suitAttributes.put(GameAttributeConstant.PHY_ATK, 0);
        suitAttributes.put(GameAttributeConstant.MAG_ATK, 0);
        suitAttributes.put(GameAttributeConstant.BUD_ATK, 0);
        suitAttributes.put(GameAttributeConstant.PHY_DEF, 0);
        suitAttributes.put(GameAttributeConstant.MAG_DEF, 0);
        suitAttributes.put(GameAttributeConstant.BUD_DEF, 0);
        suitAttributes.put(GameAttributeConstant.REFLECT, 0);
        suitAttributes.put(GameAttributeConstant.CRIT, 0);
        suitAttributes.put(GameAttributeConstant.INNER, 0);
        
        return suitAttributes;
    }

    // ==================== 升品系统 ====================

    /**
     * 装备升品
     *
     * @param characterId 角色ID
     * @param assetId 装备资产ID
     * @return 升品结果信息
     */
    @Transactional
    public String upgradeEquipment(Long characterId, Long assetId) {
        log.info("装备升品: characterId={}, assetId={}", characterId, assetId);

        // 检查角色是否存在
        UserCharacter character = userCharacterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 检查装备是否存在
        UserAsset asset = userAssetMapper.selectById(assetId);
        if (asset == null || asset.getCharacterId()!=characterId.longValue()) {
            throw new GameException(GameException.ASSET_NOT_EXISTS);
        }

        // 检查是否为装备
        Item item = itemMapper.selectByItemNo(asset.getItemNo());
        if (item == null || item.getType() != 0) {
            throw new GameException(GameException.NOT_EQUIPMENT);
        }

        // 获取当前品质
        Integer currentQuality = Helper.getAssetQuality(asset);
        if (currentQuality >= 12) {
            throw new GameException(GameException.MAX_QUALITY_REACHED);
        }

        // 获取升品配置
        UpgradeConfig.UpgradeLevel upgradeConfig = getUpgradeConfig(currentQuality);
        if (upgradeConfig == null) {
            throw new GameException(GameException.UPGRADE_CONFIG_ERROR);
        }

        // TODO: 检查材料是否足够
        // checkUpgradeMaterials(characterId, currentQuality, upgradeConfig);

        // 计算成功率
        double successRate = upgradeConfig.getSuccessRate();
        boolean success = Math.random() < successRate;

        if (success) {
            // 升品成功
            Helper.setAssetQuality(asset, currentQuality + 1);
            userAssetMapper.updateById(asset);

            // 如果装备在身上，重新计算属性
            if (asset.getPosition() == ASSET_TYPE_EQUIPPED) {
                eventPublisher.publishEvent(EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_ATTR, character.getId());
            }

            return "✅ 升品成功！" + item.getName() + " 已升级到" + (currentQuality + 1) + "品";
        } else {
            // 升品失败
            return "❌ 升品失败！" + item.getName() + " 品质未变化";
        }
    }

    /**
     * 玉佩浣灵
     *
     * @param characterId 角色ID
     * @param assetId 玉佩资产ID
     * @return 浣灵结果信息
     */
    @Transactional
    public String refineJade(Long characterId, Long assetId) {
        log.info("玉佩浣灵: characterId={}, assetId={}", characterId, assetId);

        // 检查角色是否存在
        UserCharacter character = userCharacterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 检查装备是否存在
        UserAsset asset = userAssetMapper.selectById(assetId);
        if (asset == null || asset.getCharacterId()!=characterId.longValue()) {
            throw new GameException(GameException.ASSET_NOT_EXISTS);
        }

        // 检查是否为玉佩
        Item item = itemMapper.selectByItemNo(asset.getItemNo());
        if (item == null || item.getType() != 0 || item.getSubType() != ItemConstant.POSITION_JADE) {
            throw new GameException(GameException.NOT_JADE);
        }

        // 获取当前品质
        Integer currentQuality = Helper.getAssetQuality(asset);

        // 获取浣灵配置
        UpgradeConfig.RefineLevel refineConfig = getRefineConfig(currentQuality);
        if (refineConfig == null) {
            throw new GameException(GameException.UPGRADE_CONFIG_ERROR);
        }

        // TODO: 检查材料是否足够
        // checkRefineMaterials(characterId, currentQuality, refineConfig);

        // 生成随机属性倍率
        double minRate = refineConfig.getAttributeRange().getMin();
        double maxRate = refineConfig.getAttributeRange().getMax();
        double newRate = minRate + Math.random() * (maxRate - minRate);

        // 更新玉佩属性
        JSONObject attributes;
        if (asset.getAttributes() == null || asset.getAttributes().isEmpty()) {
            attributes = new JSONObject();
        } else {
            attributes = JSON.parseObject(asset.getAttributes());
        }

        attributes.put("plus", newRate);
        asset.setAttributes(attributes.toJSONString());
        userAssetMapper.updateById(asset);

        // 如果玉佩在身上，重新计算属性
        if (asset.getPosition() == ASSET_TYPE_EQUIPPED) {
            eventPublisher.publishEvent(EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_ATTR, character.getId());
        }

        return String.format("✅ 浣灵成功！%s 属性倍率变为 %.3f", item.getName(), newRate);
    }

    // ==================== 升品浣灵辅助方法 ====================

    /**
     * 获取升品配置
     */
    private UpgradeConfig.UpgradeLevel getUpgradeConfig(Integer quality) {
        UpgradeConfig upgradeConfig = configManager.getUpgradeConfig();
        if (upgradeConfig == null || upgradeConfig.getEquipment() == null) {
            return null;
        }

        if (quality <= 3) {
            return upgradeConfig.getEquipment().getLevel13();
        } else if (quality <= 6) {
            return upgradeConfig.getEquipment().getLevel46();
        } else if (quality <= 9) {
            return upgradeConfig.getEquipment().getLevel79();
        } else {
            return upgradeConfig.getEquipment().getLevel10Plus();
        }
    }

    /**
     * 获取浣灵配置
     */
    private UpgradeConfig.RefineLevel getRefineConfig(Integer quality) {
        UpgradeConfig upgradeConfig = configManager.getUpgradeConfig();
        if (upgradeConfig == null || upgradeConfig.getJadeRefine() == null) {
            return null;
        }

        if (quality <= 3) {
            return upgradeConfig.getJadeRefine().getLevel13();
        } else if (quality <= 6) {
            return upgradeConfig.getJadeRefine().getLevel46();
        } else if (quality <= 9) {
            return upgradeConfig.getJadeRefine().getLevel79();
        } else {
            return upgradeConfig.getJadeRefine().getLevel10Plus();
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 查找指定位置的已装备物品
     */
    private UserAsset findEquippedItemByPosition(Long characterId, Integer equipPosition) {
        List<UserAsset> equippedAssets = getCharacterAssetsByPosition(characterId, ASSET_TYPE_EQUIPPED);

        for (UserAsset asset : equippedAssets) {
            Item item = itemMapper.selectByItemNo(asset.getItemNo());
            if (item != null && equipPosition.equals(item.getSubType())) {
                return asset;
            }
        }

        return null;
    }

    // ==================== 显示信息方法 ====================

    /**
     * 获取背包显示信息
     */
    public String getInventoryDisplay(Long characterId) {
        log.info("获取背包显示信息: characterId={}", characterId);

        List<UserAsset> inventoryItems = getInventoryItems(characterId);

        StringBuilder sb = new StringBuilder();
        sb.append("╭─────────────────────╮\n");
        sb.append("🎒 背包物品\n");
        sb.append(String.format("📊 使用情况：%d/50\n", inventoryItems.size()));
        sb.append("├─────────────────────┤\n");
        int gold = 0;
        int silver = 0;
        if (inventoryItems.isEmpty()) {
            sb.append("📦 背包空空如也\n");
        } else {
            int index = 1;
            for (UserAsset asset:inventoryItems) {
                if (ItemConstant.ITEM_GOLD.equals(asset.getItemNo())) {
                    gold = asset.getCount();
                    continue;
                } else if (ItemConstant.ITEM_SILVER.equals(asset.getItemNo())) {
                    silver = asset.getCount();
                    continue;
                }
                Item item = itemMapper.selectByItemNo(asset.getItemNo());
                String itemName = item != null ? item.getName() : asset.getItemNo();
                // 显示品质
                Integer quality = Helper.getAssetQuality(asset);
                String qualityStr = quality != null && quality > 1 ? quality + "品" : "";

                sb.append(String.format("%d. %s%s", index, itemName, qualityStr));
                if (asset.getCount() > 1) {
                    sb.append(String.format(" x%d", asset.getCount()));
                }
                sb.append("\n");
                index++;
            }
        }
        // 显示货币
        sb.append("╰─────────────────────╯\n");
        sb.append(String.format("💰 银两：%d\n", silver));
        sb.append(String.format("🏆 金币：%d\n", gold));
        sb.append("💡 使用：使用 物品名\n");
        sb.append("💡 装备：穿 物品名\n");
        sb.append("💡 存储：存 物品名");

        return sb.toString();
    }

    /**
     * 获取装备显示信息
     */
    public String getEquipmentDisplay(Long characterId) {
        log.info("获取装备显示信息: characterId={}", characterId);

        List<UserAsset> equippedItems = getEquippedItems(characterId);

        StringBuilder sb = new StringBuilder();
        sb.append("╭─────────────────────╮\n");
        sb.append("⚔️ 当前装备\n");
        sb.append("├─────────────────────┤\n");

        for (int slot = 1; slot <= 8; slot++) {
            UserAsset equippedItem = findEquippedItemByPosition(characterId, slot);
            sb.append(String.format("%s: ", ItemConstant.getSlotName(slot)));

            if (equippedItem != null) {
                Item item = itemMapper.selectByItemNo(equippedItem.getItemNo());
                String itemName = item != null ? item.getName() : equippedItem.getItemNo();

                // 显示品质
                Integer quality = Helper.getAssetQuality(equippedItem);
                String qualityStr = quality != null && quality > 1 ? quality + "品" : "";

                sb.append(itemName).append(qualityStr);

                // 显示属性加成
                if (item != null && item.getAttributes() != null) {
                    JSONObject attrs = JSON.parseObject(item.getAttributes());
                    if (!attrs.isEmpty()) {
                        sb.append(" (");
                        boolean first = true;
                        for (String key : attrs.keySet()) {
                            if (!first) sb.append(", ");
                            sb.append(GameAttributeConstant.getDisplayName(key)).append("+").append(attrs.getIntValue(key));
                            first = false;
                        }
                        sb.append(")");
                    }
                }
            } else {
                sb.append("未装备");
            }
            sb.append("\n");
        }

        sb.append("╰─────────────────────╯\n");
        sb.append("💡 卸下：卸下+装备位置");

        return sb.toString();
    }

    /**
     * 获取仓库显示信息
     */
    public String getStorageDisplay(Long characterId) {
        log.info("获取仓库显示信息: characterId={}", characterId);

        List<UserAsset> storageItems = getStorageItemInfo(characterId);

        StringBuilder sb = new StringBuilder();
        sb.append("╭─────────────────────╮\n");
        sb.append("📦 仓库物品\n");
        sb.append(String.format("📊 存储数量：%d\n", storageItems.size()));
        sb.append("├─────────────────────┤\n");

        if (storageItems.isEmpty()) {
            sb.append("📦 仓库空空如也\n");
        } else {
            for (int i = 0; i < storageItems.size(); i++) {
                UserAsset asset = storageItems.get(i);
                Item item = itemMapper.selectByItemNo(asset.getItemNo());
                String itemName = item != null ? item.getName() : asset.getItemNo();

                // 显示品质
                Integer quality = Helper.getAssetQuality(asset);
                String qualityStr = quality != null && quality > 1 ? quality + "品" : "";

                sb.append(String.format("%d. %s%s", i + 1, itemName, qualityStr));
                if (asset.getCount() > 1) {
                    sb.append(String.format(" x%d", asset.getCount()));
                }
                sb.append("\n");
            }
        }

        sb.append("╰─────────────────────╯\n");
        sb.append("💡 取出：取+物品名");

        return sb.toString();
    }
}
