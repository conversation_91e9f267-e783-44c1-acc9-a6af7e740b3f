package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务实体
 *
 * <AUTHOR>
 */
@Data
@TableName("quest")
@EqualsAndHashCode(callSuper = true)
public class Quest extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务类型(1:主线 2:支线 3:日常)
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;

    /**
     * 奖励JSON
     */
    private String rewards;
} 