package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.UserCharacter;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户角色Mapper接口
 *
 * <AUTHOR>
 */
public interface UserCharacterMapper extends BasicMapper<UserCharacter> {

    /**
     * 根据用户信息查询角色
     */
    @Select("SELECT * FROM user_character WHERE user_id = #{userId} AND app_id = #{appId} AND open_id = #{openId} AND area_id = #{areaId}")
    UserCharacter selectByUserInfo(@Param("userId") String userId,
                                   @Param("appId") Integer appId,
                                   @Param("openId") String openId,
                                   @Param("areaId") Long areaId);

    /**
     * 根据区服和角色名查询角色
     */
    @Select("SELECT * FROM user_character WHERE area_id = #{areaId} AND name = #{name}")
    UserCharacter selectByAreaAndName(@Param("areaId") Long areaId,
                                      @Param("name") String name);

    /**
     * 获取所有在线玩家
     */
    @Select("SELECT * FROM user_character WHERE game_status = 1")
    List<UserCharacter> getAllOnlinePlayers();
}