package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 技能实体
 *
 * <AUTHOR>
 */
@Data
@TableName("skill")
@EqualsAndHashCode(callSuper = true)
public class Skill extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 技能编号
     */
    private String skillNo;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型（0-物理攻击，1-魔法攻击，2-佛法攻击，3-物理防御，4-魔法防御，5-佛法防御,6-反伤）
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;

    /**
     * 威力
     */
    private Integer power;

    /**
     * 冷却时间
     */
    private Integer cooldown;

    /**
     * 消耗
     */
    private Integer cost;
} 