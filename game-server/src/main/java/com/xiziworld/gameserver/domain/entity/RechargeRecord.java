package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 充值记录实体
 *
 * <AUTHOR>
 */
@Data
@TableName("recharge_record")
@EqualsAndHashCode(callSuper = true)
public class RechargeRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 应用ID(0-微信)
     */
    private Integer appId;

    /**
     * 应用open_id
     */
    private String openId;

    /**
     * 区服ID
     */
    private Integer areaId;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    /**
     * 银两数量
     */
    private Integer silver;

    /**
     * 状态(0:处理中 1:成功 2:失败)
     */
    private Integer status;
} 