package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 套装配置类
 * 对应 game/suit.yml
 * 
 * <AUTHOR>
 */
@Data
public class SuitConfig {
    
    /**
     * 套装配置映射
     * Key: 套装ID, Value: 套装详细配置
     */
    private Map<String, SuitDetail> suits;
    
    /**
     * 套装系统配置
     */
    private SuitSystem suitSystem;
    
    /**
     * 单个套装的详细配置
     */
    @Data
    public static class SuitDetail {
        /**
         * 套装ID
         */
        private String suitId;
        
        /**
         * 套装名称
         */
        private String name;
        
        /**
         * 套装描述
         */
        private String description;
        
        /**
         * 职业限制
         */
        private Integer roleLimit;
        
        /**
         * 等级范围
         */
        private List<Integer> levelRange;
        
        /**
         * 套装装备列表
         */
        private List<SuitItem> items;
        
        /**
         * 套装效果配置
         * Key: 件数, Value: 套装效果
         */
        private Map<Integer, SetBonus> setBonuses;
    }
    
    /**
     * 套装装备配置
     */
    @Data
    public static class SuitItem {
        /**
         * 物品编号
         */
        private String itemNo;
        
        /**
         * 装备位置
         */
        private Integer position;
    }
    
    /**
     * 套装效果配置
     */
    @Data
    public static class SetBonus {
        /**
         * 效果名称
         */
        private String name;
        
        /**
         * 效果描述
         */
        private String description;
        
        /**
         * 属性加成
         */
        private Map<String, Integer> attributes;
        
        /**
         * 特殊效果
         */
        private Map<String, Double> specialEffects;
    }
    
    /**
     * 套装系统配置
     */
    @Data
    public static class SuitSystem {
        /**
         * 识别配置
         */
        private Recognition recognition;
        
        /**
         * 效果配置
         */
        private Effects effects;
        
        /**
         * 升级配置
         */
        private Upgrade upgrade;
    }
    
    /**
     * 套装识别配置
     */
    @Data
    public static class Recognition {
        /**
         * 是否启用自动识别
         */
        private Boolean autoDetect;
        
        /**
         * 检查间隔（秒）
         */
        private Integer checkInterval;
        
        /**
         * 是否在装备变化时立即检查
         */
        private Boolean checkOnEquipChange;
    }
    
    /**
     * 套装效果配置
     */
    @Data
    public static class Effects {
        /**
         * 是否允许多套装效果叠加
         */
        private Boolean allowMultipleSuits;
        
        /**
         * 优先级规则
         */
        private String priorityRule;
        
        /**
         * 是否显示套装效果提示
         */
        private Boolean showEffectTips;
    }
    
    /**
     * 套装升级配置
     */
    @Data
    public static class Upgrade {
        /**
         * 是否支持套装升级
         */
        private Boolean enabled;
        
        /**
         * 升级材料
         */
        private Map<String, Integer> materials;
        
        /**
         * 升级成功率
         */
        private Double successRate;
        
        /**
         * 升级后属性提升比例
         */
        private Double attributeBonus;
    }
    
    // 套装ID常量
    public static final String SUIT_YOUHU = "youhu_suit";         // 游湖套装
    public static final String SUIT_HUGUANG = "huguang_suit";     // 湖光套装
    public static final String SUIT_YINYUE = "yinyue_suit";       // 印月套装
    public static final String SUIT_YUEWANG = "yuewang_suit";     // 岳王套装
    public static final String SUIT_BAISHE = "baishe_suit";       // 白蛇套装
    public static final String SUIT_LINGYIN = "lingyin_suit";     // 灵隐套装
    public static final String SUIT_LONGWANG = "longwang_suit";   // 龙王套装
    public static final String SUIT_TIANSHI = "tianshi_suit";     // 天师套装
    public static final String SUIT_JINGANG = "jingang_suit";     // 金刚套装
    
    // 装备位置常量
    public static final int POSITION_WEAPON = 1;      // 武器
    public static final int POSITION_HEAD = 2;        // 头部
    public static final int POSITION_BODY = 3;        // 衣服
    public static final int POSITION_HAND = 4;        // 护手
    public static final int POSITION_LEG = 5;         // 护膝
    public static final int POSITION_FOOT = 6;        // 裤子
    public static final int POSITION_SHOE = 7;        // 鞋子
    
    // 职业限制常量（与其他配置保持一致）
    public static final int ROLE_SWORDSMAN = 1;       // 剑客
    public static final int ROLE_MAGE = 2;             // 仙师
    public static final int ROLE_MONK = 3;             // 圣僧
    
    /**
     * 根据套装ID获取套装配置
     */
    public SuitDetail getSuitById(String suitId) {
        return suits.get(suitId);
    }
    
    /**
     * 检查是否为套装装备
     */
    public boolean isSuitItem(String itemNo) {
        return suits.values().stream()
                .anyMatch(suit -> suit.getItems().stream()
                        .anyMatch(item -> item.getItemNo().equals(itemNo)));
    }
    
    /**
     * 根据装备获取所属套装
     */
    public SuitDetail getSuitByItem(String itemNo) {
        return suits.values().stream()
                .filter(suit -> suit.getItems().stream()
                        .anyMatch(item -> item.getItemNo().equals(itemNo)))
                .findFirst()
                .orElse(null);
    }
}
