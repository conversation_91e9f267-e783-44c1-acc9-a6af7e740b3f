package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.Monster;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.cache.impl.PerpetualCache;
import org.apache.ibatis.cache.decorators.LruCache;

import java.util.List;

/**
 * 怪物Mapper接口
 * 使用MyBatis二级缓存，适合相对静态的怪物数据
 *
 * <AUTHOR>
 */
@CacheNamespace(
    implementation = PerpetualCache.class,  // 缓存实现类
    eviction = LruCache.class,              // LRU淘汰策略
    size = 512,                             // 缓存大小（怪物数据相对较少）
    flushInterval = 18000000,                // 300分钟刷新间隔
    readWrite = false                       // 只读缓存，提高性能
)
public interface MonsterMapper extends BasicMapper<Monster> {

    /**
     * 根据怪物编号查询怪物
     */
    @Select("SELECT * FROM monster WHERE monster_no = #{monsterNo}")
    Monster selectByMonsterNo(@Param("monsterNo") String monsterNo);

    /**
     * 根据怪物编号模糊查询怪物列表（用于查找某个地图的怪物实例）
     */
    @Select("SELECT * FROM monster WHERE monster_no LIKE CONCAT(#{monsterNoPrefix}, '%')")
    List<Monster> selectByMonsterNoPrefix(@Param("monsterNoPrefix") String monsterNoPrefix);

    /**
     * 根据怪物类型查询怪物列表
     */
    @Select("SELECT * FROM monster WHERE type = #{type}")
    List<Monster> selectByType(@Param("type") String type);
}