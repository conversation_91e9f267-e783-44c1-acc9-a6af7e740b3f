package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.Rank;
import org.apache.ibatis.annotations.*;

import java.util.List;
import org.apache.ibatis.annotations.Select;

/**
 * 排行榜Mapper接口
 *
 * <AUTHOR>
 */
public interface RankMapper extends BasicMapper<Rank> {

    /**
     * 查询指定类型的排行榜前N名
     *
     * @param areaId 区服ID
     * @param type 排行榜类型 (1:等级排行 2:战力排行 3:财富排行)
     * @param limit 查询数量限制
     * @return 排行榜列表，按数值降序排列
     */
    @Select("SELECT * FROM rank WHERE area_id = #{areaId} AND type = #{type} ORDER BY amount DESC LIMIT #{limit}")
    List<Rank> selectTopRankings(@Param("areaId") Long areaId, @Param("type") Integer type, @Param("limit") Integer limit);

    /**
     * 查询指定用户在指定类型排行榜中的排名
     *
     * @param areaId 区服ID
     * @param characterId 角色ID
     * @param type 排行榜类型
     * @return 用户排名信息，如果不存在则返回null
     */
    @Select("SELECT * FROM rank WHERE area_id = #{areaId} AND character_id = #{characterId} AND type = #{type}")
    Rank selectUserRank(@Param("areaId") Long areaId, @Param("characterId") Long characterId, @Param("type") Integer type);

    /**
     * 更新或插入排行榜记录
     *
     * @param rank 排行榜记录
     */
    @Insert("INSERT INTO rank (area_id, character_id, type, amount, rank, create_time, update_time) " +
            "VALUES (#{areaId}, #{characterId}, #{type}, #{amount}, #{rank}, #{createTime}, #{updateTime}) " +
            "ON DUPLICATE KEY UPDATE " +
            "amount = VALUES(amount), " +
            "rank = VALUES(rank), " +
            "update_time = VALUES(update_time)")
    void insertOrUpdateRank(Rank rank);

    /**
     * 删除指定区服的排行榜记录
     *
     * @param areaId 区服ID
     */
    @Delete("DELETE FROM rank WHERE area_id = #{areaId}")
    void deleteByAreaId(@Param("areaId") Long areaId);

    /**
     * 删除指定用户的排行榜记录
     *
     * @param areaId 区服ID
     * @param characterId 用户ID
     */
    @Delete("DELETE FROM rank WHERE area_id = #{areaId} AND character_id = #{characterId}")
    void deleteByUser(@Param("areaId") Long areaId, @Param("characterId") Long characterId);
}