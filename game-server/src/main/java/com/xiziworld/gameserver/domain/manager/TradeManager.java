package com.xiziworld.gameserver.domain.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.GameAttributeConstant;
import com.xiziworld.gameserver.common.constant.ItemConstant;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.entity.UserAsset;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.manager.config.NpcsConfig;
import com.xiziworld.gameserver.domain.mapper.UserAssetMapper;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.config.ShopsConfig;
import com.xiziworld.gameserver.domain.manager.config.TradeConfig;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 交易管理器
 * 负责NPC交易、玩家交易、商店系统等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class TradeManager {

    @Autowired
    private UserAssetMapper userAssetMapper;
    
    @Autowired
    private ItemMapper itemMapper;
    
    @Autowired
    private ConfigManager configManager;
    
    @Autowired
    private PlayerManager playerManager;

    @Autowired
    private MapManager mapManager;
    
    @Autowired
    private AssetManager assetManager;

    // 交易会话缓存 - 交易ID -> 交易会话
    private final Map<String, TradeSession> tradeSessions = new ConcurrentHashMap<>();

    // 交易号计数器缓存 - 按区域划分 (areaId -> 已使用的交易号集合)
    private final Map<Long, Set<Integer>> tradeIdCounters = new ConcurrentHashMap<>();

    // 交易号锁 - 按区域划分，确保线程安全
    private final Map<Long, Object> tradeIdLocks = new ConcurrentHashMap<>();

    // 交易距离限制
    public static final int TRADE_DISTANCE = 10; // 10米内可交易

    // ==================== NPC交易系统 ====================

    /**
     * 查询NPC商店商品列表
     * 
     * @param characterId 角色ID
     * @param npcId NPC编号
     * @return 商品列表信息
     */
    public String queryNPCShop(Long characterId, String npcId) {
        log.info("查询NPC商店: characterId={}, npcId={}", characterId, npcId);
        
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        
        // 检查距离
        String distanceCheck = mapManager.checkNPCDistance(character, npcId);
        if (distanceCheck != null) {
            return distanceCheck;
        }
        
        // 获取商店配置
        ShopsConfig.ShopDetail shop = getNPCShop(npcId);
        if (shop == null) {
            return "🤷‍♂️ 这位大侠似乎不是商人，没有开设店铺哦！";
        }
        
        StringBuilder result = new StringBuilder();
        result.append("🏪 ").append(shop.getName()).append("\n");
        result.append("📝 ").append(shop.getDescription()).append("\n\n");
        
        if (shop.getItems() == null || shop.getItems().isEmpty()) {
            result.append("😅 掌柜的说：今日货物已售罄，请改日再来！");
        } else {
            result.append("🛍️ 珍宝清单：\n");
            int index = 1;
            for (Map.Entry<String, ShopsConfig.ItemDetail> entry : shop.getItems().entrySet()) {
                String itemNo = entry.getKey();
                ShopsConfig.ItemDetail itemDetail = entry.getValue();
                
                Item item = itemMapper.selectByItemNo(itemNo);
                if (item != null) {
                    String currency = TradeConfig.CURRENCY_SILVER.equals(itemDetail.getCurrency()) ? "银两" : "金币";
                    result.append(String.format("%d. %s - %d%s", 
                        index++, item.getName(), itemDetail.getPrice(), currency));
                    
                    if (itemDetail.getStock() != null && itemDetail.getStock() > 0) {
                        result.append(" 📦(存货:").append(itemDetail.getStock()).append(")");
                    }

                    result.append("\n");
                }
            }

            result.append("\n🛒 购买指令：'买 物品名 数量'");
            result.append("\n💰 出售指令：'卖 物品名 数量'");
        }
        
        return result.toString();
    }

    /**
     * 从NPC购买物品
     * 
     * @param characterId 角色ID
     * @param npcId NPC编号
     * @param itemName 物品名称
     * @param quantity 购买数量
     * @return 购买结果信息
     */
    @Transactional
    public String buyFromNPC(Long characterId, String npcId, String itemName, int quantity) {
        log.info("从NPC购买物品: characterId={}, npcId={}, itemName={}, quantity={}", 
                characterId, npcId, itemName, quantity);
        
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        
        // 检查距离
        String distanceCheck = mapManager.checkNPCDistance(character, npcId);
        if (distanceCheck != null) {
            return distanceCheck;
        }
        
        // 检查数量
        if (quantity <= 0) {
            throw new GameException(GameException.QUANTITY_MUST_POSITIVE);
        }
        
        // 获取商店配置
        ShopsConfig.ShopDetail shop = getNPCShop(npcId);
        if (shop == null) {
            return "🤷‍♂️ 这位大侠似乎不是商人，没有开设店铺哦！";
        }

        // 查找物品
        String itemNo = findItemByName(shop, itemName);
        if (itemNo == null) {
            return "😔 掌柜摇头道：小店没有 " + itemName + " 这等宝物！";
        }
        
        ShopsConfig.ItemDetail itemDetail = shop.getItems().get(itemNo);
        Item item = itemMapper.selectByItemNo(itemNo);
        
        // 检查库存(-1和配置是无限制的)
        if (itemDetail.getStock() != null && itemDetail.getStock()!=-1 && itemDetail.getStock() < quantity) {
            return "😰 掌柜苦笑：存货不够啊！仅剩 " + itemDetail.getStock() + " 件了！";
        }

        // 计算总价
        int totalPrice = itemDetail.getPrice() * quantity;
        String currency = itemDetail.getCurrency();

        // 检查货币是否足够
        if (!hasEnoughCurrency(character, currency, totalPrice)) {
            String currencyName = TradeConfig.CURRENCY_SILVER.equals(currency) ? "银两" : "金币";
            return "💸 囊中羞涩！还需 " + totalPrice + " " + currencyName + " 才能购得此宝！";
        }

        // 检查背包空间
        if (!assetManager.hasInventorySpace(characterId)) {
            return "🎒 行囊已满，先整理一下再来购买吧！";
        }
        
        // 执行购买
        // 扣除货币
        deductCurrency(character, currency, totalPrice);
        
        // 添加物品到背包
        assetManager.addItemToInventory(characterId, itemNo, quantity);
        
        // 更新库存（如果有限制）
        if (itemDetail.getStock() != null && itemDetail.getStock() > 0) {
            itemDetail.setStock(itemDetail.getStock() - quantity);
            // 保存库存更新到配置或数据库, 目前不做限制

        }
        
        String currencyName = TradeConfig.CURRENCY_SILVER.equals(currency) ? "银两" : "金币";
        return String.format("🎉 交易成功！获得 %s x%d，花费 %d %s\n💰 掌柜笑道：多谢大侠惠顾！", item.getName(), quantity, totalPrice, currencyName);
    }

    /**
     * 向NPC出售物品
     * 
     * @param characterId 角色ID
     * @param npcId NPC编号
     * @param itemName 物品名称
     * @param quantity 出售数量
     * @return 出售结果信息
     */
    @Transactional
    public String sellToNPC(Long characterId, String npcId, String itemName, int quantity) {
        log.info("向NPC出售物品: characterId={}, npcId={}, itemName={}, quantity={}", 
                characterId, npcId, itemName, quantity);
        
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        
        // 检查距离
        String distanceCheck = mapManager.checkNPCDistance(character, npcId);
        if (distanceCheck != null) {
            return distanceCheck;
        }
        
        // 检查数量
        if (quantity <= 0) {
            throw new GameException(GameException.QUANTITY_MUST_POSITIVE);
        }
        
        // 获取商店配置
        ShopsConfig.ShopDetail shop = getNPCShop(npcId);
        if (shop == null) {
            return "🙅‍♂️ 这位掌柜不收购物品，另寻他处吧！";
        }

        // 查找玩家背包中的物品
        UserAsset asset = findPlayerItem(characterId, itemName);
        if (asset == null) {
            return "🔍 行囊中没有 " + itemName + " 这等物品！";
        }
        // 检查数量
        if(asset.getCount()<quantity){
            return "📦 行囊中的 " + itemName + " 不足 " + quantity + " 件！";
        }

        // 检查是不是上架玩家市场了，上架玩家市场后不能出售NPC
        if (isAssetInMarket(asset.getId())) {
            return "🚫 此物品已在交易市场上架，请先下架后再出售给商人！";
        }


        Item item = itemMapper.selectByItemNo(asset.getItemNo());
        if (item == null) {
            return "🤔 此物来历不明，无法收购！";
        }
        ShopsConfig.ItemDetail itemDetail = shop.getItems().get(item.getItemNo());
        // 计算出售价格
        int buyPrice = itemDetail.getPrice();
        int totalPrice = (int)(buyPrice * quantity * shop.getSellRate());//商品出售价的折率
        
        // 执行出售
        // 扣除物品
        if (asset.getCount() == quantity) {
            // 全部出售，删除资产
            userAssetMapper.deleteById(asset.getId());
        } else {
            // 部分出售，减少数量
            asset.setCount(asset.getCount() - quantity);
            userAssetMapper.updateById(asset);
        }
        
        // 添加货币
        addCurrency(character, itemDetail.getCurrency(), totalPrice);
        String currencyName = TradeConfig.CURRENCY_SILVER.equals(itemDetail.getCurrency()) ? "银两" : "金币";
        return String.format("💰 出售成功！%s x%d 换得 %d %s\n😊 掌柜满意地点点头！", item.getName(), quantity, totalPrice,currencyName);
    }

    // ==================== 玩家交易系统 ====================
    /**
     * 发布出售物品
     * 
     * @param sellerId 出售角色ID
     * @param itemName 背包里面的物品名字
     * @param quantity 出售数量
     * @param price 出价总价
     * @param currency 货币类型
     * @return 交易发起结果
     */
    @Transactional
    public String publishItem(Long sellerId, String  itemName, int quantity, int price, String currency) {
        log.info("发布上架物品: sellerId={}, itemName={}, quantity={}, price={}, currency={}", sellerId, itemName, quantity, price, currency);
        
        // 检查买家是否存在
        UserCharacter seller = playerManager.getCharacterById(sellerId);
        if (seller == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        UserAsset asset = findPlayerItem(sellerId, itemName);
        if (asset == null) {
            return "🔍 行囊中没有 " + itemName + " 这等宝物！";
        }
        if(asset.getCharacterId()<quantity){
            return "📦 行囊中的 " + itemName + " 不足 " + quantity + " 件！";
        }
        Item item = itemMapper.selectByItemNo(asset.getItemNo());

        // 上架交易物品
        TradeSession session = new TradeSession();
        session.setTradeId(generateTradeId(seller.getAreaId()));
        session.setSellerId(sellerId);
        session.setAssetId(asset.getId());
        session.setItemName(itemName);
        session.setItemNo(asset.getItemNo());
        session.setQuantity(quantity);
        session.setTotalPrice(price);
        session.setCurrency(currency);
        session.setStatus(TradeStatus.PENDING);
        session.setCreateTime(System.currentTimeMillis());
        tradeSessions.put(session.getTradeId(), session);
        StringBuilder result = new StringBuilder();
        result.append("🎺 听好了！听好了！新宝贝上架啦！\n");
        result.append("🏪 有缘的大侠快来交易市场瞧瞧！\n\n");
        result.append("🎁 宝贝名称：").append(itemName);
        if(item.getType()==0){
            // 装备信息显示
            result.append(formatEquipmentInfo(item, asset));
        }
        result.append("\n📦 数量：").append(quantity).append(" 件");
        result.append("\n💰 价格：").append(price).append(" ");
        result.append(TradeConfig.CURRENCY_SILVER.equals(currency) ? "银两" : "金币");
        result.append("\n🛒 购买指令：买 ").append(session.getTradeId()).append(" 数量");
        //返回提示信息
        return result.toString();
    }
    /**
     * 购买上架物品
     *
     * @param buyerId 买家角色ID
     * @param tradeId 交易ID
     * @param quantity 购买数量
     * @return 购买结果
     */
    @Transactional
    public String buyItem(Long buyerId, String tradeId, int quantity) {
        log.info("购买上架物品: buyerId={}, tradeId={}, quantity={}", buyerId, tradeId, quantity);
        // 检查买家是否存在
        UserCharacter buyer = playerManager.getCharacterById(buyerId);
        if (buyer == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        TradeSession session = tradeSessions.get(tradeId);
        if (session == null) {
            return "🤷‍♂️ 此宝物已不在市场，或许已被他人购得！";
        }
        if (session.getStatus() != TradeStatus.PENDING) {
            return "😔 来晚了！此宝物已售罄！";
        }
        if (quantity > session.getQuantity()) {
            return "📦 存货不足！仅剩 " + session.getQuantity() + " 件！";
        }

        if (!hasEnoughCurrency(buyer, session.getCurrency(), session.getPrice() * quantity)) {
            return "💸 囊中羞涩！大侠还是先去赚些银两再来吧！";
        }
        UserAsset asset = userAssetMapper.selectById(session.getAssetId());
        // 扣除货币
        deductCurrency(buyer, session.getCurrency(), session.getPrice() * quantity);
        // 添加物品到背包
        assetManager.addItemToInventory(buyerId, asset.getItemNo(), asset.getAttributes(), quantity);
        // 扣除卖家物品
        UserCharacter seller = playerManager.getCharacterById(session.getSellerId());
        if (seller == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        if (asset.getCount() == quantity) {
            // 全部出售，删除资产
            userAssetMapper.deleteById(asset.getId());
        } else {
            // 部分出售，减少数量
            asset.setCount(asset.getCount() - quantity);
            userAssetMapper.updateById(asset);
        }
        //添加货币到卖家，交10%手续费
        int payment = session.getPrice() * quantity;
        int sellerPrice = (int)(session.getPrice() * quantity * 0.9);
        addCurrency(seller, session.getCurrency(), sellerPrice);

        // 更新交易状态
        session.setQuantity(session.getQuantity() - quantity);
        if(session.getQuantity()<=0){
            // 交易完成，释放交易号
            releaseTradeId(buyer.getAreaId(), tradeId);
            tradeSessions.remove(tradeId);
        }
        // 返回提示信息
        String currencyName = TradeConfig.CURRENCY_SILVER.equals(session.getCurrency()) ? "银两" : "金币";
        return String.format("🎉 交易成功！获得 %s x%d\n💰 花费 %d %s\n🎒 请查看行囊！", session.getItemName(), quantity, payment, currencyName);
    }

    /**
     * 取消交易，下架物品
     * @param characterId
     * @param tradeId
     * @return
     */
    public String cancelTrade(Long characterId,String tradeId) {
        log.info("取消交易: tradeId={}", tradeId);
        TradeSession session = tradeSessions.get(tradeId);
        if (session == null) {
            return "🤷‍♂️ 此交易已不存在！";
        }
        if(!session.getSellerId().equals(characterId)){
            return "🚫 此宝物非你所售，无法取消！";
        }

        // 获取卖家信息以确定区域
        UserCharacter seller = playerManager.getCharacterById(characterId);
        if (seller != null) {
            // 释放交易号
            releaseTradeId(seller.getAreaId(), tradeId);
        }

        tradeSessions.remove(tradeId);
        return "✅ 宝物已下架，物归原主！";
    }

    /**
     * 获取上架交易的物品列表
     * @return
     */
    public String getTradeList() {
        StringBuilder result = new StringBuilder();
        result.append("🏪 ═══ 西湖交易市场 ═══\n\n");

        if (tradeSessions.isEmpty()) {
            result.append("😔 市场空空如也，暂无宝物出售！");
            return result.toString();
        }

        for (TradeSession session : tradeSessions.values()) {
            String currencyName = TradeConfig.CURRENCY_SILVER.equals(session.getCurrency()) ? "银两" : "金币";
            result.append("🆔 编号：").append(session.getTradeId()).append("\n");
            result.append("🎁 宝贝：").append(session.getItemName()).append("\n");
            result.append("📦 数量：").append(session.getQuantity()).append(" 件\n");
            result.append("💰 价格：").append(session.getPrice()).append(" ").append(currencyName).append("\n");
            result.append("🌊 ～～～～～～～～～～～～～～～～\n");
        }
        result.append("📜 查看宝物详情：市场 编号\n");
        result.append("🛒 购买指令：买 编号 数量");
        return result.toString();
    }
    /**
     * 获取上架交易的物品详情
     * @param tradeId
     * @return
     */
    public String getTradeDetail(String tradeId) {
        TradeSession session = tradeSessions.get(tradeId);
        if (session == null) {
            return "🤷‍♂️ 此宝物已不在市场！";
        }
        Item item = itemMapper.selectByItemNo(session.getItemNo());
        StringBuilder result = new StringBuilder();
        String currencyName = TradeConfig.CURRENCY_SILVER.equals(session.getCurrency()) ? "银两" : "金币";

        result.append("🏷️ ═══ 宝物详情 ═══\n");
        result.append("🆔 编号：").append(session.getTradeId()).append("\n");
        result.append("🎁 名称：").append(session.getItemName()).append("\n");

        if(item.getType()==0){
            UserAsset asset = userAssetMapper.selectById(session.getAssetId());
            // 装备信息显示
            result.append(formatEquipmentInfo(item, asset));
        }

        result.append("\n📦 数量：").append(session.getQuantity()).append(" 件");
        result.append("\n💰 价格：").append(session.getPrice()).append(" ").append(currencyName);
        result.append("\n🛒 购买：买 ").append(session.getTradeId()).append(" 数量");

        return result.toString();
    }

    // ==================== 私有辅助方法 ====================
    /**
     * 查找玩家背包中的物品
     */
    private UserAsset findPlayerItem(Long characterId, String itemName) {
        List<UserAsset> inventoryItems = assetManager.getCharacterAssetsByPosition(characterId, AssetManager.ASSET_TYPE_INVENTORY);

        for (UserAsset asset : inventoryItems) {
            Item item = itemMapper.selectByItemNo(asset.getItemNo());
            if (item != null && itemName.equals(item.getName())) {
                return asset;
            }
        }

        return null;
    }
    /**
     * 根据物品名称查找物品编号
     */
    private String findItemByName(ShopsConfig.ShopDetail shop, String itemName) {
        if (shop.getItems() == null) {
            return null;
        }

        for (String itemNo : shop.getItems().keySet()) {
            Item item = itemMapper.selectByItemNo(itemNo);
            if (item != null && itemName.equals(item.getName())) {
                return itemNo;
            }
        }

        return null;
    }



    /**
     * 获取NPC商店配置
     */
    private ShopsConfig.ShopDetail getNPCShop(String npcId) {
        ShopsConfig shopsConfig = configManager.getShopsConfig();
        if (shopsConfig == null || shopsConfig.getShops() == null) {
            return null;
        }
        
        // 根据NPC ID查找对应的商店
        for (ShopsConfig.ShopDetail shop : shopsConfig.getShops().values()) {
            if (npcId.equals(shop.getNpcNo())) {
                return shop;
            }
        }
        
        return null;
    }

    /**
     * 检查是否有足够的货币
     */
    private boolean hasEnoughCurrency(UserCharacter character, String currency, int amount) {
        Map<String, Integer> currencies = getCharacterCurrency(character.getId());
        int currentAmount = currencies.getOrDefault(currency, 0);
        return currentAmount >= amount;
    }

    /**
     * 扣除货币
     */
    @Transactional
    public void deductCurrency(UserCharacter character, String currency, int amount) {
        log.info("扣除货币: characterId={}, currency={}, amount={}", character.getId(), currency, amount);

        // 查找对应的货币资产
        UserAsset currencyAsset = findCurrencyAsset(character.getId(), currency);

        if (currencyAsset == null) {
            // 如果没有货币资产，创建一个（数量为0）
            currencyAsset = createCurrencyAsset(character, currency, 0);
        }

        // 检查数量是否足够
        if (currencyAsset.getCount() < amount) {
            throw new GameException(GameException.CURRENCY_NOT_ENOUGH);
        }

        // 扣除货币
        currencyAsset.setCount(currencyAsset.getCount() - amount);

        if (currencyAsset.getCount() <= 0) {
            // 如果数量为0，删除资产记录
            userAssetMapper.deleteById(currencyAsset.getId());
        } else {
            // 更新数量
            userAssetMapper.updateById(currencyAsset);
        }
    }

    /**
     * 添加货币
     */
    @Transactional
    public void addCurrency(UserCharacter character, String currency, int amount) {
        log.info("添加货币: characterId={}, currency={}, amount={}", character.getId(), currency, amount);

        // 查找对应的货币资产
        UserAsset currencyAsset = findCurrencyAsset(character.getId(), currency);

        if (currencyAsset == null) {
            // 如果没有货币资产，创建一个
            currencyAsset = createCurrencyAsset(character, currency, amount);
        } else {
            // 增加货币数量
            currencyAsset.setCount(currencyAsset.getCount() + amount);
            userAssetMapper.updateById(currencyAsset);
        }
    }

    /**
     * 查找货币资产
     */
    private UserAsset findCurrencyAsset(Long characterId, String currency) {
        List<UserAsset> assets = assetManager.getCharacterAssetsByPosition(characterId, AssetManager.ASSET_TYPE_INVENTORY);

        // 根据货币类型确定物品编号
        String targetItemNo = TradeConfig.CURRENCY_SILVER.equals(currency) ? ItemConstant.ITEM_SILVER : ItemConstant.ITEM_GOLD;;

        for (UserAsset asset : assets) {
            if (targetItemNo.equals(asset.getItemNo())) {
                return asset;
            }
        }

        return null;
    }

    /**
     * 创建货币资产
     */
    private UserAsset createCurrencyAsset(UserCharacter character, String currency, int amount) {
        // 根据货币类型确定物品编号（使用实际的物品编号）
        String itemNo = TradeConfig.CURRENCY_SILVER.equals(currency) ? ItemConstant.ITEM_SILVER : ItemConstant.ITEM_GOLD;

        UserAsset currencyAsset = new UserAsset();
        currencyAsset.setCharacterId(character.getId());
        currencyAsset.setItemNo(itemNo);
        currencyAsset.setCount(amount);
        currencyAsset.setPosition(AssetManager.ASSET_TYPE_INVENTORY);
        currencyAsset.setAttributes("{}");

        userAssetMapper.insert(currencyAsset);
        return currencyAsset;
    }

    // ==================== 交易会话内部类 ====================

    /**
     * 交易会话类
     */
    @Data
    public static class TradeSession {
        private String tradeId;
        private Long buyerId;
        private Long sellerId;
        private Long assetId;
        private String itemName;
        private String itemNo;
        private int quantity;
        private int totalPrice;
        private String currency;
        private TradeStatus status;
        private long createTime;

        public int getPrice() {
            return totalPrice / quantity;
        }
    }

    /**
     * 交易状态枚举
     */
    public enum TradeStatus {
        PENDING,    // 上架售卖中
        COMPLETED  // 已卖完
    }

    // ==================== 货币管理系统 ====================

    /**
     * 获取角色货币信息
     *
     * @param characterId 角色ID
     * @return 货币信息
     */
    public Map<String, Integer> getCharacterCurrency(Long characterId) {
        Map<String, Integer> currencies = new HashMap<>();

        // 查询角色的货币资产（通过缓存管理器）
        List<UserAsset> assets = assetManager.getCharacterAssetsByPosition(characterId, AssetManager.ASSET_TYPE_INVENTORY);

        for (UserAsset asset : assets) {
            // 直接通过物品编号判断货币类型
            if (ItemConstant.ITEM_SILVER.equals(asset.getItemNo())) {
                currencies.put(TradeConfig.CURRENCY_SILVER, asset.getCount());
            } else if (ItemConstant.ITEM_GOLD.equals(asset.getItemNo())) {
                currencies.put(TradeConfig.CURRENCY_GOLD, asset.getCount());
            }
        }

        // 设置默认值
        currencies.putIfAbsent(TradeConfig.CURRENCY_SILVER, 0);
        currencies.putIfAbsent(TradeConfig.CURRENCY_GOLD, 0);

        return currencies;
    }

    /**
     * 货币兑换
     *
     * @param characterId 角色ID
     * @param fromCurrency 源货币类型
     * @param toCurrency 目标货币类型
     * @param amount 兑换数量
     * @return 兑换结果信息
     */
    @Transactional
    public String exchangeCurrency(Long characterId, String fromCurrency, String toCurrency, int amount) {
        log.info("货币兑换: characterId={}, from={}, to={}, amount={}",
                characterId, fromCurrency, toCurrency, amount);

        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 检查兑换数量
        if (amount <= 0) {
            return "🤔 兑换数量必须大于零！";
        }

        // 检查货币类型
        if (!isValidCurrency(fromCurrency) || !isValidCurrency(toCurrency)) {
            return "❌ 钱庄不认识这种货币！";
        }

        if (fromCurrency.equals(toCurrency)) {
            return "😅 同种货币无需兑换！";
        }

        // 计算兑换比例（1金币 = 100银两）
        int exchangeRate = getExchangeRate(fromCurrency, toCurrency);
        int targetAmount = amount * exchangeRate;

        // 检查源货币是否足够
        Map<String, Integer> currencies = getCharacterCurrency(characterId);
        int currentAmount = currencies.getOrDefault(fromCurrency, 0);

        if (currentAmount < amount) {
            String currencyName = getCurrencyName(fromCurrency);
            return "💸 " + currencyName + "不足！当前仅有 " + currentAmount + " " + currencyName;
        }

        // 执行兑换
        deductCurrency(character, fromCurrency, amount);
        addCurrency(character, toCurrency, targetAmount);

        String fromName = getCurrencyName(fromCurrency);
        String toName = getCurrencyName(toCurrency);

        return String.format("💱 兑换成功！消耗 %d %s，获得 %d %s\n😊 钱庄掌柜：承蒙惠顾！",
                amount, fromName, targetAmount, toName);
    }

    /**
     * 获取角色财富信息
     */
    public String getWealthInfo(Long characterId) {
        Map<String, Integer> currencies = getCharacterCurrency(characterId);

        StringBuilder result = new StringBuilder();
        result.append("💰 ═══ 财富一览 ═══\n");
        result.append("🥈 银两：").append(currencies.get(TradeConfig.CURRENCY_SILVER)).append(" 两\n");
        result.append("🥇 金币：").append(currencies.get(TradeConfig.CURRENCY_GOLD)).append(" 枚\n");

        // 计算总价值（以银两为单位）
        int totalValue = currencies.get(TradeConfig.CURRENCY_SILVER) +
                        currencies.get(TradeConfig.CURRENCY_GOLD) * 100;
        result.append("💎 总价值：").append(totalValue).append(" 银两");

        return result.toString();
    }

    // ==================== 交易记录和统计 ====================

    /**
     * 获取交易历史记录
     */
    public String getTradeHistory(Long characterId, int limit) {
        // TODO: 实现交易历史记录查询
        return "📜 交易史册正在整理中，敬请期待！";
    }

    /**
     * 获取交易统计信息
     */
    public Map<String, Object> getTradeStats(Long characterId) {
        Map<String, Object> stats = new HashMap<>();

        // TODO: 实现交易统计
        stats.put("totalTrades", 0);
        stats.put("totalSpent", 0);
        stats.put("totalEarned", 0);
        stats.put("favoriteNPC", "暂无");

        return stats;
    }

    // ==================== 定时任务和清理 ====================


    /**
     * 刷新NPC商店库存（定时任务调用）
     */
    public void refreshNPCShopStock() {
        log.info("刷新NPC商店库存");

        // TODO: 实现商店库存刷新逻辑
        // 1. 重置限购数量
        // 2. 补充库存
        // 3. 更新商品价格（如果有动态价格）
    }

    // ==================== 辅助方法完善 ====================

    /**
     * 检查是否为有效的货币类型
     */
    private boolean isValidCurrency(String currency) {
        return TradeConfig.CURRENCY_SILVER.equals(currency) ||
               TradeConfig.CURRENCY_GOLD.equals(currency);
    }

    /**
     * 获取货币兑换比例
     */
    private int getExchangeRate(String fromCurrency, String toCurrency) {
        if (TradeConfig.CURRENCY_GOLD.equals(fromCurrency) &&
            TradeConfig.CURRENCY_SILVER.equals(toCurrency)) {
            return 100; // 1金币 = 100银两
        } else if (TradeConfig.CURRENCY_SILVER.equals(fromCurrency) &&
                   TradeConfig.CURRENCY_GOLD.equals(toCurrency)) {
            return 1; // 100银两 = 1金币（需要100银两才能换1金币）
        }
        return 1;
    }

    /**
     * 获取货币名称
     */
    private String getCurrencyName(String currency) {
        if (TradeConfig.CURRENCY_SILVER.equals(currency)) {
            return "银两";
        } else if (TradeConfig.CURRENCY_GOLD.equals(currency)) {
            return "金币";
        }
        return "未知货币";
    }

    /**
     * 生成交易ID - 按区域分配简短的交易号
     * @param areaId 区域ID
     * @return 格式为 "T数字" 的交易ID
     */
    private String generateTradeId(Long areaId) {
        int tradeNumber = getNextTradeId(areaId);
        return "T" + tradeNumber;
    }

    /**
     * 获取下一个可用的交易号
     * @param areaId 区域ID
     * @return 可用的交易号
     */
    private int getNextTradeId(Long areaId) {
        Object lock = tradeIdLocks.computeIfAbsent(areaId, k -> new Object());
        synchronized (lock) {
            Set<Integer> usedIds = tradeIdCounters.computeIfAbsent(areaId, k -> new HashSet<>());

            // 查找最小的可用ID（从1开始）
            for (int i = 1; i <= usedIds.size() + 1; i++) {
                if (!usedIds.contains(i)) {
                    usedIds.add(i);
                    log.debug("为区域{}分配交易号: T{}", areaId, i);
                    return i;
                }
            }

            // 理论上不会到达这里，但作为保险
            int nextId = usedIds.size() + 1;
            usedIds.add(nextId);
            log.debug("为区域{}分配交易号: T{}", areaId, nextId);
            return nextId;
        }
    }

    /**
     * 释放交易号，供下次使用
     * @param areaId 区域ID
     * @param tradeId 交易ID
     */
    private void releaseTradeId(Long areaId, String tradeId) {
        if (tradeId == null || !tradeId.startsWith("T")) {
            return;
        }

        try {
            int tradeNumber = Integer.parseInt(tradeId.substring(1));
            Object lock = tradeIdLocks.computeIfAbsent(areaId, k -> new Object());
            synchronized (lock) {
                Set<Integer> usedIds = tradeIdCounters.get(areaId);
                if (usedIds != null) {
                    usedIds.remove(tradeNumber);
                    log.debug("释放区域{}的交易号: T{}", areaId, tradeNumber);
                }
            }
        } catch (NumberFormatException e) {
            log.warn("无法解析交易ID: {}", tradeId);
        }
    }

    // ==================== 公共接口方法 ====================

    /**
     * 获取交易会话
     */
    public TradeSession getTradeSession(String tradeId) {
        return tradeSessions.get(tradeId);
    }

    // ==================== 装备信息显示 ====================

    /**
     * 格式化装备信息显示（基础属性和附加属性分开显示）
     */
    private String formatEquipmentInfo(Item item, UserAsset asset) {
        StringBuilder result = new StringBuilder();

        // 显示品质
        Integer quality = Helper.getAssetQuality(asset);
        if (quality != null && quality > 1) {
            result.append("\n✨ 品质：").append(quality).append("品");
        }

        // 显示基础属性（来自item.attributes）
        if (item.getAttributes() != null && !item.getAttributes().isEmpty()) {
            JSONObject baseAttributes = JSON.parseObject(item.getAttributes());
            if (!baseAttributes.isEmpty()) {
                result.append("\n⚔️ 基础属性：");
                boolean first = true;
                for (String key : baseAttributes.keySet()) {
                    if (!first) result.append("，");
                    result.append(getAttributeDisplayName(key))
                          .append("+").append(baseAttributes.getIntValue(key));
                    first = false;
                }
            }
        }

        // 显示附加属性（来自asset.attributes，排除degree和plus）
        if (asset.getAttributes() != null && !asset.getAttributes().isEmpty()) {
            JSONObject assetAttributes = JSON.parseObject(asset.getAttributes());
            JSONObject additionalAttributes = new JSONObject();

            // 过滤掉degree和plus，只显示真正的附加属性
            for (String key : assetAttributes.keySet()) {
                if (!GameAttributeConstant.DEGREE.equals(key) &&
                    !GameAttributeConstant.PLUS.equals(key)) {
                    additionalAttributes.put(key, assetAttributes.get(key));
                }
            }

            if (!additionalAttributes.isEmpty()) {
                result.append("\n🌟 附加属性：");
                boolean first = true;
                for (String key : additionalAttributes.keySet()) {
                    if (!first) result.append("，");
                    result.append(getAttributeDisplayName(key))
                          .append("+").append(additionalAttributes.getIntValue(key));
                    first = false;
                }
            }
        }

        // 显示玉佩的加成倍率
        if (item.getSubType() != null && item.getSubType() == 8 && // 玉佩位置
            asset.getAttributes() != null && !asset.getAttributes().isEmpty()) {
            JSONObject assetAttributes = JSON.parseObject(asset.getAttributes());
            Double plus = assetAttributes.getDouble(GameAttributeConstant.PLUS);
            if (plus != null && plus > 1.0) {
                result.append("\n💎 属性倍率：").append(String.format("%.3f", plus));
            }
        }

        return result.toString();
    }

    /**
     * 获取属性显示名称
     */
    private String getAttributeDisplayName(String key) {
        switch (key) {
            case GameAttributeConstant.PHY_ATK: return "物攻";
            case GameAttributeConstant.MAG_ATK: return "法攻";
            case GameAttributeConstant.BUD_ATK: return "佛攻";
            case GameAttributeConstant.PHY_DEF: return "物防";
            case GameAttributeConstant.MAG_DEF: return "法防";
            case GameAttributeConstant.BUD_DEF: return "佛防";
            case GameAttributeConstant.HP: return "血量";
            case GameAttributeConstant.MP: return "法力";
            case GameAttributeConstant.REFLECT: return "反伤";
            case GameAttributeConstant.CRIT: return "暴击";
            case GameAttributeConstant.INNER: return "内力";
            default: return key;
        }
    }

    // ==================== 交易号管理 ====================

    /**
     * 清理空闲区域的交易号计数器
     * 定期调用此方法可以释放内存
     */
    public void cleanupTradeIdCounters() {
        Set<Long> activeAreas = new HashSet<>();

        // 收集当前活跃的区域ID
        for (TradeSession session : tradeSessions.values()) {
            UserCharacter seller = playerManager.getCharacterById(session.getSellerId());
            if (seller != null) {
                activeAreas.add(seller.getAreaId());
            }
        }

        // 清理非活跃区域的计数器
        tradeIdCounters.entrySet().removeIf(entry -> {
            Long areaId = entry.getKey();
            Set<Integer> usedIds = entry.getValue();

            // 如果区域不活跃且没有使用中的交易号，则清理
            if (!activeAreas.contains(areaId) && usedIds.isEmpty()) {
                tradeIdLocks.remove(areaId);
                log.debug("清理区域{}的交易号计数器", areaId);
                return true;
            }
            return false;
        });
    }

    /**
     * 获取交易号使用统计信息（用于调试）
     */
    public String getTradeIdStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("📊 交易号使用统计：\n");

        for (Map.Entry<Long, Set<Integer>> entry : tradeIdCounters.entrySet()) {
            Long areaId = entry.getKey();
            Set<Integer> usedIds = entry.getValue();
            stats.append("区域 ").append(areaId).append(": 使用中 ").append(usedIds.size()).append(" 个交易号\n");
            if (!usedIds.isEmpty()) {
                stats.append("  使用的号码: ").append(usedIds).append("\n");
            }
        }

        return stats.toString();
    }

    // ==================== 市场状态检查 ====================

    /**
     * 检查指定资产是否已在交易市场上架
     * @param assetId 资产ID
     * @return true-已上架，false-未上架
     */
    private boolean isAssetInMarket(Long assetId) {
        if (assetId == null) {
            return false;
        }

        // 遍历所有交易会话，检查是否有使用该资产的交易
        for (TradeSession session : tradeSessions.values()) {
            if (session.getStatus() == TradeStatus.PENDING &&
                assetId.equals(session.getAssetId())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取资产在市场中的交易信息
     * @param assetId 资产ID
     * @return 交易会话，如果未上架则返回null
     */
    public TradeSession getAssetTradeSession(Long assetId) {
        if (assetId == null) {
            return null;
        }

        for (TradeSession session : tradeSessions.values()) {
            if (session.getStatus() == TradeStatus.PENDING &&
                assetId.equals(session.getAssetId())) {
                return session;
            }
        }

        return null;
    }

}
