package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 区服实体
 *
 * <AUTHOR>
 */
@Data
@TableName("area")
@EqualsAndHashCode(callSuper = true)
public class Area extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 外部区服识别ID,如微信群聊ID
     */
    private String openId;

    /**
     * 区服名称(区服别名)
     */
    private String areaName;

    /**
     * 应用标识
     */
    private Integer appId;

    /**
     * 区服状态(0:正常 1:维护/关闭)
     */
    private Integer status;
} 