package com.xiziworld.gameserver.domain.manager.config;

import com.xiziworld.gameserver.common.GameException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import javax.annotation.PostConstruct;
import java.io.InputStream;

/**
 * 配置管理器 - 重构版本
 * 使用类型安全的配置对象，提供更好的IDE支持和类型检查
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConfigManager {

    /**
     * -- GETTER --
     *  获取命令配置
     */
    // 配置对象实例
    @Getter
    private CommandConfig commandConfig;
    /**
     * -- GETTER --
     *  获取地图配置
     */
    @Getter
    private MapsConfig mapsConfig;
    /**
     * -- GETTER --
     *  获取NPC配置
     */
    @Getter
    private NpcsConfig npcsConfig;
    /**
     * -- GETTER --
     *  获取商店配置
     */
    @Getter
    private ShopsConfig shopsConfig;
    /**
     * -- GETTER --
     *  获取掉落配置
     */
    @Getter
    private DropsConfig dropsConfig;
    /**
     * -- GETTER --
     *  获取采集配置
     */
    @Getter
    private CollectConfig collectConfig;
    /**
     * -- GETTER --
     *  获取宝箱配置
     */
    @Getter
    private TreasureConfig treasureConfig;
    /**
     * -- GETTER --
     *  获取地图刷怪配置
     */
    @Getter
    private MapRefreshConfig mapRefreshConfig;
    /**
     * -- GETTER --
     *  获取等级配置
     */
    @Getter
    private LevelsConfig levelsConfig;

    /**
     * -- GETTER --
     *  获取新手配置
     */
    @Getter
    private NewbieConfig newbieConfig;
    /**
     * -- GETTER --
     *  获取战斗配置
     */
    @Getter
    private BattleConfig battleConfig;
    /**
     * -- GETTER --
     *  获取升级配置
     */
    @Getter
    private UpgradeConfig upgradeConfig;
    /**
     * -- GETTER --
     *  获取交易配置
     */
    @Getter
    private TradeConfig tradeConfig;
    /**
     * -- GETTER --
     *  获取套装配置
     */
    @Getter
    private SuitConfig suitConfig;
    
    // YAML解析器
    private final Yaml yaml = new Yaml();
    
    /**
     * 初始化配置管理器
     */
    @PostConstruct
    public void init() {
        try {
            log.info("开始初始化配置管理器...");
            
            // 加载所有配置文件
            loadAllConfigs();
            
            // 验证配置完整性
            validateConfigs();

            // 初始化缓存
            initCaches();

            log.info("配置管理器初始化完成");
        } catch (Exception e) {
            log.error("配置管理器初始化失败", e);
            throw new GameException(GameException.CONFIG_LOAD_FAILED);
        }
    }
    
    /**
     * 加载所有配置文件
     */
    private void loadAllConfigs() {
        // 系统配置
        commandConfig = loadConfig("system/command.yml", CommandConfig.class);
        
        // 世界配置
        mapsConfig = loadConfig("world/maps.yml", MapsConfig.class);
        npcsConfig = loadConfig("world/npcs.yml", NpcsConfig.class);
        shopsConfig = loadConfig("world/shops.yml", ShopsConfig.class);
        dropsConfig = loadConfig("world/drops.yml", DropsConfig.class);
        collectConfig = loadConfig("world/collect.yml", CollectConfig.class);
        treasureConfig = loadConfig("world/treasure.yml", TreasureConfig.class);
        mapRefreshConfig = loadConfig("world/map_refresh.yml", MapRefreshConfig.class);
        
        // 游戏配置
        levelsConfig = loadConfig("game/levels.yml", LevelsConfig.class);
        newbieConfig = loadConfig("game/newbie.yml", NewbieConfig.class);
        battleConfig = loadConfig("game/battle.yml", BattleConfig.class);
        upgradeConfig = loadConfig("game/upgrade.yml", UpgradeConfig.class);
        tradeConfig = loadConfig("game/trade.yml", TradeConfig.class);
        suitConfig = loadConfig("game/suit.yml", SuitConfig.class);
    }
    
    /**
     * 加载单个配置文件
     */
    private <T> T loadConfig(String filePath, Class<T> configClass) {
        try {
            String fullPath = "config/" + filePath;
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fullPath);
            
            if (inputStream == null) {
                log.warn("配置文件不存在: {}", fullPath);
                return null;
            }
            
            // 使用指定类型的构造器
            Constructor constructor = new Constructor(configClass);
            Yaml yamlLoader = new Yaml(constructor);
            
            T config = yamlLoader.load(inputStream);
            inputStream.close();
            
            if (config != null) {
                log.debug("加载配置文件: {} -> {}", configClass.getSimpleName(), fullPath);
            } else {
                log.warn("配置文件为空: {}", fullPath);
            }
            
            return config;
        } catch (Exception e) {
            log.error("加载配置文件失败: {} -> {}", configClass.getSimpleName(), filePath, e);
            throw new GameException(GameException.CONFIG_LOAD_FAILED);
        }
    }
    
    /**
     * 验证配置完整性
     */
    private void validateConfigs() {
        int loadedCount = 0;
        
        // 验证必需的配置
        if (commandConfig == null) {
            throw new GameException(GameException.COMMAND_CONFIG_LOAD_FAILED);
        }
        loadedCount++;

        if (mapsConfig == null) {
            throw new GameException(GameException.MAP_CONFIG_LOAD_FAILED);
        }
        loadedCount++;

        if (npcsConfig == null) {
            throw new GameException(GameException.NPC_CONFIG_LOAD_FAILED);
        }
        loadedCount++;

        if (levelsConfig == null) {
            throw new GameException(GameException.LEVEL_CONFIG_LOAD_FAILED);
        }
        loadedCount++;

        if (suitConfig == null) {
            throw new GameException(GameException.SUIT_CONFIG_LOAD_FAILED);
        }
        loadedCount++;
        
        // 统计所有加载的配置
        if (shopsConfig != null) loadedCount++;
        if (dropsConfig != null) loadedCount++;
        if (collectConfig != null) loadedCount++;
        if (treasureConfig != null) loadedCount++;
        if (mapRefreshConfig != null) loadedCount++;
        if (newbieConfig != null) loadedCount++;
        if (battleConfig != null) loadedCount++;
        if (upgradeConfig != null) loadedCount++;
        if (tradeConfig != null) loadedCount++;
        
        log.info("配置验证通过，已加载{}个配置文件", loadedCount);
    }

    /**
     * 初始化缓存
     */
    private void initCaches() {
        try {
            log.info("开始初始化配置缓存...");

            // 初始化等级缓存（经验和属性）
            if (levelsConfig != null) {
                levelsConfig.initLevelCache();
                log.info("等级缓存初始化完成: {}", levelsConfig.getCacheStats());
                log.info("属性缓存统计: {}", levelsConfig.getAttributeCacheStats());
            }

            log.info("配置缓存初始化完成");
        } catch (Exception e) {
            log.error("配置缓存初始化失败", e);
            throw new GameException(GameException.CONFIG_LOAD_FAILED);
        }
    }

    // ==================== 便捷访问方法 ====================
    /**
     * 根据等级计算所需经验值
     */
    public Long calculateLevelExp(int level) {
        return levelsConfig.calculateLevelExp(level);
    }
    
    /**
     * 根据等级获取称号
     */
    public String getTitleByLevel(int level) {
        return levelsConfig.getTitleByLevel(level);
    }
    
    /**
     * 根据职业ID获取基础属性
     */
    public LevelsConfig.BaseAttributes getBaseAttributesByRole(int roleId) {
        String roleConstant = LevelsConfig.getRoleConstant(roleId);
        if (roleConstant == null) {
            return null;
        }
        return levelsConfig.getBaseAttributes().get(roleConstant);
    }
    
    /**
     * 检查是否在新手保护期
     */
    public boolean isInNewbieProtection(int level) {
        return newbieConfig.isInProtectionPeriod(level);
    }
    
    /**
     * 计算职业克制伤害倍率
     */
    public double calculateRestraintDamage(String attackerRole, String defenderRole) {
        return battleConfig.calculateRestraintDamage(attackerRole, defenderRole);
    }
    
    /**
     * 计算交易税收
     */
    public int calculateTradeTax(int tradeValue) {
        return tradeConfig.calculateTax(tradeValue);
    }

    // ==================== 配置管理方法 ====================

    /**
     * 重载配置文件
     */
    public void reloadConfigs() {
        try {
            log.info("开始重载配置文件...");

            // 清空缓存
            clearCaches();

            // 重新加载配置
            loadAllConfigs();

            // 验证配置
            validateConfigs();

            // 初始化缓存
            initCaches();

            log.info("配置文件重载完成");
        } catch (Exception e) {
            log.error("配置文件重载失败", e);
            throw new GameException(GameException.CONFIG_LOAD_FAILED);
        }
    }

    /**
     * 清空所有缓存
     */
    private void clearCaches() {
        if (levelsConfig != null) {
            levelsConfig.clearExpCache();
        }
        log.info("配置缓存已清空");
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        StringBuilder stats = new StringBuilder();

        if (levelsConfig != null) {
            stats.append(levelsConfig.getCacheStats());
        }

        return stats.toString();
    }

    /**
     * 打印经验值范围（调试用）
     */
    public void printExpRange(int startLevel, int endLevel) {
        if (levelsConfig != null) {
            levelsConfig.printExpRange(startLevel, endLevel);
        }
    }
}
