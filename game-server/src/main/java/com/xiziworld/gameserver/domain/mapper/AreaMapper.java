package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.Area;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.cache.decorators.LruCache;
import org.apache.ibatis.cache.impl.PerpetualCache;

import java.util.List;

/**
 * 游戏区服Mapper接口
 * 使用MyBatis二级缓存
 *
 * <AUTHOR>
 */
@CacheNamespace(
    implementation = PerpetualCache.class,  // 缓存实现类
    eviction = LruCache.class,              // LRU淘汰策略
    size = 256,                             // 缓存大小（技能数据较少）
    flushInterval = 180000,                // 3分钟刷新间隔
    readWrite = false                       // 只读缓存，提高性能
)
public interface AreaMapper extends BasicMapper<Area> {
    /**
     * 查询所有区服信息
     */
    @Select("SELECT * FROM area")
    List<Area> selectAll();

    /**
     * 根据区服标识查询区服信息
     */
    @Select("SELECT * FROM area WHERE open_id = #{openId}")
    Area selectByOpenId(@Param("openId") String openId);
}