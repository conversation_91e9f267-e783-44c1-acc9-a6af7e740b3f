package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.Quest;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.cache.impl.PerpetualCache;
import org.apache.ibatis.cache.decorators.LruCache;

import java.util.List;

/**
 * 任务Mapper接口
 * 使用MyBatis二级缓存，适合相对静态的任务数据
 *
 * <AUTHOR>
 */
@CacheNamespace(
    implementation = PerpetualCache.class,  // 缓存实现类
    eviction = LruCache.class,              // LRU淘汰策略
    size = 512,                             // 缓存大小
    flushInterval = 18000000,                // 300分钟刷新间隔
    readWrite = false                       // 只读缓存，提高性能
)
public interface QuestMapper extends BasicMapper<Quest> {

    /**
     * 根据任务类型查询任务列表
     */
    @Select("<script>" +
            "SELECT * FROM quest WHERE 1 = 1 " +
            "<if test='type != null'> AND type = #{type} </if>" +
            "ORDER BY type, id" +
            "</script>")
    List<Quest> selectByType(@Param("type") Integer type);

    /**
     * 根据任务名称查询任务
     */
    @Select("SELECT * FROM quest WHERE name = #{name}")
    Quest selectByName(@Param("name") String name);
}