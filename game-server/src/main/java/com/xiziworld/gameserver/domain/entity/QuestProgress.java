package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务进度实体
 *
 * <AUTHOR>
 */
@Data
@TableName("quest_progress")
@EqualsAndHashCode(callSuper = true)
public class QuestProgress extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 区服ID
     */
    private Long characterId;

    /**
     * 任务ID
     */
    private Long questId;

    /**
     * 进度
     */
    private Integer progress;

    /**
     * 状态(0:进行中 1:已完成 2:已失败)
     */
    private Integer status;
} 