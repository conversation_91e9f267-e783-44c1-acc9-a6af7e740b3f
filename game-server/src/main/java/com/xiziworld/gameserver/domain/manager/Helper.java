package com.xiziworld.gameserver.domain.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.domain.entity.UserAsset;
import com.xiziworld.gameserver.domain.entity.UserCharacter;

import java.util.HashMap;
import java.util.Map;

public class Helper {

    public static Map<String, Object> getCharacterPosition(UserCharacter character){
        JSONObject attributes = JSON.parseObject(character.getPosition());
        Map<String, Object> position = new HashMap<>();
        position.put("mapId", attributes.getString("mapId"));
        position.put("x", attributes.getInteger("x"));
        position.put("y", attributes.getInteger("y"));
        position.put("lastMoveTime", attributes.getLong("lastMoveTime"));
        return position;
    }
    public static String getCurrentMapId(UserCharacter character) {
        return getCharacterPosition(character).get("mapId").toString();
    }
    public static void setCharacterPosition(UserCharacter character, String mapId, int x, int y) {
        JSONObject position = JSON.parseObject(character.getPosition());
        if(position==null){
            position = new JSONObject();
        }
        position.put("mapId", mapId);
        position.put("x", x);
        position.put("y", y);
        position.put("lastMoveTime", System.currentTimeMillis());
        character.setPosition(position.toString());
    }

    /**
     * 根据职业类型获取职业名称
     */
    public static String getRoleTypeName(Integer roleType) {
        switch (roleType) {
            case 1:
                return "剑客";
            case 2:
                return "仙师";
            case 3:
                return "圣僧";
            default:
                return "未知职业";
        }
    }
    /**
     * 从物品编号中提取技能ID
     */
    public static String extractSkillNoFromItem(String itemNo) {
        // 技能书编号格式：ITEM_SKILL_JK_01 -> SKILL_JK_01
        if (itemNo != null && itemNo.startsWith("ITEM_SKILL_")) {
            return itemNo.substring("ITEM_".length());
        }
        return null;
    }
    /**
     * 使用默认参数计算属性成长率（配置获取失败时的兜底方案）
     */
    public static  double calculateAttributeGrowthWithDefaults(Integer level, boolean isHpMp) {
        double totalGrowth = 0.0;

        // 使用默认的等级范围成长参数
        for (int i = 2; i <= level; i++) {
            if (i <= 10) {
                totalGrowth += isHpMp ? 0.20 : 0.08;
            } else if (i <= 20) {
                totalGrowth += isHpMp ? 0.15 : 0.04;
            } else if (i <= 40) {
                totalGrowth += isHpMp ? 0.10 : 0.02;
            } else if (i <= 60) {
                totalGrowth += isHpMp ? 0.05 : 0.015;
            } else {
                totalGrowth += isHpMp ? 0.03 : 0.01;
            }
        }

        return totalGrowth;
    }



    /**
     * 累加属性
     */
    public static void addAttributes(JSONObject target, JSONObject source) {
        for (String key : source.keySet()) {
            Object sourceValue = source.get(key);
            if (sourceValue instanceof Integer) {
                int currentValue = target.getIntValue(key);
                target.put(key, currentValue + (Integer) sourceValue);
            }
        }
    }

    /**
     * 计算品质加成比例
     */
    public static double calculateQualityBonus(Integer quality) {
        if (quality <= 3) {
            return 0.005 * quality; // 1-3品：0.5%每品
        } else if (quality <= 6) {
            return 0.015 + 0.01 * (quality - 3); // 4-6品：1%每品
        } else if (quality <= 9) {
            return 0.045 + 0.02 * (quality - 6); // 7-9品：2%每品
        } else {
            return 0.105 + 0.03 * (quality - 9); // 10品以上：3%每品
        }
    }

    /**
     * 应用品质加成
     */
    public static void applyQualityBonus(JSONObject attributes, double bonus) {
        for (String key : attributes.keySet()) {
            Object value = attributes.get(key);
            if (value instanceof Integer) {
                int intValue = (Integer) value;
                attributes.put(key, (int) (intValue * (1 + bonus)));
            }
        }
    }
    /**
     * 从资产属性中获取品质
     */
    public static Integer getAssetQuality(UserAsset asset) {
        if (asset.getAttributes() == null || asset.getAttributes().isEmpty()) {
            return 1; // 默认1品
        }

        try {
            JSONObject attributes = JSON.parseObject(asset.getAttributes());
            return attributes.getInteger("degree");
        } catch (Exception e) {
            return 1; // 解析失败默认1品
        }
    }

    /**
     * 设置资产品质
     */
    public static void setAssetQuality(UserAsset asset, Integer quality) {
        JSONObject attributes;
        if (asset.getAttributes() == null || asset.getAttributes().isEmpty()) {
            attributes = new JSONObject();
        } else {
            attributes = JSON.parseObject(asset.getAttributes());
        }

        attributes.put("degree", quality);
        asset.setAttributes(attributes.toJSONString());
    }
}
