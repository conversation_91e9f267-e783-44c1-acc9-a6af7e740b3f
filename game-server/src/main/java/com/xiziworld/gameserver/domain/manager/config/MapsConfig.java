package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 地图配置类
 * 对应 world/maps.yml
 * 
 * <AUTHOR>
 */
@Data
public class MapsConfig {
    
    /**
     * 地图配置映射
     * Key: 地图ID, Value: 地图详细配置
     */
    private Map<String, MapDetail> maps;
    
    /**
     * 单个地图的详细配置
     */
    @Data
    public static class MapDetail {
        /**
         * 地图ID
         */
        private String mapId;

        /**
         * 地图名称
         */
        private String name;

        /**
         * 地图描述
         */
        private String description;

        /**
         * 地图坐标
         */
        private Coordinates coordinates;

        /**
         * 地图类型
         */
        private String type;

        /**
         * 是否为副本地图
         */
        private Boolean instance;

        /**
         * 连接的地图列表
         */
        private List<Connection> connections;

        /**
         * 地图特殊属性
         */
        private MapProperties properties;

        /**
         * 环境图片路径
         */
        private String environmentImage;

        /**
         * 主图片路径
         */
        private String mainImage;

        /**
         * 怪物列表
         */
        private List<String> monsters;

        /**
         * NPC列表
         */
        private List<String> npcs;

        /**
         * 采集区域列表
         */
        private List<CollectArea> collectAreas;
    }
    
    /**
     * 地图坐标
     */
    @Data
    public static class Coordinates {
        /**
         * X坐标
         */
        private Integer x;
        
        /**
         * Y坐标
         */
        private Integer y;
    }
    
    /**
     * 地图连接配置
     */
    @Data
    public static class Connection {
        /**
         * 目标地图ID
         */
        private String mapId;
        
        /**
         * 连接方向
         */
        private String direction;
        
        /**
         * 连接描述
         */
        private String description;
    }
    
    /**
     * 地图属性配置
     */
    @Data
    public static class MapProperties {
        /**
         * 是否允许PVP
         */
        private Boolean pvpEnabled;
        
        /**
         * 是否为安全区
         */
        private Boolean safeZone;
        
        /**
         * 最大玩家数量
         */
        private Integer maxPlayers;
        
        /**
         * 地图等级限制
         */
        private LevelRange levelRange;
    }
    
    /**
     * 等级范围
     */
    @Data
    public static class LevelRange {
        /**
         * 最小等级
         */
        private Integer min;

        /**
         * 最大等级
         */
        private Integer max;
    }

    /**
     * 采集区域
     */
    @Data
    public static class CollectArea {
        /**
         * 采集类型
         */
        private String type;
    }

    // 地图ID常量
    public static final String MAP_WULIN = "wulin";           // 武林主城
    public static final String MAP_SUDI = "sudi";             // 苏堤
    public static final String MAP_DUANQIAO = "duanqiao";     // 断桥
    public static final String MAP_GUSHAN = "gushan";         // 孤山
    public static final String MAP_TAIZIWWAN = "taiziwwan";   // 太子湾
    public static final String MAP_BAOSHUSHAN = "baoshushan"; // 保俶山
    public static final String MAP_MEIJIAWU = "meijiawu";     // 梅家坞
    public static final String MAP_LONGJINGCUN = "longjingcun"; // 龙井村
    public static final String MAP_LEIFENGTA = "leifengta";   // 雷峰塔底
    public static final String MAP_HUXINTING = "huxinting";   // 湖心亭
    
    // 地图类型常量
    public static final String TYPE_CITY = "city";           // 城市
    public static final String TYPE_FIELD = "field";         // 野外
    public static final String TYPE_DUNGEON = "dungeon";     // 副本
    
    // 连接方向常量
    public static final String DIRECTION_NORTH = "north";    // 北
    public static final String DIRECTION_SOUTH = "south";    // 南
    public static final String DIRECTION_EAST = "east";      // 东
    public static final String DIRECTION_WEST = "west";      // 西
}
