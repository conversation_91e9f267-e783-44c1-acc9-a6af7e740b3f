package com.xiziworld.gameserver.domain.manager;

import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.manager.config.CollectConfig;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 采集管理器
 * 支持两种采集模式：
 * 1. 手动采集：立即执行，立即返回结果
 * 2. 挂机采集：异步执行，定时返回结果
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CollectManager {

    @Autowired
    private ConfigManager configManager;
    
    @Autowired
    private PlayerManager playerManager;
    
    @Autowired
    private MapManager mapManager;
    
    @Autowired
    private QuestManager questManager;
    
    @Autowired
    private AssetManager assetManager;

    @Autowired
    private ItemMapper itemMapper;

    // 挂机采集状态缓存 - 角色ID -> 挂机采集状态
    private final Map<Long, IdleCollectStatus> idleCollectStatusCache = new ConcurrentHashMap<>();
    
    // 采集冷却缓存 - 角色ID -> 采集类型 -> 冷却结束时间
    private final Map<Long, Map<String, Long>> collectCooldownCache = new ConcurrentHashMap<>();

    // ==================== 手动采集功能（立即执行） ====================

    /**
     * 手动钓鱼 - 立即返回结果
     */
    public String fishing(Long characterId) {
        return executeCollect(characterId, CollectConfig.TYPE_FISHING);
    }

    /**
     * 手动采桑 - 立即返回结果
     */
    public String mulberry(Long characterId) {
        return executeCollect(characterId, CollectConfig.TYPE_MULBERRY);
    }

    /**
     * 手动采茶 - 立即返回结果
     */
    public String tea(Long characterId) {
        return executeCollect(characterId, CollectConfig.TYPE_TEA);
    }

    /**
     * 手动挖矿 - 立即返回结果
     */
    public String mining(Long characterId) {
        return executeCollect(characterId, CollectConfig.TYPE_MINING);
    }

    /**
     * 执行手动采集 - 立即返回结果
     */
    private String executeCollect(Long characterId, String collectType) {
        try {
            // 1. 基础检查
            UserCharacter character = playerManager.getCharacterById(characterId);
            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            if (character.getHp() <= 0) {
                return "💀 阁下已身受重伤，需先疗伤才能" + CollectConfig.getNameByType(collectType) + "啊！";
            }

            // 2. 检查是否在挂机采集中
            if (isIdleCollecting(characterId)) {
                return "🧘‍♂️ 阁下正在挂机修炼中，需先退出挂机才能手动" + CollectConfig.getNameByType(collectType) + "！";
            }

            // 3. 检查冷却时间
            if (isInCooldown(characterId, collectType)) {
                long remainingTime = getCooldownRemainingTime(characterId, collectType);
                return "⏰ " + CollectConfig.getNameByType(collectType) + "需要耐心，请等待" + remainingTime + "秒后再次尝试";
            }

            // 4. 检查地图和位置
            Map<String, Object> position = Helper.getCharacterPosition(character);
            String mapId = position.get("mapId").toString();
            int x = Integer.parseInt(position.get("x").toString());
            int y = Integer.parseInt(position.get("y").toString());
            CollectConfig.CollectLocation location = findCollectLocation(mapId, collectType, x, y);
            if (location == null) {
                return "当前位置无法进行" + CollectConfig.getNameByType(collectType);
            }

            // 5. 立即执行采集并返回结果
            CollectResult result = calculateCollectResults(location, collectType, 1.0); // 手动采集100%效率
            
            // 6. 添加物品到背包
            StringBuilder resultMessage = new StringBuilder();
            String collectName = CollectConfig.getNameByType(collectType);
            String emoji = getCollectEmoji(collectType);
            
            resultMessage.append(emoji).append(" 【").append(collectName).append("完成】\n\n");

            if (result==null) {
                String[] failMessages = {
                    "🌊 江湖路远，此次空手而归，莫要气馁！",
                    "🍃 天意弄人，今日运气欠佳，明日再试！",
                    "⭐ 缘分未到，宝物与阁下擦肩而过！",
                    "🌙 时运不济，但坚持必有收获！"
                };
                Random random = new Random();
                resultMessage.append(failMessages[random.nextInt(failMessages.length)]);
            } else {
                //resultMessage.append("收获：\n");

                try {
                    assetManager.addItemToInventory(characterId, result.getItemNo(), result.getQuantity());
                    resultMessage.append("✨ ").append(result.getItemName())
                               .append(" x").append(result.getQuantity());
                    if (result.isRare()) {
                        resultMessage.append(" 🌟【稀世珍宝】🌟");
                        // TODO: 发送全服稀有物品消息
                    }
                    resultMessage.append("\n");
                } catch (Exception e) {
                    log.warn("添加采集物品失败: itemNo={}, quantity={}", result.getItemNo(), result.getQuantity(), e);
                }
                // 触发任务事件
                questManager.onItemCollected(characterId, collectType, 1);
            }

            // 设置冷却时间（手动采集冷却时间较短）
            setCooldown(characterId, collectType, 5); // 5秒冷却

            log.info("角色完成手动采集: characterId={}, collectType={}, results={}", characterId, collectType, result!=null?result.getItemName():"");

            return resultMessage.toString();

        } catch (Exception e) {
            log.error("手动采集失败: characterId={}, collectType={}", characterId, collectType, e);
            return "采集失败：" + e.getMessage();
        }
    }

    // ==================== 挂机采集功能（异步执行） ====================

    /**
     * 开始挂机钓鱼
     */
    public String startIdleFishing(Long characterId) {
        return startIdleCollect(characterId, CollectConfig.TYPE_FISHING);
    }

    /**
     * 开始挂机采桑
     */
    public String startIdleMulberry(Long characterId) {
        return startIdleCollect(characterId, CollectConfig.TYPE_MULBERRY);
    }

    /**
     * 开始挂机采茶
     */
    public String startIdleTea(Long characterId) {
        return startIdleCollect(characterId, CollectConfig.TYPE_TEA);
    }

    /**
     * 开始挂机挖矿
     */
    public String startIdleMining(Long characterId) {
        return startIdleCollect(characterId, CollectConfig.TYPE_MINING);
    }

    /**
     * 开始挂机采集
     */
    private String startIdleCollect(Long characterId, String collectType) {
        try {
            // 1. 基础检查
            UserCharacter character = playerManager.getCharacterById(characterId);
            if (character == null) {
                throw new GameException(GameException.CHARACTER_NOT_EXISTS);
            }

            if (character.getHp() <= 0) {
                return "💀 阁下身受重伤，需先疗伤才能开始挂机修炼！";
            }

            // 2. 检查是否已在挂机中
            if (isIdleCollecting(characterId)) {
                return "🧘‍♂️ 阁下已在挂机修炼中，专心致志方能有所收获！";
            }

            // 3. 检查地图和位置
            Map<String, Object> position = Helper.getCharacterPosition(character);
            String mapId = position.get("mapId").toString();
            int x = Integer.parseInt(position.get("x").toString());
            int y = Integer.parseInt(position.get("y").toString());
            CollectConfig.CollectLocation location = findCollectLocation(mapId, collectType, x, y);
            if (location == null) {
                return "当前位置无法进行" + CollectConfig.getNameByType(collectType);
            }

            // 4. 创建挂机采集状态
            IdleCollectStatus status = new IdleCollectStatus();
            status.setCharacterId(characterId);
            status.setCollectType(collectType);
            status.setLocation(location);
            status.setStartTime(System.currentTimeMillis());
            status.setLastCollectTime(System.currentTimeMillis());
            status.setMaxDuration(3 * 60 * 60 * 1000L); // 最多3小时
            status.setCollectInterval(getCollectInterval()); // 从配置获取间隔时间
            
            idleCollectStatusCache.put(characterId, status);

            String collectName = CollectConfig.getNameByType(collectType);
            String emoji = getCollectEmoji(collectType);
            
            log.info("角色开始挂机采集: characterId={}, collectType={}", characterId, collectType);

            return emoji + " 【挂机修炼开始】\n" +
                   "🧘‍♂️ 阁下已进入" + collectName + "修炼状态\n" +
                   "⏰ 每" + (status.getCollectInterval() / 1000 / 60) + "分钟自动结算收获\n" +
                   "🕐 最多可修炼3小时，专心致志必有所得！";

        } catch (Exception e) {
            log.error("开始挂机采集失败: characterId={}, collectType={}", characterId, collectType, e);
            return "挂机采集失败：" + e.getMessage();
        }
    }

    /**
     * 退出挂机采集
     */
    public String exitIdleCollect(Long characterId) {
        IdleCollectStatus status = idleCollectStatusCache.remove(characterId);
        if (status == null) {
            return "🤔 阁下当前并未在挂机修炼中！";
        }

        long duration = System.currentTimeMillis() - status.getStartTime();
        String collectName = CollectConfig.getNameByType(status.getCollectType());

        log.info("角色退出挂机采集: characterId={}, collectType={}, duration={}分钟", characterId, status.getCollectType(), duration / 1000 / 60);

        return "🧘‍♂️ 【修炼结束】\n" +
               "✨ 阁下已退出" + collectName + "修炼\n" +
               "⏰ 本次修炼时长：" + (duration / 1000 / 60) + "分钟\n" +
               "🌟 勤能补拙，持之以恒必有大成！";
    }

    /**
     * 定时处理挂机采集结算
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void processIdleCollect() {
        long currentTime = System.currentTimeMillis();
        
        for (Map.Entry<Long, IdleCollectStatus> entry : idleCollectStatusCache.entrySet()) {
            Long characterId = entry.getKey();
            IdleCollectStatus status = entry.getValue();
            
            try {
                // 检查是否超过最大挂机时间
                if (currentTime - status.getStartTime() >= status.getMaxDuration()) {
                    idleCollectStatusCache.remove(characterId);
                    // TODO: 发送消息给玩家：挂机时间已满，自动退出
                    continue;
                }
                
                // 检查是否到了结算时间
                if (currentTime - status.getLastCollectTime() >= status.getCollectInterval()) {
                    processIdleCollectResult(characterId, status);
                    status.setLastCollectTime(currentTime);
                }
                
            } catch (Exception e) {
                log.error("处理挂机采集失败: characterId={}", characterId, e);
            }
        }
    }

    /**
     * 处理挂机采集结果
     */
    private void processIdleCollectResult(Long characterId, IdleCollectStatus status) {
        try {
            // 检查角色状态
            UserCharacter character = playerManager.getCharacterById(characterId);
            if (character == null || character.getHp() <= 0) {
                return; // 角色不存在或已死亡，跳过本次结算
            }

            // 计算挂机采集结果（效率为手动的20%）
            CollectResult result = calculateCollectResults(status.getLocation(), status.getCollectType(), 0.2);

            if (result!=null) {
                StringBuilder resultMessage = new StringBuilder();
                String collectName = CollectConfig.getNameByType(status.getCollectType());
                String emoji = getCollectEmoji(status.getCollectType());

                resultMessage.append(emoji).append(" 【修炼收获】\n");
                resultMessage.append("🧘‍♂️ ").append(collectName).append("修炼有成，获得：\n");

                try {
                    assetManager.addItemToInventory(characterId, result.getItemNo(), result.getQuantity());
                    resultMessage.append("🎁 ").append(result.getItemName())
                               .append(" x").append(result.getQuantity());
                    if (result.isRare()) {
                        resultMessage.append(" 🌟【天赐珍宝】🌟");
                        // TODO: 发送全服稀有物品消息
                    }
                    resultMessage.append("\n");
                } catch (Exception e) {
                    log.warn("添加挂机采集物品失败: itemNo={}, quantity={}", result.getItemNo(), result.getQuantity(), e);
                }

                // TODO: 发送消息给玩家
                log.info("挂机采集结算: characterId={}, collectType={}, result={}", characterId, status.getCollectType(), result.getItemName());

                // 触发任务事件
                questManager.onItemCollected(characterId, status.getCollectType(), 1);
            }

        } catch (Exception e) {
            log.error("处理挂机采集结果失败: characterId={}", characterId, e);
        }
    }

    // ==================== 采集逻辑计算 ====================

    /**
     * 计算采集结果
     */
    private CollectResult calculateCollectResults(CollectConfig.CollectLocation location, String collectType, double efficiency) {
        List<CollectResult> results = new ArrayList<>();

        if (location.getItems() == null) {
            return null;
        }

        // 首先判断是否采集成功
        double successRate = location.getSuccessRate() * efficiency;
        successRate = applyEnvironmentEffects(successRate, collectType);

        Random random = new Random();
        if (random.nextDouble() > successRate) {
            return null; // 采集失败，返回空结果
        }

        // 采集成功，计算获得的物品
        for (CollectConfig.CollectItem item : location.getItems()) {
            if (random.nextDouble() <= item.getRate() * efficiency) {
                CollectResult result = new CollectResult();
                result.setItemNo(item.getItemNo());
                result.setQuantity(calculateItemQuantity(item));
                result.setItemName(getItemName(item.getItemNo()));
                result.setRare(isRareItem(item.getRate()));
                return result;
            }
        }
        return null;
    }

    /**
     * 应用环境效果（天气、时间段等）
     */
    private double applyEnvironmentEffects(double baseRate, String collectType) {
        CollectConfig collectConfig = configManager.getCollectConfig();
        CollectConfig.GlobalCollectConfig globalConfig = collectConfig.getGlobalCollectConfig();

        if (globalConfig == null) {
            return baseRate;
        }

        double finalRate = baseRate;

        // 应用时间段效果
        if (globalConfig.getTimeEffects() != null) {
            String timeSlot = getCurrentTimeSlot();
            Double timeEffect = globalConfig.getTimeEffects().get(timeSlot);
            if (timeEffect != null) {
                finalRate *= timeEffect;
            }
        }

        // 应用天气效果（这里简化处理，实际可以从天气系统获取）
        if (globalConfig.getWeatherEffects() != null) {
            String weather = getCurrentWeather(); // 简化为晴天
            Double weatherEffect = globalConfig.getWeatherEffects().get(weather);
            if (weatherEffect != null) {
                finalRate *= weatherEffect;
            }
        }

        return Math.min(1.0, finalRate); // 确保不超过100%
    }

    /**
     * 查找采集地点
     */
    private CollectConfig.CollectLocation findCollectLocation(String mapId, String collectType, int playerX, int playerY) {
        CollectConfig collectConfig = configManager.getCollectConfig();
        CollectConfig.CollectType type = collectConfig.getCollectTypes().get(collectType);

        if (type == null || type.getLocations() == null) {
            return null;
        }

        for (CollectConfig.CollectLocation location : type.getLocations()) {
            if (location.getMap().equals(mapId)) {
                // 检查玩家是否在采集范围内
                if (isInRange(playerX, playerY, location)) {
                    return location;
                }
            }
        }

        return null;
    }

    /**
     * 检查是否在采集范围内
     */
    private boolean isInRange(int playerX, int playerY, CollectConfig.CollectLocation location) {
        if (location.getXRange() == null || location.getYRange() == null) {
            return true; // 如果没有配置范围，默认允许
        }

        int minX = location.getXRange().get(0);
        int maxX = location.getXRange().get(1);
        int minY = location.getYRange().get(0);
        int maxY = location.getYRange().get(1);

        return playerX >= minX && playerX <= maxX && playerY >= minY && playerY <= maxY;
    }

    // ==================== 工具方法 ====================

    /**
     * 检查是否在挂机采集中
     */
    public boolean isIdleCollecting(Long characterId) {
        return idleCollectStatusCache.containsKey(characterId);
    }

    /**
     * 检查冷却时间
     */
    private boolean isInCooldown(Long characterId, String collectType) {
        Map<String, Long> cooldowns = collectCooldownCache.get(characterId);
        if (cooldowns == null) {
            return false;
        }

        Long cooldownEndTime = cooldowns.get(collectType);
        if (cooldownEndTime == null) {
            return false;
        }

        return System.currentTimeMillis() < cooldownEndTime;
    }

    /**
     * 获取冷却剩余时间（秒）
     */
    private long getCooldownRemainingTime(Long characterId, String collectType) {
        Map<String, Long> cooldowns = collectCooldownCache.get(characterId);
        if (cooldowns == null) {
            return 0;
        }

        Long cooldownEndTime = cooldowns.get(collectType);
        if (cooldownEndTime == null) {
            return 0;
        }

        long remaining = (cooldownEndTime - System.currentTimeMillis()) / 1000;
        return Math.max(0, remaining);
    }

    /**
     * 设置冷却时间
     */
    private void setCooldown(Long characterId, String collectType, int cooldownSeconds) {
        Map<String, Long> cooldowns = collectCooldownCache.computeIfAbsent(characterId, k -> new ConcurrentHashMap<>());
        cooldowns.put(collectType, System.currentTimeMillis() + cooldownSeconds * 1000L);
    }

    /**
     * 获取当前时间段
     */
    private String getCurrentTimeSlot() {
        LocalTime now = LocalTime.now();
        int hour = now.getHour();

        if (hour >= 5 && hour < 7) {
            return "dawn";
        } else if (hour >= 7 && hour < 12) {
            return "morning";
        } else if (hour >= 12 && hour < 18) {
            return "afternoon";
        } else if (hour >= 18 && hour < 20) {
            return "evening";
        } else {
            return "night";
        }
    }

    /**
     * 获取当前天气（简化实现）
     */
    private String getCurrentWeather() {
        return "sunny"; // 简化为晴天，实际可以接入天气系统
    }

    /**
     * 计算物品数量
     */
    private int calculateItemQuantity(CollectConfig.CollectItem item) {
        if (item.getCount() != null) {
            return item.getCount();
        }

        if (item.getMin() != null && item.getMax() != null) {
            Random random = new Random();
            return random.nextInt(item.getMax() - item.getMin() + 1) + item.getMin();
        }

        return 1; // 默认数量
    }

    /**
     * 判断是否为稀有物品
     */
    private boolean isRareItem(double rate) {
        return rate <= 0.05; // 概率小于等于5%的物品视为稀有
    }

    /**
     * 获取采集间隔时间（毫秒）
     */
    private long getCollectInterval() {
        // 从配置获取，默认10分钟
        return 10 * 60 * 1000L;
    }

    /**
     * 获取物品名称
     */
    private String getItemName(String itemNo) {
        switch (itemNo) {
            case CollectConfig.ITEM_FISH_01: return "白条";
            case CollectConfig.ITEM_FISH_02: return "鲫鱼";
            case CollectConfig.ITEM_FISH_03: return "鲤鱼";
            case CollectConfig.ITEM_FISH_04: return "鳜鱼";
            case CollectConfig.ITEM_FISH_05: return "鳗鱼";
            case CollectConfig.ITEM_MULBERRY: return "桑叶";
            case CollectConfig.ITEM_SILK: return "天蚕丝";
            case CollectConfig.ITEM_TEA: return "茶叶";
            case CollectConfig.ITEM_TEA_SPECIAL: return "龙井仙露";
            case CollectConfig.ITEM_STONE: return "雨花石";
            case CollectConfig.ITEM_METEOR: return "天外陨石";
            case "ITEM_DRUG_HP_01": return "回气丹";
            case "ITEM_DRUG_MP_01": return "回法丹";
            case "ITEM_DRUG_SP_02": return "灵芝仙草";
            default: return getItemNameByNo(itemNo);
        }
    }
    private String getItemNameByNo(String itemNo) {
        Item item = itemMapper.selectByItemNo(itemNo);
        if(item==null){
            return "未知宝物";
        }
        return item.getName();
    }

    /**
     * 获取采集表情符号
     */
    private String getCollectEmoji(String collectType) {
        switch (collectType) {
            case CollectConfig.TYPE_FISHING: return "🎣";
            case CollectConfig.TYPE_MULBERRY: return "🌿";
            case CollectConfig.TYPE_TEA: return "🍃";
            case CollectConfig.TYPE_MINING: return "⛏️";
            default: return "📦";
        }
    }

    // ==================== 内部类 ====================

    /**
     * 挂机采集状态
     */
    public static class IdleCollectStatus {
        private Long characterId;
        private String collectType;
        private CollectConfig.CollectLocation location;
        private long startTime;
        private long lastCollectTime;
        private long maxDuration;
        private long collectInterval;

        // Getters and Setters
        public Long getCharacterId() { return characterId; }
        public void setCharacterId(Long characterId) { this.characterId = characterId; }
        public String getCollectType() { return collectType; }
        public void setCollectType(String collectType) { this.collectType = collectType; }
        public CollectConfig.CollectLocation getLocation() { return location; }
        public void setLocation(CollectConfig.CollectLocation location) { this.location = location; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getLastCollectTime() { return lastCollectTime; }
        public void setLastCollectTime(long lastCollectTime) { this.lastCollectTime = lastCollectTime; }
        public long getMaxDuration() { return maxDuration; }
        public void setMaxDuration(long maxDuration) { this.maxDuration = maxDuration; }
        public long getCollectInterval() { return collectInterval; }
        public void setCollectInterval(long collectInterval) { this.collectInterval = collectInterval; }
    }

    /**
     * 采集结果
     */
    public static class CollectResult {
        private String itemNo;
        private String itemName;
        private int quantity;
        private boolean rare;

        // Getters and Setters
        public String getItemNo() { return itemNo; }
        public void setItemNo(String itemNo) { this.itemNo = itemNo; }
        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }
        public int getQuantity() { return quantity; }
        public void setQuantity(int quantity) { this.quantity = quantity; }
        public boolean isRare() { return rare; }
        public void setRare(boolean rare) { this.rare = rare; }
    }
}
