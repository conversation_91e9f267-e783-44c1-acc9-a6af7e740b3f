package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;

/**
 * 交易配置类
 * 对应 game/trade.yml
 * 
 * <AUTHOR>
 */
@Data
public class TradeConfig {
    
    /**
     * 交易税收配置
     */
    private Tax tax;
    
    /**
     * 交易限制配置
     */
    private Limits limits;
    
    /**
     * 安全机制配置
     */
    private Security security;
    
    /**
     * 交易税收配置
     */
    @Data
    public static class Tax {
        /**
         * 税收比例
         */
        private Double rate;
        
        /**
         * 最小税收金额
         */
        private Integer minAmount;
        
        /**
         * 最大税收金额
         */
        private Integer maxAmount;
        
        /**
         * 税收货币类型
         */
        private String currency;
    }
    
    /**
     * 交易限制配置
     */
    @Data
    public static class Limits {
        /**
         * 单次交易最大物品数量
         */
        private Integer maxItems;
        
        /**
         * 单次交易最大价值
         */
        private Integer maxValue;
        
        /**
         * 交易超时时间（秒）
         */
        private Integer timeout;
        
        /**
         * 最小交易等级
         */
        private Integer minLevel;
    }
    
    /**
     * 安全机制配置
     */
    @Data
    public static class Security {
        /**
         * 确认时间（秒）
         */
        private Integer confirmTime;
        
        /**
         * 是否需要二次确认
         */
        private Boolean doubleConfirm;
    }
    
    // 交易相关常量
    public static final double DEFAULT_TAX_RATE = 0.1;         // 默认税收比例10%
    public static final int DEFAULT_MIN_TAX = 10;              // 默认最小税收
    public static final int DEFAULT_MAX_TAX = 10000;           // 默认最大税收
    public static final String DEFAULT_CURRENCY = "silver";    // 默认货币类型
    
    public static final int DEFAULT_MAX_ITEMS = 10;            // 默认最大物品数量
    public static final int DEFAULT_MAX_VALUE = 1000000;       // 默认最大交易价值
    public static final int DEFAULT_TIMEOUT = 300;             // 默认超时时间（5分钟）
    public static final int DEFAULT_MIN_LEVEL = 5;             // 默认最小交易等级
    
    public static final int DEFAULT_CONFIRM_TIME = 10;         // 默认确认时间（10秒）
    public static final boolean DEFAULT_DOUBLE_CONFIRM = true; // 默认需要二次确认
    
    // 货币类型常量
    public static final String CURRENCY_SILVER = "silver";     // 银两
    public static final String CURRENCY_GOLD = "gold";         // 金币
    
    /**
     * 计算交易税收
     */
    public int calculateTax(int tradeValue) {
        if (tax == null) {
            return 0;
        }
        
        int taxAmount = (int) (tradeValue * tax.getRate());
        
        // 应用最小和最大税收限制
        if (tax.getMinAmount() != null) {
            taxAmount = Math.max(taxAmount, tax.getMinAmount());
        }
        
        if (tax.getMaxAmount() != null) {
            taxAmount = Math.min(taxAmount, tax.getMaxAmount());
        }
        
        return taxAmount;
    }
    
    /**
     * 检查交易是否超出限制
     */
    public boolean isTradeValid(int itemCount, int tradeValue, int playerLevel) {
        if (limits == null) {
            return true;
        }
        
        // 检查物品数量限制
        if (limits.getMaxItems() != null && itemCount > limits.getMaxItems()) {
            return false;
        }
        
        // 检查交易价值限制
        if (limits.getMaxValue() != null && tradeValue > limits.getMaxValue()) {
            return false;
        }
        
        // 检查等级限制
        if (limits.getMinLevel() != null && playerLevel < limits.getMinLevel()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取交易超时时间
     */
    public int getTradeTimeout() {
        return limits != null && limits.getTimeout() != null ? 
               limits.getTimeout() : DEFAULT_TIMEOUT;
    }
    
    /**
     * 获取确认时间
     */
    public int getConfirmTime() {
        return security != null && security.getConfirmTime() != null ? 
               security.getConfirmTime() : DEFAULT_CONFIRM_TIME;
    }
    
    /**
     * 是否需要二次确认
     */
    public boolean isDoubleConfirmRequired() {
        return security != null && security.getDoubleConfirm() != null ? 
               security.getDoubleConfirm() : DEFAULT_DOUBLE_CONFIRM;
    }
}
