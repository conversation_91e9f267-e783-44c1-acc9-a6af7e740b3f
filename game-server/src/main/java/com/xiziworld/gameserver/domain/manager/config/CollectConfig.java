package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采集配置类
 * 对应 world/collect.yml
 * 
 * <AUTHOR>
 */
@Data
public class CollectConfig {

    /**
     * 采集配置
     */
    public  Map<String,CollectType> collectTypes;

    /**
     * 采集汇总配置
     */
    public  Summary summary;

    /**
     * 全局采集配置
     */
    public  GlobalCollectConfig globalCollectConfig;

    /**
     * 根据地图ID获取所有采集类型
     */
    public Map<String,CollectType> getCollectTypesByMapId(String mapId) {
        return collectTypes.entrySet().stream()
                .filter(entry -> entry.getValue().getLocations().stream()
                        .anyMatch(location -> location.getMap().equals(mapId)))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,      // 保持原有的key
                        Map.Entry::getValue     // 对应的value
                ));
    }

    /**
     * 采集类型配置
     */
    @Data
    public static class CollectType {
        /**
         * 采集地点列表
         */
        public  List<CollectLocation> locations;
    }
    
    /**
     * 采集地点配置
     */
    @Data
    public static class CollectLocation {
        /**
         * 地图ID
         */
        public  String map;
        
        /**
         * X坐标范围
         */
        public  List<Integer> xRange;

        /**
         * Y坐标范围
         */
        public  List<Integer> yRange;

        /**
         * 成功率
         */
        public  Double successRate;
        
        /**
         * 耗时（秒）
         */
        public  Integer timeCost;
        
        /**
         * 可采集物品列表
         */
        public  List<CollectItem> items;
    }
    
    /**
     * 采集物品配置
     */
    @Data
    public static class CollectItem {
        /**
         * 物品编号
         */
        public  String itemNo;
        
        /**
         * 获得概率
         */
        public  Double rate;
        
        /**
         * 获得数量
         */
        public  Integer count;
        
        /**
         * 最小数量
         */
        public  Integer min;
        
        /**
         * 最大数量
         */
        public  Integer max;
    }
    
    // 采集类型常量
    public static final String TYPE_FISHING = "fishing";      // 钓鱼
    public static final String TYPE_MULBERRY = "mulberry";    // 采桑
    public static final String TYPE_TEA = "tea";              // 采茶
    public static final String TYPE_MINING = "mining";        // 挖矿

    public static String getNameByType(String type) {
        switch (type) {
            case TYPE_FISHING:
                return "钓鱼";
            case TYPE_MULBERRY:
                return "采桑";
            case TYPE_TEA:
                return "采茶";
            case TYPE_MINING:
                return "挖矿";
            default:
                return "未知";
        }
    }
    /**
     * 全局采集配置
     */
    @Data
    public static class GlobalCollectConfig {
        /**
         * 天气影响
         */
        public  Map<String, Double> weatherEffects;

        /**
         * 时间段影响
         */
        public  Map<String, Double> timeEffects;
    }

    /**
     * 采集汇总配置
     */
    @Data
    public static class Summary {
        /**
         * 汇总时间窗口(秒)
         */
        public Integer timeWindow;

        /**
         * 最小汇总玩家数
         */
        public Integer minPlayers;

        /**
         * 消息模板
         */
        public String messageTemplate;
    }

    // 采集物品常量
    public static final String ITEM_FISH_01 = "ITEM_MAT_FISH_01";     // 白条
    public static final String ITEM_FISH_02 = "ITEM_MAT_FISH_02";     // 鲫鱼
    public static final String ITEM_FISH_03 = "ITEM_MAT_FISH_03";     // 鲤鱼
    public static final String ITEM_FISH_04 = "ITEM_MAT_FISH_04";     // 鳜鱼
    public static final String ITEM_FISH_05 = "ITEM_MAT_FISH_05";     // 鳗鱼
    public static final String ITEM_MULBERRY = "ITEM_MAT_PLANT_SAN";  // 桑叶
    public static final String ITEM_SILK = "ITEM_MAT_SILK";           // 天蚕丝
    public static final String ITEM_TEA = "ITEM_MAT_PLANT_CHA";       // 茶叶
    public static final String ITEM_TEA_SPECIAL = "ITEM_DRUG_SP_01";  // 龙井仙露
    public static final String ITEM_STONE = "ITEM_MAT_STONE";      // 雨花石
    public static final String ITEM_METEOR = "ITEM_MAT_STONE_02";     // 天外陨石
}
