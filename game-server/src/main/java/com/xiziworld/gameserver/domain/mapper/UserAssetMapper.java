package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.UserAsset;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户资产Mapper接口
 *
 * <AUTHOR>
 */
public interface UserAssetMapper extends BasicMapper<UserAsset> {

    /**
     * 根据用户信息和资产位置查询资产列表（去掉user_character表关联）
     */
    @Select("SELECT * FROM user_asset WHERE character_id = #{characterId} AND position = #{position} ORDER BY id")
    List<UserAsset> selectByUserInfoAndPosition(@Param("characterId") Long characterId, @Param("position") Integer position);

    /**
     * 根据用户信息和物品编号查询资产
     */
    @Select("SELECT * FROM user_asset WHERE character_id = #{characterId} AND item_no = #{itemNo} ORDER BY id")
    List<UserAsset> selectByUserInfoAndItemNo(@Param("characterId") String characterId, @Param("itemNo") String itemNo);
}