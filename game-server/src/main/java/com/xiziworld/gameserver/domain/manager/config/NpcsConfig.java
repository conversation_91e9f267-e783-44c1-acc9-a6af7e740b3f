package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * NPC配置类
 * 对应 world/npcs.yml
 * 
 * <AUTHOR>
 */
@Data
public class NpcsConfig {
    
    /**
     * NPC配置映射
     * Key: NPC编号, Value: NPC详细配置
     */
    private Map<String, NpcDetail> npcs;
    
    /**
     * 单个NPC的详细配置
     */
    @Data
    public static class NpcDetail {
        /**
         * NPC编号
         */
        private String npcNo;
        
        /**
         * NPC名称
         */
        private String name;
        
        /**
         * NPC描述
         */
        private String description;
        
        /**
         * 所在地图
         */
        private String location;
        
        /**
         * NPC坐标
         */
        private Coordinates coordinates;
        
        /**
         * 对话内容
         */
        private List<String> dialogue;
        
        /**
         * NPC功能列表
         */
        private List<String> functions;

        /**
         * 商店ID（如果NPC是商人）
         */
        private String shopId;

        /**
         * NPC属性
         */
        private NpcProperties properties;
    }
    
    /**
     * NPC坐标
     */
    @Data
    public static class Coordinates {
        /**
         * X坐标
         */
        private Integer x;
        
        /**
         * Y坐标
         */
        private Integer y;
    }
    
    /**
     * 对话配置
     */
    @Data
    public static class Dialogue {
        /**
         * 问候语
         */
        private String greeting;
        
        /**
         * 告别语
         */
        private String farewell;
        
        /**
         * 对话选项
         */
        private List<DialogueOption> options;
    }
    
    /**
     * 对话选项
     */
    @Data
    public static class DialogueOption {
        /**
         * 选项文本
         */
        private String text;
        
        /**
         * 选项动作
         */
        private String action;
        
        /**
         * 回复内容
         */
        private String response;
    }
    
    /**
     * NPC属性
     */
    @Data
    public static class NpcProperties {
        /**
         * 是否可移动
         */
        private Boolean movable;
        
        /**
         * 是否可攻击
         */
        private Boolean attackable;
        
        /**
         * 刷新时间（秒）
         */
        private Integer respawnTime;
        
        /**
         * 等级
         */
        private Integer level;
    }
    
    // NPC编号常量
    public static final String NPC_QIANCHU = "qianchu";       // 钱镠
    public static final String NPC_SONGWUSAO = "songwusao";   // 宋五嫂
    public static final String NPC_YUEFEI = "yuefei";         // 岳飞
    public static final String NPC_DINGREN = "dingren";       // 丁仁
    public static final String NPC_XUXIAN = "xuxian";         // 许仙
    public static final String NPC_JIGONG = "jigong";         // 济公
    
    // NPC功能常量
    public static final String FUNCTION_SHOP = "shop";        // 商店
    public static final String FUNCTION_QUEST = "quest";      // 任务
    public static final String FUNCTION_TELEPORT = "teleport"; // 传送
    public static final String FUNCTION_STORAGE = "storage";  // 仓库
    public static final String FUNCTION_REPAIR = "repair";    // 修理
    public static final String FUNCTION_ENHANCE = "enhance";  // 强化
    public static final String FUNCTION_TRADE = "trade";      // 交易
}
