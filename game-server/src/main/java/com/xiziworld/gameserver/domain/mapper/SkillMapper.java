package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.Skill;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.cache.impl.PerpetualCache;
import org.apache.ibatis.cache.decorators.LruCache;

/**
 * 技能Mapper接口
 * 使用MyBatis二级缓存，适合相对静态的技能数据
 *
 * <AUTHOR>
 */
@CacheNamespace(
    implementation = PerpetualCache.class,  // 缓存实现类
    eviction = LruCache.class,              // LRU淘汰策略
    size = 256,                             // 缓存大小（技能数据较少）
    flushInterval = 18000000,                // 300分钟刷新间隔
    readWrite = false                       // 只读缓存，提高性能
)
public interface SkillMapper extends BasicMapper<Skill> {

    /**
     * 根据技能编号查询技能
     */
    @Select("SELECT * FROM skill WHERE skill_no = #{skillNo}")
    Skill selectBySkillNo(@Param("skillNo") String skillNo);

    @Select("SELECT * FROM skill WHERE name = #{name}")
    Skill getSkillByName(@Param("name") String name);
}