package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 等级配置类
 * 对应 game/levels.yml
 * 
 * <AUTHOR>
 */
@Data
public class LevelsConfig {

    /**
     * 基础属性配置
     * Key: 职业常量名称, Value: 基础属性
     */
    private Map<String, BaseAttributes> baseAttributes;

    /**
     * 经验值缓存 - 预计算所有等级的经验值
     * Key: 等级, Value: 该等级所需经验值
     */
    private final Map<Integer, Long> expCache = new ConcurrentHashMap<>();

    /**
     * 属性缓存 - 预计算每个等级每个职业的所有属性
     * Key: roleType_level (如: "1_10"), Value: LevelAttributes
     */
    private final Map<String, LevelAttributes> attributeCache = new ConcurrentHashMap<>();

    /**
     * 最大等级
     */
    public static final int MAX_LEVEL = 120;
    
    /**
     * 升级属性配置
     */
    private List<LevelRange> levelRanges;
    
    /**
     * 等级经验配置
     */
    private LevelExp levelExp;
    
    /**
     * 等级称号配置
     */
    private List<TitleRange> titles;
    
    /**
     * 基础属性配置
     */
    @Data
    public static class BaseAttributes {
        /**
         * 生命值
         */
        private Integer hp;
        
        /**
         * 法力值
         */
        private Integer mp;
        
        /**
         * 法术攻击
         */
        private Integer attackM;
        
        /**
         * 物理攻击
         */
        private Integer attackW;
        
        /**
         * 佛法攻击
         */
        private Integer attackF;
        
        /**
         * 物理防御
         */
        private Integer defenseW;
        
        /**
         * 法术防御
         */
        private Integer defenseM;
        
        /**
         * 佛法防御
         */
        private Integer defenseF;
        
        /**
         * 基础经验
         */
        private Integer baseExp;
    }
    
    /**
     * 等级范围配置
     */
    @Data
    public static class LevelRange {
        /**
         * 等级范围
         */
        private List<Integer> range;
        
        /**
         * 血量法气每级增加比例
         */
        private Double hpMpIncrease;
        
        /**
         * 其他属性每级增加比例
         */
        private Double otherIncrease;
    }
    
    /**
     * 等级经验配置
     */
    @Data
    public static class LevelExp {
        /**
         * 经验计算公式
         */
        private String formula;
        
        /**
         * 基础经验值
         */
        private Long baseExp;
        
        /**
         * 经验增长倍数
         */
        private Double multiplier;
        
        /**
         * 等级影响因子
         */
        private Integer levelFactor;
    }
    
    /**
     * 称号范围配置
     */
    @Data
    public static class TitleRange {
        /**
         * 等级范围
         */
        private List<Integer> range;
        
        /**
         * 称号名称
         */
        private String title;

        private String description;
    }
    
    // 职业常量
    public static final String ROLE_SWORDSMAN = "swordsman";   // 剑客
    public static final String ROLE_MAGE = "mage";             // 仙师
    public static final String ROLE_MONK = "monk";             // 圣僧

    // 职业ID映射
    public static final Map<Integer, String> ROLE_ID_MAP = new HashMap<Integer, String>() {{
        put(1, ROLE_SWORDSMAN);
        put(2, ROLE_MAGE);
        put(3, ROLE_MONK);
    }};
    
    // 等级常量
    public static final int MIN_LEVEL = 1;                     // 最小等级
    public static final int NEWBIE_LEVEL = 10;                 // 新手等级上限
    public static final int PVP_LEVEL = 3;                    // PVP开启等级

    /**
     * 等级属性数据类
     */
    @Data
    public static class LevelAttributes {
        private int hp;          // 血量
        private int mp;          // 法力值
        private int phyAtk;      // 物理攻击
        private int magAtk;      // 法术攻击
        private int budAtk;      // 佛法攻击
        private int phyDef;      // 物理防御
        private int magDef;      // 法术防御
        private int budDef;      // 佛法防御
    }
    
    /**
     * 根据职业ID获取职业常量名称
     */
    public static String getRoleConstant(int roleId) {
        return ROLE_ID_MAP.get(roleId);
    }
    
    /**
     * 根据等级获取所需经验值（直接从缓存获取）
     */
    public long calculateLevelExp(int level) {
        if (level <= 1) {
            return levelExp.getBaseExp();
        }

        if (level > MAX_LEVEL) {
            level = MAX_LEVEL; // 超过最大等级按最大等级处理
        }

        // 直接从缓存获取，如果缓存为空则说明配置未初始化
        Long cachedExp = expCache.get(level);
        if (cachedExp == null) {
            throw new IllegalStateException("经验缓存未初始化，等级: " + level);
        }
        if(level <= 10) {
            return (long)(cachedExp* 0.5);
        }
        if(level <= 20) {
            return (long)(cachedExp* 0.75);
        }
        if(level <= 30) {
            return (long)(cachedExp* 0.9);
        }
        return cachedExp;
    }

    /**
     * 初始化等级缓存 - 预计算所有等级的经验值和属性值
     * 配置加载完成后立即调用此方法
     */
    public void initLevelCache() {
        if (levelExp == null) {
            throw new IllegalStateException("等级经验配置未加载");
        }
        if (levelRanges == null || levelRanges.isEmpty()) {
            throw new IllegalStateException("等级范围配置未加载");
        }

        // 清空现有缓存
        expCache.clear();
        attributeCache.clear();

        // 初始化经验缓存
        initExpCache();

        // 初始化属性缓存
        initAttributeCache();

        // 记录日志
        System.out.println("等级缓存初始化完成：");
        System.out.println("- 经验缓存：1-" + MAX_LEVEL + "级");
        System.out.println("- 属性缓存：1-" + MAX_LEVEL + "级 × 3职业");
        System.out.println("- 缓存大小：经验" + expCache.size() + "条，属性" + attributeCache.size() + "条");
    }

    /**
     * 初始化经验缓存
     */
    private void initExpCache() {
        // 1级经验值
        long baseExp = levelExp.getBaseExp();
        expCache.put(1, baseExp);

        // 计算2~120级的所有经验值
        long currentExp = baseExp;
        for (int level = 2; level <= MAX_LEVEL; level++) {
            // 应用公式：(上级经验 * multiplier) + (当前等级 * levelFactor)
            double result = (currentExp * levelExp.getMultiplier()) + (level * levelExp.getLevelFactor());
            currentExp = (int) Math.round(result);
            expCache.put(level, currentExp);
        }
    }

    /**
     * 初始化属性缓存 - 预计算每个等级每个职业的所有属性
     */
    private void initAttributeCache() {
        if (baseAttributes == null) {
            throw new IllegalStateException("基础属性配置未加载");
        }

        // 为每个职业的每个等级计算属性
        for (int roleType = 1; roleType <= 3; roleType++) {
            String roleConstant = getRoleConstant(roleType);
            BaseAttributes roleBaseAttr = baseAttributes.get(roleConstant);
            if (roleBaseAttr == null) {
                System.err.println("警告：职业" + roleType + "(" + roleConstant + ")的基础属性配置未找到");
                continue;
            }

            for (int level = 1; level <= MAX_LEVEL; level++) {
                LevelAttributes levelAttributes = calculateLevelAttributes(level, roleBaseAttr);
                String cacheKey = roleType + "_" + level;
                attributeCache.put(cacheKey, levelAttributes);
            }
        }
    }



    /**
     * 计算指定等级的属性值（基于levels.yml配置）
     */
    private LevelAttributes calculateLevelAttributes(int level, BaseAttributes baseAttr) {
        LevelAttributes levelAttributes = new LevelAttributes();

        // 获取该等级对应的成长参数
        LevelRange levelRange = getLevelRangeForLevel(level);
        double hpMpIncrease = levelRange != null && levelRange.getHpMpIncrease() != null ?
                             levelRange.getHpMpIncrease() : 0.05;
        double otherIncrease = levelRange != null && levelRange.getOtherIncrease() != null ?
                              levelRange.getOtherIncrease() : 0.03;

        // 计算血量和法力值（使用hpMpIncrease成长率）
        int baseHp = baseAttr.getHp() != null ? baseAttr.getHp() : 100;
        int baseMp = baseAttr.getMp() != null ? baseAttr.getMp() : 100;
        levelAttributes.setHp((int) (baseHp * (1 + (level - 1) * hpMpIncrease)));
        levelAttributes.setMp((int) (baseMp * (1 + (level - 1) * hpMpIncrease)));

        // 计算攻击属性（使用otherIncrease成长率）
        int basePhyAtk = baseAttr.getAttackW() != null ? baseAttr.getAttackW() : 10;  // 物理攻击
        int baseMagAtk = baseAttr.getAttackM() != null ? baseAttr.getAttackM() : 10;  // 法术攻击
        int baseBudAtk = baseAttr.getAttackF() != null ? baseAttr.getAttackF() : 10;  // 佛法攻击
        levelAttributes.setPhyAtk((int) (basePhyAtk * (1 + (level - 1) * otherIncrease)));
        levelAttributes.setMagAtk((int) (baseMagAtk * (1 + (level - 1) * otherIncrease)));
        levelAttributes.setBudAtk((int) (baseBudAtk * (1 + (level - 1) * otherIncrease)));

        // 计算防御属性（使用otherIncrease成长率）
        int basePhyDef = baseAttr.getDefenseW() != null ? baseAttr.getDefenseW() : 10;  // 物理防御
        int baseMagDef = baseAttr.getDefenseM() != null ? baseAttr.getDefenseM() : 10;  // 法术防御
        int baseBudDef = baseAttr.getDefenseF() != null ? baseAttr.getDefenseF() : 10;  // 佛法防御
        levelAttributes.setPhyDef((int) (basePhyDef * (1 + (level - 1) * otherIncrease)));
        levelAttributes.setMagDef((int) (baseMagDef * (1 + (level - 1) * otherIncrease)));
        levelAttributes.setBudDef((int) (baseBudDef * (1 + (level - 1) * otherIncrease)));

        return levelAttributes;
    }

    /**
     * 获取指定等级对应的等级范围配置
     */
    public LevelRange getLevelRangeForLevel(int level) {
        if (levelRanges == null) {
            return null;
        }

        for (LevelRange levelRange : levelRanges) {
            if (levelRange.getRange() != null && levelRange.getRange().size() >= 2) {
                int minLevel = levelRange.getRange().get(0);
                int maxLevel = levelRange.getRange().get(1);
                if (level >= minLevel && level <= maxLevel) {
                    return levelRange;
                }
            }
        }

        return null;
    }

    /**
     * 清空经验缓存（配置重载时使用）
     */
    public void clearExpCache() {
        expCache.clear();
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("经验缓存: %d个等级已缓存 (1-%d级)", expCache.size(), MAX_LEVEL);
    }

    /**
     * 检查经验缓存是否已初始化
     */
    public boolean isExpCacheInitialized() {
        return expCache.size() >= MAX_LEVEL;
    }

    /**
     * 获取指定等级范围的经验值（用于调试）
     */
    public void printExpRange(int startLevel, int endLevel) {
        System.out.println("=== 经验值范围 " + startLevel + "-" + endLevel + "级 ===");
        for (int level = startLevel; level <= Math.min(endLevel, MAX_LEVEL); level++) {
            Long exp = expCache.get(level);
            System.out.println(level + "级: " + (exp != null ? exp : "未缓存"));
        }
    }
    
    /**
     * 根据等级获取称号
     */
    public String getTitleByLevel(int level) {
        for (TitleRange titleRange : titles) {
            List<Integer> range = titleRange.getRange();
            if (range != null && range.size() >= 2) {
                int minLevel = range.get(0);
                int maxLevel = range.get(1);
                if (level >= minLevel && level <= maxLevel) {
                    return titleRange.getTitle();
                }
            }
        }
        return "无名";
    }

    // ==================== 属性缓存相关方法 ====================

    /**
     * 获取指定职业和等级的属性
     * @param roleType 职业类型 (1剑客 2仙师 3圣僧)
     * @param level 等级
     * @return 该等级的属性值，如果未找到返回null
     */
    public LevelAttributes getLevelAttributes(int roleType, int level) {
        if (level < MIN_LEVEL || level > MAX_LEVEL) {
            return null;
        }

        String cacheKey = roleType + "_" + level;
        LevelAttributes attributes = attributeCache.get(cacheKey);

        if (attributes == null) {
            throw new IllegalStateException("属性缓存未初始化，职业: " + roleType + ", 等级: " + level);
        }

        return attributes;
    }

    /**
     * 检查属性缓存是否已初始化
     */
    public boolean isAttributeCacheInitialized() {
        // 检查是否有3个职业 × MAX_LEVEL个等级的缓存
        return attributeCache.size() >= (3 * MAX_LEVEL);
    }

    /**
     * 获取属性缓存统计信息
     */
    public Map<String, Object> getAttributeCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", attributeCache.size());
        stats.put("expectedSize", 3 * MAX_LEVEL);
        stats.put("isInitialized", isAttributeCacheInitialized());

        // 统计每个职业的缓存数量
        Map<String, Integer> roleStats = new HashMap<>();
        for (int roleType = 1; roleType <= 3; roleType++) {
            int count = 0;
            for (int level = 1; level <= MAX_LEVEL; level++) {
                String cacheKey = roleType + "_" + level;
                if (attributeCache.containsKey(cacheKey)) {
                    count++;
                }
            }
            roleStats.put("role" + roleType, count);
        }
        stats.put("roleStats", roleStats);

        return stats;
    }

    /**
     * 清空属性缓存
     */
    public void clearAttributeCache() {
        attributeCache.clear();
    }
}
