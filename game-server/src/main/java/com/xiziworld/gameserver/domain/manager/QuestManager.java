package com.xiziworld.gameserver.domain.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.entity.Quest;
import com.xiziworld.gameserver.domain.entity.QuestProgress;
import com.xiziworld.gameserver.domain.mapper.QuestMapper;
import com.xiziworld.gameserver.domain.mapper.QuestProgressMapper;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.manager.AssetManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 任务管理器
 * 负责任务接取、任务完成、进度管理、奖励发放等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class QuestManager {
    
    @Autowired
    private QuestMapper questMapper;
    
    @Autowired
    private QuestProgressMapper questProgressMapper;
    
    @Autowired
    private ConfigManager configManager;
    
    @Autowired
    private PlayerManager playerManager;
    
    @Autowired
    private TradeManager tradeManager;
    
    @Autowired
    private AssetManager assetManager;

    // 任务类型常量
    public static final int QUEST_TYPE_MAIN = 1;    // 主线任务
    public static final int QUEST_TYPE_SIDE = 2;    // 支线任务
    public static final int QUEST_TYPE_DAILY = 3;   // 日常任务
    public static final int QUEST_TYPE_WEEKLY = 4;  // 周常任务

    // 任务状态常量
    public static final int QUEST_STATUS_AVAILABLE = 0;  // 可接取
    public static final int QUEST_STATUS_ACCEPTED = 1;   // 已接取
    public static final int QUEST_STATUS_COMPLETED = 2;  // 已完成
    public static final int QUEST_STATUS_SUBMITTED = 3;  // 已提交
    public static final int QUEST_STATUS_FAILED = 4;     // 已失败

    // ==================== 任务查询系统 ====================

    /**
     * 获取角色任务列表
     * 
     * @param characterId 角色ID
     * @param questType 任务类型（null表示所有类型）
     * @return 任务列表信息
     */
    public String getQuestList(Long characterId, Integer questType) {
        log.info("获取任务列表: characterId={}, questType={}", characterId, questType);
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        // 获取任务列表
        List<Quest> availableQuests = getAvailableQuests(character, questType);
        List<QuestProgress> activeQuests = getActiveQuests(character, questType);
        
        StringBuilder result = new StringBuilder();
        
        if (questType == null) {
            result.append("📋 任务总览\n");
        } else {
            result.append("📋 ").append(getQuestTypeName(questType)).append("\n");
        }
        
        result.append("╭──────────────────╮\n");
        
        // 显示进行中的任务
        if (!activeQuests.isEmpty()) {
            result.append("🔄 进行中的任务：\n");
            for (QuestProgress progress : activeQuests) {
                Quest quest = questMapper.selectById(progress.getQuestId());
                if (quest != null) {
                    String statusIcon = getQuestStatusIcon(progress.getStatus());
                    result.append(String.format("%s %s (%s)\n", 
                        statusIcon, quest.getName(), getQuestStatusName(progress.getStatus())));
                    
                    // 显示进度信息
                    String progressInfo = getQuestProgressInfo(quest, progress);
                    if (progressInfo != null) {
                        result.append("   ").append(progressInfo).append("\n");
                    }
                    
                    // 显示奖励信息
                    String rewardInfo = getQuestRewardInfo(quest);
                    result.append("   奖励：").append(rewardInfo).append("\n");
                }
            }
            result.append("\n");
        }
        
        // 显示可接取的任务
        if (!availableQuests.isEmpty()) {
            result.append("✨ 可接取的任务：\n");
            int index = 1;
            for (Quest quest : availableQuests) {
                result.append(String.format("%d. %s\n", index++, quest.getName()));
                result.append("   ").append(quest.getDescription()).append("\n");
                
                String rewardInfo = getQuestRewardInfo(quest);
                result.append("   奖励：").append(rewardInfo).append("\n");
            }
        }
        
        result.append("╰──────────────────╯\n");
        result.append("\n💡 使用 '接受任务 任务名' 接受任务");
        result.append("\n💡 使用 '提交任务 任务名' 提交完成的任务");
        
        return result.toString();
    }

    /**
     * 获取任务详细信息
     */
    public String getQuestDetail(Long characterId, String questName) {
        log.info("获取任务详情: characterId={}, questName={}", characterId, questName);

        // 查找任务
        Quest quest = findQuestByName(questName);
        if (quest == null) {
            return "未找到任务: " + questName;
        }
        
        // 获取任务进度
        QuestProgress progress = getQuestProgress(characterId, quest.getId());
        
        StringBuilder result = new StringBuilder();
        result.append("📜 ").append(quest.getName()).append("\n\n");
        result.append("📝 任务描述：\n").append(quest.getDescription()).append("\n\n");
        result.append("🏷️ 任务类型：").append(getQuestTypeName(quest.getType())).append("\n");
        
        if (progress != null) {
            result.append("📊 任务状态：").append(getQuestStatusName(progress.getStatus())).append("\n");
            
            String progressInfo = getQuestProgressInfo(quest, progress);
            if (progressInfo != null) {
                result.append("📈 任务进度：").append(progressInfo).append("\n");
            }
        } else {
            result.append("📊 任务状态：未接取\n");
        }
        
        result.append("\n🎁 任务奖励：\n").append(getQuestRewardInfo(quest));
        
        return result.toString();
    }

    // ==================== 任务接取系统 ====================

    /**
     * 接受任务
     * 
     * @param characterId 角色ID
     * @param questName 任务名称
     * @return 接受结果信息
     */
    @Transactional
    public String acceptQuest(Long characterId, String questName) {
        log.info("接受任务: characterId={}, questName={}", characterId, questName);
        
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        
        // 查找任务
        Quest quest = findQuestByName(questName);
        if (quest == null) {
            return "未找到任务: " + questName;
        }
        
        // 检查是否已接取
        QuestProgress existingProgress = getQuestProgress(character.getId(), quest.getId());
        if (existingProgress != null) {
            return "你已经接取了任务: " + questName;
        }
        
        // 检查前置条件
        String prerequisiteCheck = checkQuestPrerequisites(character, quest);
        if (prerequisiteCheck != null) {
            return prerequisiteCheck;
        }
        
        // 检查任务数量限制
        if (isQuestLimitReached(character, quest.getType())) {
            return "已达到该类型任务的数量上限";
        }
        
        // 创建任务进度
        QuestProgress progress = new QuestProgress();
        progress.setCharacterId(character.getId());
        progress.setQuestId(quest.getId());
        progress.setProgress(0);
        progress.setStatus(QUEST_STATUS_ACCEPTED);
        
        questProgressMapper.insert(progress);
        
        return "✅ 成功接取任务: " + quest.getName();
    }

    /**
     * 提交任务
     * 
     * @param characterId 角色ID
     * @param questName 任务名称
     * @return 提交结果信息
     */
    @Transactional
    public String submitQuest(Long characterId, String questName) {
        log.info("提交任务: characterId={}, questName={}", characterId, questName);
        
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        
        // 查找任务
        Quest quest = findQuestByName(questName);
        if (quest == null) {
            return "未找到任务: " + questName;
        }
        
        // 获取任务进度
        QuestProgress progress = getQuestProgress(character.getId(), quest.getId());
        if (progress == null) {
            return "你尚未接取任务: " + questName;
        }
        
        if (progress.getStatus() != QUEST_STATUS_COMPLETED) {
            return "任务尚未完成，无法提交";
        }
        
        // 发放奖励
        String rewardResult = giveQuestRewards(character, quest);
        
        // 更新任务状态
        progress.setStatus(QUEST_STATUS_SUBMITTED);
        questProgressMapper.updateById(progress);
        
        return "✅ 任务提交成功！\n" + rewardResult;
    }

    /**
     * 放弃任务
     * 
     * @param characterId 角色ID
     * @param questName 任务名称
     * @return 放弃结果信息
     */
    @Transactional
    public String abandonQuest(Long characterId, String questName) {
        log.info("放弃任务: characterId={}, questName={}", characterId, questName);
        
        // 查找任务
        Quest quest = findQuestByName(questName);
        if (quest == null) {
            return "未找到任务: " + questName;
        }
        
        // 获取任务进度
        QuestProgress progress = getQuestProgress(characterId, quest.getId());
        if (progress == null) {
            return "你尚未接取任务: " + questName;
        }
        
        if (progress.getStatus() == QUEST_STATUS_SUBMITTED) {
            return "任务已提交，无法放弃";
        }
        
        // 检查是否可以放弃（主线任务通常不能放弃）
        if (quest.getType() == QUEST_TYPE_MAIN) {
            return "主线任务无法放弃";
        }
        
        // 删除任务进度
        questProgressMapper.deleteById(progress.getId());
        
        return "✅ 已放弃任务: " + quest.getName();
    }

    // ==================== 任务进度更新系统 ====================

    /**
     * 更新任务进度
     * 
     * @param characterId 角色ID
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    public void updateQuestProgress(Long characterId, String eventType, Map<String, Object> eventData) {
        log.info("更新任务进度: characterId={}, eventType={}, eventData={}", 
                characterId, eventType, eventData);
        
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            return;
        }
        
        // 获取所有进行中的任务
        List<QuestProgress> activeQuests = getActiveQuests(character, null);
        
        for (QuestProgress progress : activeQuests) {
            if (progress.getStatus() != QUEST_STATUS_ACCEPTED) {
                continue;
            }
            
            Quest quest = questMapper.selectById(progress.getQuestId());
            if (quest == null) {
                continue;
            }
            
            // 根据事件类型更新进度
            boolean updated = updateQuestProgressByEvent(quest, progress, eventType, eventData);
            
            if (updated) {
                questProgressMapper.updateById(progress);
                
                // 检查任务是否完成
                if (isQuestCompleted(quest, progress)) {
                    progress.setStatus(QUEST_STATUS_COMPLETED);
                    questProgressMapper.updateById(progress);
                    
                    // TODO: 通知玩家任务完成
                    log.info("任务完成: characterId={}, questName={}", characterId, quest.getName());
                }
            }
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取可接取的任务列表
     */
    private List<Quest> getAvailableQuests(UserCharacter character, Integer questType) {
        List<Quest> allQuests = questMapper.selectByType(questType);
        List<Quest> availableQuests = new ArrayList<>();
        
        for (Quest quest : allQuests) {
            // 检查是否已接取
            QuestProgress progress = getQuestProgress(character.getId(), quest.getId());
            if (progress == null) {
                // 检查前置条件
                if (checkQuestPrerequisites(character, quest) == null) {
                    availableQuests.add(quest);
                }
            }
        }
        
        return availableQuests;
    }

    /**
     * 获取进行中的任务列表（在Manager层过滤任务类型）
     */
    private List<QuestProgress> getActiveQuests(UserCharacter character, Integer questType) {
        // 1. 查询所有进行中的任务进度
        List<QuestProgress> allActiveQuests = questProgressMapper.selectActiveByCharacter(character.getId());

        // 2. 如果不需要按类型过滤，直接返回
        if (questType == null) {
            return allActiveQuests;
        }

        // 3. 在Manager层过滤任务类型（利用Quest缓存）
        List<QuestProgress> filteredQuests = new ArrayList<>();
        for (QuestProgress progress : allActiveQuests) {
            Quest quest = questMapper.selectById(progress.getQuestId());
            if (quest != null && questType.equals(quest.getType())) {
                filteredQuests.add(progress);
            }
        }

        return filteredQuests;
    }

    /**
     * 获取任务进度
     */
    private QuestProgress getQuestProgress(Long characterId, Long questId) {
        return questProgressMapper.selectByCharacterAndQuest(characterId, questId);
    }

    /**
     * 根据名称查找任务
     */
    private Quest findQuestByName(String questName) {
        return questMapper.selectByName(questName);
    }

    /**
     * 检查任务前置条件
     */
    private String checkQuestPrerequisites(UserCharacter character, Quest quest) {
        // 基础等级检查（根据任务类型设置不同的等级要求）
        int requiredLevel = getRequiredLevel(quest);
        if (character.getLevel() < requiredLevel) {
            return "等级不足，需要等级" + requiredLevel + "才能接取该任务";
        }

        // 主线任务的前置检查
        if (quest.getType() == QUEST_TYPE_MAIN) {
            String mainQuestCheck = checkMainQuestPrerequisites(character, quest);
            if (mainQuestCheck != null) {
                return mainQuestCheck;
            }
        }

        return null; // 所有条件都满足
    }

    /**
     * 获取任务所需等级
     */
    private int getRequiredLevel(Quest quest) {
        switch (quest.getType()) {
            case QUEST_TYPE_MAIN:
                return 1; // 主线任务1级可接
            case QUEST_TYPE_SIDE:
                return 5; // 支线任务5级可接
            case QUEST_TYPE_DAILY:
                return 10; // 日常任务10级可接
            case QUEST_TYPE_WEEKLY:
                return 15; // 周常任务15级可接
            default:
                return 1;
        }
    }

    /**
     * 检查主线任务前置条件
     */
    private String checkMainQuestPrerequisites(UserCharacter character, Quest quest) {
        // 简单的主线任务顺序检查
        // 可以根据任务名称或ID判断顺序
        return null; // 暂时不做复杂的前置检查
    }



    /**
     * 检查任务是否已完成
     */
    private boolean isQuestCompleted(Long characterId, String questName) {
        Quest quest = findQuestByName(questName);
        if (quest == null) {
            return false;
        }

        QuestProgress progress = getQuestProgress(characterId, quest.getId());
        return progress != null && progress.getStatus() == QUEST_STATUS_SUBMITTED;
    }

    /**
     * 根据职业ID获取职业名称
     */
    private String getRoleTypeName(Integer roleType) {
        switch (roleType) {
            case 1: return "剑客";
            case 2: return "仙师";
            case 3: return "圣僧";
            default: return "未知职业";
        }
    }

    /**
     * 检查任务数量限制
     */
    private boolean isQuestLimitReached(UserCharacter character, Integer questType) {
        List<QuestProgress> activeQuests = getActiveQuests(character, questType);
        
        // 设置不同类型任务的数量限制
        int limit = 10; // 默认限制
        switch (questType) {
            case QUEST_TYPE_MAIN:
                limit = 1; // 主线任务同时只能接1个
                break;
            case QUEST_TYPE_SIDE:
                limit = 5; // 支线任务同时最多5个
                break;
            case QUEST_TYPE_DAILY:
                limit = 10; // 日常任务同时最多10个
                break;
            case QUEST_TYPE_WEEKLY:
                limit = 3; // 周常任务同时最多3个
                break;
        }
        
        return activeQuests.size() >= limit;
    }

    /**
     * 发放任务奖励
     */
    private String giveQuestRewards(UserCharacter character, Quest quest) {
        if (quest.getRewards() == null || quest.getRewards().isEmpty()) {
            return "无奖励";
        }
        
        try {
            JSONObject rewards = JSON.parseObject(quest.getRewards());
            StringBuilder result = new StringBuilder();
            result.append("🎁 获得奖励：\n");
            
            // 经验奖励
            if (rewards.containsKey("exp")) {
                int exp = rewards.getInteger("exp");
                boolean levelUp = playerManager.addExperience(character.getId(), (long) exp);
                result.append("经验 +").append(exp);
                if (levelUp) {
                    result.append(" (恭喜升级！)");
                }
                result.append("\n");
            }
            
            // 金币奖励
            if (rewards.containsKey("gold")) {
                int gold = rewards.getInteger("gold");
                // TODO: 添加金币
                result.append("金币 +").append(gold).append("\n");
            }
            
            // 银两奖励
            if (rewards.containsKey("silver")) {
                int silver = rewards.getInteger("silver");
                // TODO: 添加银两
                result.append("银两 +").append(silver).append("\n");
            }
            
            // 物品奖励
            if (rewards.containsKey("items")) {
                // TODO: 添加物品到背包
                result.append("物品奖励已放入背包\n");
            }
            
            return result.toString();
        } catch (Exception e) {
            log.error("解析任务奖励失败: questId={}, rewards={}", quest.getId(), quest.getRewards(), e);
            return "奖励发放失败";
        }
    }

    /**
     * 获取任务类型名称
     */
    private String getQuestTypeName(Integer type) {
        switch (type) {
            case QUEST_TYPE_MAIN: return "主线任务";
            case QUEST_TYPE_SIDE: return "支线任务";
            case QUEST_TYPE_DAILY: return "日常任务";
            case QUEST_TYPE_WEEKLY: return "周常任务";
            default: return "未知任务";
        }
    }

    /**
     * 获取任务状态名称
     */
    private String getQuestStatusName(Integer status) {
        switch (status) {
            case QUEST_STATUS_AVAILABLE: return "可接取";
            case QUEST_STATUS_ACCEPTED: return "进行中";
            case QUEST_STATUS_COMPLETED: return "已完成";
            case QUEST_STATUS_SUBMITTED: return "已提交";
            case QUEST_STATUS_FAILED: return "已失败";
            default: return "未知状态";
        }
    }

    /**
     * 获取任务状态图标
     */
    private String getQuestStatusIcon(Integer status) {
        switch (status) {
            case QUEST_STATUS_ACCEPTED: return "🔄";
            case QUEST_STATUS_COMPLETED: return "✅";
            case QUEST_STATUS_FAILED: return "❌";
            default: return "📋";
        }
    }

    /**
     * 获取任务进度信息
     */
    private String getQuestProgressInfo(Quest quest, QuestProgress progress) {
        // TODO: 根据任务类型返回具体的进度信息
        // 例如：击杀怪物 5/10，采集物品 3/5 等
        return "进度 " + progress.getProgress() + "/100";
    }

    /**
     * 获取任务奖励信息
     */
    private String getQuestRewardInfo(Quest quest) {
        if (quest.getRewards() == null || quest.getRewards().isEmpty()) {
            return "无奖励";
        }
        
        try {
            JSONObject rewards = JSON.parseObject(quest.getRewards());
            StringBuilder result = new StringBuilder();
            
            if (rewards.containsKey("exp")) {
                result.append("经验+").append(rewards.getInteger("exp")).append(" ");
            }
            if (rewards.containsKey("gold")) {
                result.append("金币+").append(rewards.getInteger("gold")).append(" ");
            }
            if (rewards.containsKey("silver")) {
                result.append("银两+").append(rewards.getInteger("silver")).append(" ");
            }
            if (rewards.containsKey("items")) {
                result.append("物品奖励 ");
            }
            
            return result.toString().trim();
        } catch (Exception e) {
            return "奖励信息解析失败";
        }
    }

    /**
     * 根据事件更新任务进度
     */
    private boolean updateQuestProgressByEvent(Quest quest, QuestProgress progress,
                                             String eventType, Map<String, Object> eventData) {
        // 简化的进度更新逻辑
        // 根据任务类型和事件类型决定是否更新进度

        boolean updated = false;
        int currentProgress = progress.getProgress();

        switch (eventType) {
            case "monster_killed":
                // 击杀类任务，每击杀一个怪物增加10%进度
                if (isKillQuest(quest)) {
                    currentProgress = Math.min(100, currentProgress + 10);
                    updated = true;
                }
                break;

            case "item_collected":
                // 采集类任务，每采集一个物品增加20%进度
                if (isCollectQuest(quest)) {
                    int quantity = (Integer) eventData.getOrDefault("quantity", 1);
                    currentProgress = Math.min(100, currentProgress + (quantity * 20));
                    updated = true;
                }
                break;

            case "location_visited":
                // 访问类任务，访问指定地点直接完成
                if (isVisitQuest(quest)) {
                    currentProgress = 100;
                    updated = true;
                }
                break;

            case "npc_talked":
                // 对话类任务，与指定NPC对话直接完成
                if (isTalkQuest(quest)) {
                    currentProgress = 100;
                    updated = true;
                }
                break;

            case "level_up":
                // 等级类任务，达到指定等级直接完成
                if (isLevelQuest(quest)) {
                    currentProgress = 100;
                    updated = true;
                }
                break;

            default:
                log.debug("未处理的事件类型: {}", eventType);
                break;
        }

        if (updated) {
            progress.setProgress(currentProgress);
        }

        return updated;
    }

    /**
     * 判断是否为击杀类任务
     */
    private boolean isKillQuest(Quest quest) {
        return quest.getName().contains("击杀") || quest.getName().contains("消灭") ||
               quest.getDescription().contains("击杀") || quest.getDescription().contains("消灭");
    }

    /**
     * 判断是否为采集类任务
     */
    private boolean isCollectQuest(Quest quest) {
        return quest.getName().contains("采集") || quest.getName().contains("收集") ||
               quest.getDescription().contains("采集") || quest.getDescription().contains("收集");
    }

    /**
     * 判断是否为访问类任务
     */
    private boolean isVisitQuest(Quest quest) {
        return quest.getName().contains("前往") || quest.getName().contains("访问") ||
               quest.getDescription().contains("前往") || quest.getDescription().contains("访问");
    }

    /**
     * 判断是否为对话类任务
     */
    private boolean isTalkQuest(Quest quest) {
        return quest.getName().contains("对话") || quest.getName().contains("拜访") ||
               quest.getDescription().contains("对话") || quest.getDescription().contains("拜访");
    }

    /**
     * 判断是否为等级类任务
     */
    private boolean isLevelQuest(Quest quest) {
        return quest.getName().contains("等级") || quest.getName().contains("升级") ||
               quest.getDescription().contains("等级") || quest.getDescription().contains("升级");
    }



    /**
     * 检查任务是否完成
     */
    private boolean isQuestCompleted(Quest quest, QuestProgress progress) {
        // 简单检查：进度是否达到100%
        return progress.getProgress() >= 100;
    }

    // ==================== 日常任务和周常任务系统 ====================

    /**
     * 刷新日常任务（每日0点调用）
     */
    public void refreshDailyQuests() {
        log.info("刷新日常任务");

        // TODO: 实现日常任务刷新逻辑
        // 1. 重置所有角色的日常任务进度
        // 2. 生成新的日常任务
        // 3. 清理过期的日常任务
    }

    /**
     * 刷新周常任务（每周一0点调用）
     */
    public void refreshWeeklyQuests() {
        log.info("刷新周常任务");

        // TODO: 实现周常任务刷新逻辑
        // 1. 重置所有角色的周常任务进度
        // 2. 生成新的周常任务
        // 3. 清理过期的周常任务
    }

    /**
     * 获取任务统计信息
     */
    public Map<String, Object> getQuestStats(Long characterId) {

        Map<String, Object> stats = new HashMap<>();
        // 统计各类型任务数量（查询所有任务进度）
        List<QuestProgress> allProgress = questProgressMapper.selectAllByCharacter(characterId);

        int mainQuests = 0, sideQuests = 0, dailyQuests = 0, weeklyQuests = 0;
        int completedQuests = 0;

        for (QuestProgress progress : allProgress) {
            Quest quest = questMapper.selectById(progress.getQuestId());
            if (quest != null) {
                switch (quest.getType()) {
                    case QUEST_TYPE_MAIN: mainQuests++; break;
                    case QUEST_TYPE_SIDE: sideQuests++; break;
                    case QUEST_TYPE_DAILY: dailyQuests++; break;
                    case QUEST_TYPE_WEEKLY: weeklyQuests++; break;
                }

                if (progress.getStatus() == QUEST_STATUS_COMPLETED) {
                    completedQuests++;
                }
            }
        }

        stats.put("mainQuests", mainQuests);
        stats.put("sideQuests", sideQuests);
        stats.put("dailyQuests", dailyQuests);
        stats.put("weeklyQuests", weeklyQuests);
        stats.put("completedQuests", completedQuests);
        stats.put("totalActiveQuests", allProgress.size());

        return stats;
    }

    // ==================== 任务事件触发系统 ====================

    /**
     * 怪物击杀事件
     */
    public void onMonsterKilled(Long characterId, String monsterType, String mapId) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("monsterType", monsterType);
        eventData.put("mapId", mapId);

        updateQuestProgress(characterId, "monster_killed", eventData);
    }

    /**
     * 物品采集事件
     */
    public void onItemCollected(Long characterId, String itemType, int quantity) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("itemType", itemType);
        eventData.put("quantity", quantity);

        updateQuestProgress(characterId, "item_collected", eventData);
    }

    /**
     * 地点访问事件
     */
    public void onLocationVisited(Long characterId, String mapId) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("mapId", mapId);

        updateQuestProgress(characterId, "location_visited", eventData);
    }

    /**
     * NPC对话事件
     */
    public void onNPCTalked(Long characterId, String npcId) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("npcId", npcId);

        updateQuestProgress(characterId, "npc_talked", eventData);
    }

    /**
     * 等级提升事件
     */
    public void onLevelUp(Long characterId, int newLevel) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("level", newLevel);

        updateQuestProgress(characterId, "level_up", eventData);
    }

    /**
     * 装备穿戴事件
     */
    public void onEquipmentEquipped(Long characterId, String itemType, int quality) {
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("itemType", itemType);
        eventData.put("quality", quality);

        updateQuestProgress(characterId, "equipment_equipped", eventData);
    }

    // ==================== 公共接口方法 ====================

    /**
     * 检查角色是否有进行中的任务
     */
    public boolean hasActiveQuests(Long characterId) {
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            return false;
        }

        List<QuestProgress> activeQuests = getActiveQuests(character, null);
        return !activeQuests.isEmpty();
    }

    /**
     * 获取角色可提交的任务数量
     */
    public int getCompletedQuestCount(Long characterId) {
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            return 0;
        }

        List<QuestProgress> activeQuests = getActiveQuests(character, null);
        return (int) activeQuests.stream()
                .filter(progress -> progress.getStatus() == QUEST_STATUS_COMPLETED)
                .count();
    }

    /**
     * 获取任务简要信息（用于状态显示）
     */
    public String getQuestSummary(Long characterId) {
        Map<String, Object> stats = getQuestStats(characterId);

        int totalActive = (Integer) stats.get("totalActiveQuests");
        int completed = (Integer) stats.get("completedQuests");

        if (totalActive == 0) {
            return "无进行中的任务";
        }

        return String.format("任务：%d个进行中，%d个可提交", totalActive, completed);
    }

    /**
     * 清理过期任务（定时任务调用）
     */
    public void cleanExpiredQuests() {
        log.info("清理过期任务");

        // TODO: 实现过期任务清理逻辑
        // 1. 清理过期的日常任务
        // 2. 清理过期的周常任务
        // 3. 清理失败的任务
    }
}
