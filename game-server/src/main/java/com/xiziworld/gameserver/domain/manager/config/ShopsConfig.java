package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.Map;

/**
 * 商店配置类
 * 对应 world/shops.yml
 * 
 * <AUTHOR>
 */
@Data
public class ShopsConfig {
    
    /**
     * 商店配置映射
     * Key: 商店ID, Value: 商店详细配置
     */
    private Map<String, ShopDetail> shops;
    
    /**
     * 单个商店的详细配置
     */
    @Data
    public static class ShopDetail {
        /**
         * 商店ID
         */
        private String shopId;
        
        /**
         * 商店名称
         */
        private String name;
        
        /**
         * 关联的NPC编号
         */
        private String npcNo;
        
        /**
         * 商店位置
         */
        private String location;
        
        /**
         * 商店描述
         */
        private String description;
        
        /**
         * 商品配置映射
         * Key: 物品编号, Value: 商品详细配置
         */
        private Map<String, ItemDetail> items;
        
        /**
         * 购买倍率
         */
        private Double buyRate;
        
        /**
         * 出售倍率
         */
        private Double sellRate;
    }
    
    /**
     * 商品详细配置
     */
    @Data
    public static class ItemDetail {
        /**
         * 商品价格
         */
        private Integer price;
        
        /**
         * 货币类型
         */
        private String currency;
        
        /**
         * 库存数量（-1表示无限）
         */
        private Integer stock;
        
        /**
         * 折扣率
         */
        private Double discount;
    }
    
    // 商店ID常量
    public static final String SHOP_QIANCHU = "qianchu_shop";     // 钱镠商店
    public static final String SHOP_SONGWUSAO = "songwusao_shop"; // 宋五嫂鱼店
    public static final String SHOP_YUEFEI = "yuefei_shop";       // 岳飞装备店
    public static final String SHOP_DINGREN = "dingren_shop";     // 丁仁技能书店
    public static final String SHOP_XUXIAN = "xuxian_shop";       // 许仙药店
    public static final String SHOP_JIGONG = "jigong_shop";       // 济公神奇盒子店
    
    // 货币类型常量
    public static final String CURRENCY_SILVER = "silver";        // 银两
    public static final String CURRENCY_GOLD = "gold";            // 金币
}
