package com.xiziworld.gameserver.domain.manager.config;

import com.xiziworld.gameserver.common.constant.CmdConstant;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 命令配置类
 * 对应 system/command.yml
 * 
 * <AUTHOR>
 */
@Data
public class CommandConfig {
    
    /**
     * 命令配置映射
     * Key: 命令名称, Value: 命令详细配置
     */
    private Map<String, CommandDetail> commands;
    
    /**
     * 全局限流配置
     */
    private GlobalLimits globalLimits;
    
    /**
     * 单个命令的详细配置
     */
    @Data
    public static class CommandDetail {
        /**
         * 命令别名列表
         */
        private List<String> aliases;
        
        /**
         * 命令描述
         */
        private String description;
        
        /**
         * 限流配置
         */
        private RateLimit rateLimit;
        
        /**
         * 是否为管理员专用命令
         */
        private Boolean adminOnly;
    }
    
    /**
     * 限流配置
     */
    @Data
    public static class RateLimit {
        /**
         * 限流时间窗口（秒）
         */
        private Integer seconds;
        
        /**
         * 最大执行次数
         */
        private Integer maxCount;
        
        /**
         * 超出限制时的提示消息
         */
        private String message;
    }
    
    /**
     * 全局限流配置
     */
    @Data
    public static class GlobalLimits {
        /**
         * 每用户每分钟最大命令数
         */
        private Integer maxCommandsPerMinute;
        
        /**
         * 每用户每小时最大命令数
         */
        private Integer maxCommandsPerHour;
        
        /**
         * 超出限制的提示消息
         */
        private String limitExceededMessage;
    }
    
    // ==================== 查看类命令常量 ====================
    public static final String COMMAND_VIEW_STATUS = "viewStatus";      // 查看状态 - 显示角色当前行动、状态、位置、死亡信息
    public static final String COMMAND_VIEW_MAP = "viewMap";             // 查看地图 - 显示地图、人物分布、boss分布
    public static final String COMMAND_VIEW_EQUIPMENT = "viewEquipment"; // 查看装备 - 显示装备属性和装备情况
    public static final String COMMAND_VIEW_INVENTORY = "viewInventory"; // 查看背包 - 显示背包内容
    public static final String COMMAND_VIEW_RANKING = "viewRanking";     // 查看排名 - 显示战力、财富、恶人、降妖排行
    public static final String COMMAND_VIEW_TARGET = "viewTarget";       // 查看目标 - 查看指定目标的介绍和属性

    // ==================== 移动类命令常量 ====================
    public static final String COMMAND_MOVE_TO = "moveTo";               // 前往 - 移动到指定地点、坐标或目标编号

    // ==================== 战斗类命令常量 ====================
    public static final String COMMAND_ATTACK = "attack";                 // 攻击 - 对目标发起普通攻击
    public static final String COMMAND_CAST_SKILL = "castSkill";          // 施技能 - 对目标使用技能
    public static final String COMMAND_RUN = "run";                      // 逃跑 - 逃跑，脱离战斗
    public static final String COMMAND_REVIVE = "revive";                 // 复活 - 死亡后复活

    // ==================== 采集类命令常量 ====================
    public static final String COMMAND_FISHING = "fishing";               // 钓鱼 - 在可钓鱼的地图进行钓鱼
    public static final String COMMAND_MULBERRY = "mulberry";             // 采桑 - 在可采桑的地图进行采桑
    public static final String COMMAND_TEA_PICKING = "teaPicking";       // 采茶 - 在可采茶的地图进行采茶
    public static final String COMMAND_MINING = "mining";                 // 挖矿 - 在可挖矿的地图进行挖矿

    // ==================== 挂机类命令常量 ====================
    public static final String COMMAND_IDLE_MINING = "idleMining";       // 挂机挖矿 - 挂机挖矿，每10分钟计算一次结果
    public static final String COMMAND_IDLE_FISHING = "idleFishing";     // 挂机钓鱼 - 挂机钓鱼，每10分钟计算一次结果
    public static final String COMMAND_IDLE_MULBERRY = "idleMulberry";   // 挂机采桑 - 挂机采桑，每10分钟计算一次结果
    public static final String COMMAND_IDLE_COMBAT = "idleCombat";       // 挂机打怪 - 挂机自动打怪，技能可选
    public static final String COMMAND_IDLE_EXIT = "idleExit";          // 退出挂机

    // ==================== 人物操作命令常量 ====================
    public static final String COMMAND_CREATE_CHARACTER = "createCharacter"; // 创建角色 - 创建对应职业名称的角色
    public static final String COMMAND_USE_ITEM = "useItem";             // 使用物品 - 使用消耗品，比如药品
    public static final String COMMAND_EQUIP_ITEM = "equipItem";         // 装备物品 - 穿上装备
    public static final String COMMAND_STORE_ITEM = "storeItem";         // 存物品 - 将物品放入仓库
    public static final String COMMAND_RETRIEVE_ITEM = "retrieveItem";   // 取物品 - 从仓库取出物品
    public static final String COMMAND_EXCHANGE_CURRENCY = "exchangeCurrency"; // 兑现 - 按配置比例兑换银两为金币

    // ==================== 交易类命令常量 ====================
    public static final String COMMAND_BUY = "buy";     // 购买 - 从市场/NPC购买物品
    public static final String COMMAND_SELL = "sell";       // 出售 - 向市场/NPC出售物品
    public static final String COMMAND_ASK_NPC = "askNpc";               // 询问NPC - 向NPC询问信息
    public static final String COMMAND_CANCEL_SELL = "cancelSell"; // 取出市场售卖
    public static final String COMMAND_VIEW_MARKET = "viewMarket"; // 查看市场


    // ==================== 装备操作命令常量 ====================
    public static final String COMMAND_UPGRADE_EQUIPMENT = "upgradeEquipment"; // 装备升品 - 对身上对应的道具进行升品
    public static final String COMMAND_REFINE_JADE = "refineJade";       // 玉佩浣灵 - 只对身上的玉佩进行浣灵


    // ==================== 帮助类命令常量 ====================
    public static final String COMMAND_HELP = "help";                    // 帮助 - 显示游戏帮助信息

    // ==================== 游戏模式命令常量 ====================
    public static final String COMMAND_ENTER_GAME = "enterGame";        // 进入游戏模式 - 进入游戏状态
    public static final String COMMAND_EXIT_GAME = "exitGame";// 退出游戏模式 - 退出游戏状态

    // ==================== 管理员命令常量 ====================
    public static final String COMMAND_ADMIN_CREATE_AREA = "adminCreateArea"; // 创建区服 - 创建新的游戏区服
    public static final String COMMAND_ADMIN_LOAD_AREA = "adminloadArea"; // 加载游戏分区
    public static final String COMMAND_ADMIN_RELOAD = "adminReload";    // 重载配置 - 重新加载游戏配置文件
    public static final String COMMAND_ADMIN_KICK = "adminKick";        // 踢出玩家 - 强制玩家下线
    public static final String COMMAND_ADMIN_BAN = "adminBan";          // 封禁玩家 - 禁止玩家登录
    public static final String COMMAND_ADMIN_ANNOUNCE = "adminAnnounce"; // 系统公告 - 发送全服公告


    /**
     * 解析命令类型
     * @param commandName 标准命令名称
     * @return 命令类型
     */
    public static Integer parseCommandType(String commandName) {
        if (StringUtils.isBlank(commandName)) {
            return 0;
        }

        // 根据标准命令名称确定类型
        switch (commandName) {
            // 查看命令
            case COMMAND_VIEW_STATUS:
            case COMMAND_VIEW_MAP:
            case COMMAND_VIEW_EQUIPMENT:
            case COMMAND_VIEW_INVENTORY:
            case COMMAND_VIEW_RANKING:
            case COMMAND_VIEW_TARGET:
                return CmdConstant.CMD_TYPE_VIEW;

            // 移动命令
            case COMMAND_MOVE_TO:
                return CmdConstant.CMD_TYPE_MOVE;

            // 战斗命令
            case COMMAND_ATTACK:
            case COMMAND_CAST_SKILL:
            case COMMAND_REVIVE:
            case COMMAND_RUN:
                return CmdConstant.CMD_TYPE_BATTLE;

            // 采集命令
            case COMMAND_FISHING:
            case COMMAND_MULBERRY:
            case COMMAND_TEA_PICKING:
            case COMMAND_MINING:
                return CmdConstant.CMD_TYPE_COLLECT;

            // 挂机命令
            case COMMAND_IDLE_MINING:
            case COMMAND_IDLE_FISHING:
            case COMMAND_IDLE_MULBERRY:
            case COMMAND_IDLE_COMBAT:
                return CmdConstant.CMD_TYPE_HOLD;

            // 人物操作命令
            case COMMAND_CREATE_CHARACTER:
            case COMMAND_USE_ITEM:
            case COMMAND_EQUIP_ITEM:
            case COMMAND_STORE_ITEM:
            case COMMAND_RETRIEVE_ITEM:
            case COMMAND_EXCHANGE_CURRENCY:
                return CmdConstant.CMD_TYPE_CHARACTER;

            // 交易命令
            case COMMAND_BUY:
            case COMMAND_SELL:
            case COMMAND_ASK_NPC:
            case COMMAND_CANCEL_SELL:
            case COMMAND_VIEW_MARKET:
                return CmdConstant.CMD_TYPE_TRADE;

            // 装备操作命令
            case COMMAND_UPGRADE_EQUIPMENT:
            case COMMAND_REFINE_JADE:
                return CmdConstant.CMD_TYPE_EQUIPMENT;

            // 系统命令
            case COMMAND_HELP:
            case COMMAND_ENTER_GAME:
            case COMMAND_EXIT_GAME:
                return CmdConstant.CMD_TYPE_GAME;

            // 管理员命令
            case COMMAND_ADMIN_CREATE_AREA:
            case COMMAND_ADMIN_LOAD_AREA:
            case COMMAND_ADMIN_RELOAD:
            case COMMAND_ADMIN_KICK:
            case COMMAND_ADMIN_BAN:
            case COMMAND_ADMIN_ANNOUNCE:
                return CmdConstant.CMD_TYPE_ADMIN;

            default:
                return 0; // 未知命令
        }
    }
}
