package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.QuestProgress;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务进度Mapper接口
 *
 * <AUTHOR>
 */
public interface QuestProgressMapper extends BasicMapper<QuestProgress> {

    /**
     * 根据角色信息查询进行中的任务（去掉quest表关联）
     */
    @Select("SELECT * FROM quest_progress WHERE character_id = #{characterId} AND status IN (1, 2) ORDER BY id")
    List<QuestProgress> selectActiveByCharacter(@Param("characterId") Long characterId);

    /**
     * 根据角色信息和任务ID查询任务进度
     */
    @Select("SELECT * FROM quest_progress WHERE character_id = #{characterId} AND quest_id = #{questId}")
    QuestProgress selectByCharacterAndQuest(@Param("characterId") Long characterId, @Param("questId") Long questId);

    /**
     * 根据角色信息查询所有任务进度
     */
    @Select("SELECT * FROM quest_progress WHERE character_id = #{characterId} ORDER BY id")
    List<QuestProgress> selectAllByCharacter(@Param("characterId") Long characterId);
}