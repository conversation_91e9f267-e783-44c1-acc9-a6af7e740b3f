package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.Map;

/**
 * 战斗配置类
 * 对应 game/battle.yml
 * 
 * <AUTHOR>
 */
@Data
public class BattleConfig {
    
    /**
     * 职业描述配置
     * Key: 职业常量名称, Value: 职业描述
     */
    private Map<String, ClassDescription> classDescriptions;
    
    /**
     * 职业克制关系配置
     */
    private ClassRestraint classRestraint;
    
    /**
     * 经验计算配置
     */
    private ExpCalculation expCalculation;
    
    /**
     * 职业描述配置
     */
    @Data
    public static class ClassDescription {
        /**
         * 职业名称
         */
        private String name;
        
        /**
         * 职业描述
         */
        private String description;
        
        /**
         * 主要属性
         */
        private String primaryAttribute;
        
        /**
         * 职业特色
         */
        private String specialty;
    }
    
    /**
     * 职业克制关系配置
     */
    @Data
    public static class ClassRestraint {
        /**
         * 剑客克制关系
         */
        private RestraintDetail swordsman;
        
        /**
         * 仙师克制关系
         */
        private RestraintDetail mage;
        
        /**
         * 圣僧克制关系
         */
        private RestraintDetail monk;
    }
    
    /**
     * 克制关系详情
     */
    @Data
    public static class RestraintDetail {
        /**
         * 克制的职业
         */
        private String restrains;
        
        /**
         * 被克制的职业
         */
        private String restrainedBy;
        
        /**
         * 克制伤害加成
         */
        private Double damageBonus;
        
        /**
         * 被克制伤害减免
         */
        private Double damageReduction;
    }
    
    /**
     * 经验计算配置
     */
    @Data
    public static class ExpCalculation {
        private Integer baseDamage;// 基础伤害
        private Double  attackFactor;// 攻击力影响因子
        private Double  defenseFactor;// 防御力影响因子
        private Double  levelFactor;// 等级差影响因子
        private Double  criticalRate;// 基础暴击率5%
        private Double  criticalDamage;// 暴击伤害倍数
        private Integer maxLevelDifference;// 最大等级差
        private Double  minExpRate;// 最小经验倍率
        private Double  damageVariance;// 伤害波动范围
    }
    
    // 职业常量（与LevelsConfig保持一致）
    public static final String ROLE_SWORDSMAN = "swordsman";   // 剑客
    public static final String ROLE_MAGE = "mage";             // 仙师
    public static final String ROLE_MONK = "monk";             // 圣僧
    
    // 战斗相关常量
    public static final double DEFAULT_DAMAGE_BONUS = 1.2;     // 默认克制伤害加成
    public static final double DEFAULT_DAMAGE_REDUCTION = 0.8; // 默认被克制伤害减免
    public static final int MAX_LEVEL_DIFFERENCE = 10;         // 最大等级差
    public static final double MIN_EXP_RATE = 0.1;             // 最小经验倍率
    
    // 属性类型常量
    public static final String ATTR_PHYSICAL = "physical";     // 物理属性
    public static final String ATTR_MAGICAL = "magical";       // 法术属性
    public static final String ATTR_BUDDHIST = "buddhist";     // 佛法属性
    
    /**
     * 获取职业克制关系
     */
    public RestraintDetail getRestraintByRole(String roleConstant) {
        switch (roleConstant) {
            case ROLE_SWORDSMAN:
                return classRestraint.getSwordsman();
            case ROLE_MAGE:
                return classRestraint.getMage();
            case ROLE_MONK:
                return classRestraint.getMonk();
            default:
                return null;
        }
    }
    
    /**
     * 计算克制伤害倍率
     */
    public double calculateRestraintDamage(String attackerRole, String defenderRole) {
        RestraintDetail restraint = getRestraintByRole(attackerRole);
        if (restraint == null) {
            return 1.0;
        }
        
        // 检查是否克制目标职业
        if (defenderRole.equals(restraint.getRestrains())) {
            return restraint.getDamageBonus();
        }
        
        // 检查是否被目标职业克制
        if (defenderRole.equals(restraint.getRestrainedBy())) {
            return restraint.getDamageReduction();
        }
        
        return 1.0; // 无克制关系
    }
    
    /**
     * 计算经验倍率
     */
    public double calculateExpRate(int attackerLevel, int defenderLevel) {
        //TODO
        return 1.0;
    }
}
