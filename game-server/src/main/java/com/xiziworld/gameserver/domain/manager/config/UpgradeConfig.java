package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.Map;

/**
 * 升级配置类
 * 对应 game/upgrade.yml
 * 
 * <AUTHOR>
 */
@Data
public class UpgradeConfig {
    
    /**
     * 装备升品配置
     */
    private Equipment equipment;
    
    /**
     * 玉佩浣灵配置
     */
    private JadeRefine jadeRefine;
    
    /**
     * 装备升品配置
     */
    @Data
    public static class Equipment {
        /**
         * 1-3品升品配置
         */
        private UpgradeLevel level13;
        
        /**
         * 4-6品升品配置
         */
        private UpgradeLevel level46;
        
        /**
         * 7-9品升品配置
         */
        private UpgradeLevel level79;
        
        /**
         * 10品以上升品配置
         */
        private UpgradeLevel level10Plus;
    }
    
    /**
     * 玉佩浣灵配置
     */
    @Data
    public static class JadeRefine {
        /**
         * 1-3品浣灵配置
         */
        private RefineLevel level13;
        
        /**
         * 4-6品浣灵配置
         */
        private RefineLevel level46;
        
        /**
         * 7-9品浣灵配置
         */
        private RefineLevel level79;
        
        /**
         * 10品以上浣灵配置
         */
        private RefineLevel level10Plus;
    }
    
    /**
     * 升品等级配置
     */
    @Data
    public static class UpgradeLevel {
        /**
         * 所需材料配置
         * Key: 材料物品编号, Value: 数量公式
         */
        private Map<String, String> materials;
        
        /**
         * 成功率
         */
        private Double successRate;
        
        /**
         * 属性提升比例
         */
        private Double attributeIncrease;
    }
    
    /**
     * 浣灵等级配置
     */
    @Data
    public static class RefineLevel {
        /**
         * 所需材料配置
         * Key: 材料物品编号, Value: 数量公式
         */
        private Map<String, String> materials;
        
        /**
         * 成功率
         */
        private Double successRate;
        
        /**
         * 属性范围配置
         */
        private AttributeRange attributeRange;
    }
    
    /**
     * 属性范围配置
     */
    @Data
    public static class AttributeRange {
        /**
         * 最小属性倍率
         */
        private Double min;
        
        /**
         * 最大属性倍率
         */
        private Double max;
    }
    
    // 升品材料常量
    public static final String MATERIAL_STONE = "ITEM_STONE";          // 雨花石
    public static final String MATERIAL_METEOR = "ITEM_METEOR";        // 天外陨石
    public static final String MATERIAL_SILVER = "ITEM_SILVER";        // 银两
    public static final String MATERIAL_GOLD = "ITEM_GOLD";            // 金币
    
    // 浣灵材料常量
    public static final String MATERIAL_HUANLING = "ITEM_MAT_HUANLING"; // 日月同辉
    public static final String MATERIAL_MONEY_GOLD = "ITEM_MONEY_GOLD"; // 金币
    public static final String MATERIAL_MONEY_SILVER = "ITEM_MONEY_SILVER"; // 银两
    
    // 品级常量
    public static final int GRADE_MIN = 1;                             // 最小品级
    public static final int GRADE_MAX = 15;                            // 最大品级
    public static final int GRADE_RANGE_1_3 = 3;                       // 1-3品
    public static final int GRADE_RANGE_4_6 = 6;                       // 4-6品
    public static final int GRADE_RANGE_7_9 = 9;                       // 7-9品
    public static final int GRADE_RANGE_10_PLUS = 10;                  // 10品以上
    
    /**
     * 根据品级获取装备升品配置
     */
    public UpgradeLevel getEquipmentUpgradeByGrade(int grade) {
        if (grade >= 1 && grade <= 3) {
            return equipment.getLevel13();
        } else if (grade >= 4 && grade <= 6) {
            return equipment.getLevel46();
        } else if (grade >= 7 && grade <= 9) {
            return equipment.getLevel79();
        } else if (grade >= 10) {
            return equipment.getLevel10Plus();
        }
        return null;
    }
    
    /**
     * 根据品级获取玉佩浣灵配置
     */
    public RefineLevel getJadeRefineByGrade(int grade) {
        if (grade >= 1 && grade <= 3) {
            return jadeRefine.getLevel13();
        } else if (grade >= 4 && grade <= 6) {
            return jadeRefine.getLevel46();
        } else if (grade >= 7 && grade <= 9) {
            return jadeRefine.getLevel79();
        } else if (grade >= 10) {
            return jadeRefine.getLevel10Plus();
        }
        return null;
    }
    
    /**
     * 计算材料数量
     */
    public int calculateMaterialCount(String formula, int level) {
        // 简单的公式解析，支持 "level * 数字" 格式
        if (formula.contains("level")) {
            String[] parts = formula.split("\\*");
            if (parts.length == 2) {
                try {
                    int multiplier = Integer.parseInt(parts[1].trim());
                    return level * multiplier;
                } catch (NumberFormatException e) {
                    return 1;
                }
            }
        }
        
        // 尝试直接解析为数字
        try {
            return Integer.parseInt(formula.trim());
        } catch (NumberFormatException e) {
            return 1;
        }
    }
}
