package com.xiziworld.gameserver.domain.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.common.EventPublisher;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.domain.entity.Area;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.entity.Monster;
import com.xiziworld.gameserver.domain.manager.config.*;
import com.xiziworld.gameserver.domain.manager.player.UserCharacterCacheManager;
import com.xiziworld.gameserver.domain.mapper.AreaMapper;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import com.xiziworld.gameserver.domain.mapper.MonsterMapper;

import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import com.xiziworld.gameserver.domain.mapper.UserCharacterMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 地图管理器
 * 负责地图管理、移动逻辑、环境扫描、临时编号机制等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MapManager {
    @Autowired
    private UserCharacterCacheManager characterCacheManager;
    
    @Autowired
    private MonsterMapper monsterMapper;
    
    @Autowired
    private ConfigManager configManager;

    @Autowired
    private EventPublisher eventPublisher;

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private ItemMapper itemMapper;

    // 地图对象缓存： 地图ID -> 游戏分区 -> 编号-> 对象实例
    private final Map<String,Map<Long, Map<Integer, TempObject>>> mapObjectsCache = new ConcurrentHashMap<>();

    // 地图玩家缓存 - 地图ID -> 分区ID-> 玩家信息
    //private final Map<String, Map<Long, List<PlayerMapInfo>>> mapPlayersCache = new ConcurrentHashMap<>();

    // 地图怪物实例缓存 - 地图ID -> 游戏分区 -> 怪物实例列表
    private final Map<String, Map<Long, List<MonsterInstance>>> mapMonstersCache = new ConcurrentHashMap<>();

    // 怪物实例ID生成器
    private final AtomicLong monsterInstanceIdGenerator = new AtomicLong(1000000);
    @Autowired
    private UserCharacterMapper userCharacterMapper;

    /**
     * 服务启动时，地图初始化怪物和NPC和采集点
     */
    @PostConstruct
    public void init(){
        //刷新怪物
        refreshAllMapMonsters();
        //刷新NPC
        refreshAllMapNpcs();
        //刷新采集点
        refreshAllMapCollectAreas();
        //刷新玩家
        refreshAllMapPlayers();
    }


    // ==================== 地图基础功能 ====================

    private final Map<String, Map<Long, Object>> tempIdLocks = new ConcurrentHashMap<>();
    /**
     * 获得当前地图可用的临时编号
     * @param areaId
     * @param mapId
     * @return
     */
    private int getNextTempId(long areaId, String mapId) {
        Object lock = tempIdLocks.computeIfAbsent(mapId, k -> new ConcurrentHashMap<>()).computeIfAbsent(areaId, k -> new Object());
        synchronized (lock) {
            Map<Long, Map<Integer, TempObject>> mapCache = mapObjectsCache.computeIfAbsent(mapId, k -> new ConcurrentHashMap<>());
            Map<Integer, TempObject> areaCache = mapCache.get(areaId);
            if (areaCache == null) {
                areaCache = new ConcurrentHashMap<>();
                mapCache.put(areaId, areaCache);
                return 1;
            }
            for (int i = 1; i <= areaCache.size() + 1; i++) {
                if (!areaCache.containsKey(i)) {
                    //占坑，不让别人抢到
                    areaCache.put(i, new TempObject());
                    return i;
                }
            }
            return areaCache.size() + 1;
        }
    }

    /**
     * 获取地图详细信息
     *
     * @param mapId 地图ID
     * @return 地图信息
     */
    public MapsConfig.MapDetail getMapDetail(String mapId) {
        MapsConfig.MapDetail mapDetail = configManager.getMapsConfig().getMaps().get(mapId);
        if (mapDetail == null) {
            throw new GameException(GameException.MAP_NOT_EXISTS);
        }
        return mapDetail;
    }

    /**
     * 检查两个地图是否相连
     */
    public boolean isMapConnected(String fromMapId, String toMapId) {
        MapsConfig.MapDetail fromMap = getMapDetail(fromMapId);
        if (fromMap.getConnections() == null) {
            return false;
        }
        
        return fromMap.getConnections().stream()
                .anyMatch(conn -> toMapId.equals(conn.getMapId()));
    }

    /**
     * 获取地图连接信息
     */
    public List<MapsConfig.Connection> getMapConnections(String mapId) {
        MapsConfig.MapDetail mapDetail = getMapDetail(mapId);
        return mapDetail.getConnections() != null ? mapDetail.getConnections() : new ArrayList<>();
    }

    // ==================== 移动系统 ====================

    /**
     * 根据地图名称移动（支持中文名称）
     */
    public String moveToMapByName(Long characterId, String mapName) {
        String mapId = getMapIdByName(mapName);
        if (mapId == null) {
            return "大侠想去哪里？江湖传说里没有这个地方: " + mapName;
        }
        UserCharacter character = characterCacheManager.getCharacter(characterId);
        return characterMove(character, getCurrentMapId(characterId), mapId, null, null, false);
    }

    /**
     * 角色在地图变上移动更
     * @param character 玩家信息
     * @param oldMapId 原地图ID（可为null）
     * @param newMapId 新地图ID
     * @param newX 新X坐标
     * @param newY 新Y坐标
     */
    public void characterMove(UserCharacter character, String oldMapId, String newMapId, int newX, int newY) {
        characterMove(character, oldMapId, newMapId, newX, newY, false);
    }

    /**
     * 获取玩家附近可以交易某物品的NPC
     * @param character
     * @param itemName
     * @return
     */
    public NpcsConfig.NpcDetail getNearbyNpcForItem(UserCharacter character, String itemName) {
        try {
            // 获取附近的NPC列表
            List<NpcsConfig.NpcDetail> nearbyNpcs = scanNpcs(character);

            // 获取商店配置
            ShopsConfig shopsConfig = configManager.getShopsConfig();
            if (shopsConfig == null || shopsConfig.getShops() == null) {
                return null;
            }
            Item item = itemMapper.selectByItemName(itemName);
            if(item==null){
                return null;
            }
            // 查找销售该物品的NPC
            for (NpcsConfig.NpcDetail npc : nearbyNpcs) {
                if (npc.getFunctions() != null && npc.getFunctions().contains(NpcsConfig.FUNCTION_SHOP)) {
                    // 查找该NPC对应的商店
                    ShopsConfig.ShopDetail shop = findShopByNpcId(npc.getNpcNo());
                    if (shop != null && shopHasItem(shop, item.getItemNo())) {
                        return npc;
                    }
                }
            }

            return null;

        } catch (Exception e) {
            log.error("根据物品名获取NPC失败: characterId={}, itemName={}", character.getId(), itemName, e);
            return null;
        }
    }

    /**
     * 根据NPC ID查找对应的商店
     */
    public ShopsConfig.ShopDetail findShopByNpcId(String npcId) {
        ShopsConfig shopsConfig = configManager.getShopsConfig();
        if (shopsConfig == null || shopsConfig.getShops() == null) {
            return null;
        }

        for (ShopsConfig.ShopDetail shop : shopsConfig.getShops().values()) {
            if (npcId.equals(shop.getNpcNo())) {
                return shop;
            }
        }

        return null;
    }    /**
     * 检查商店是否销售指定物品
     */
    private boolean shopHasItem(ShopsConfig.ShopDetail shop, String itemNo) {
        if (shop.getItems() == null) {
            return false;
        }

        for (String no : shop.getItems().keySet()) {
            if (itemNo.equals(no)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取玩家距离可交易的NPC
     * 1. 同区同地图
     * 2. 距离小于10米
     */
    public List<NpcsConfig.NpcDetail> scanNpcs(UserCharacter character) {
        List<NpcsConfig.NpcDetail> nearbyNpcs = new ArrayList<>();

        try {
            // 获取角色当前位置
            Map<String, Object> position = getCharacterPosition(character.getId());
            String mapId = (String) position.get("mapId");
            Integer playerX = (Integer) position.get("x");
            Integer playerY = (Integer) position.get("y");

            if (mapId == null || playerX == null || playerY == null) {
                log.debug("角色位置信息不完整: characterId={}, mapId={}, x={}, y={}",
                         character.getId(), mapId, playerX, playerY);
                return nearbyNpcs;
            }

            // 扫描地图中的NPC
            List<TempObject> npcObjects = scanNPCs(character, mapId, playerX, playerY);

            // 获取NPC配置
            NpcsConfig npcsConfig = configManager.getNpcsConfig();
            if (npcsConfig == null || npcsConfig.getNpcs() == null) {
                log.warn("NPC配置不存在");
                return nearbyNpcs;
            }

            // 筛选距离小于10米的NPC，并获取详细配置
            for (TempObject npcObj : npcObjects) {
                if (npcObj.getDistance() <= 20.0) {
                    // 根据objectId获取NPC详细配置
                    NpcsConfig.NpcDetail npcDetail = npcsConfig.getNpcs().get(npcObj.getObjectId());
                    if (npcDetail != null) {
                        nearbyNpcs.add(npcDetail);
                        log.debug("找到可交易NPC: {} ({}), 距离={}米", npcDetail.getName(), npcDetail.getNpcNo(), npcObj.getDistance());
                    }
                }
            }

            log.debug("角色{}附近找到{}个可交易NPC", character.getId(), nearbyNpcs.size());

        } catch (Exception e) {
            log.error("扫描附近NPC失败: characterId={}", character.getId(), e);
        }

        return nearbyNpcs;
    }

    /**
     * 检查与NPC的距离
     * @param userCharacter 用户角色
     * @param npcId NPC ID
     * @return 如果距离检查通过返回null，否则返回错误信息
     */
    public String checkNPCDistance(UserCharacter userCharacter, String npcId) {
        try {
            // 获取附近的NPC列表（距离10米内）
            List<NpcsConfig.NpcDetail> nearbyNpcs = scanNpcs(userCharacter);

            // 检查指定的NPC是否在附近
            for (NpcsConfig.NpcDetail npc : nearbyNpcs) {
                if (npcId.equals(npc.getNpcNo())) {
                    // 找到了指定的NPC，距离检查通过
                    log.debug("NPC距离检查通过: 角色{}与NPC{}在交易距离内",
                             userCharacter.getId(), npcId);
                    return null;
                }
            }

            // 没有找到指定的NPC，说明距离太远或NPC不存在
            log.debug("NPC距离检查失败: 角色{}与NPC{}距离过远或NPC不存在",
                     userCharacter.getId(), npcId);
            return "🚶‍♂️ 距离太远了！请靠近NPC再进行交易！";

        } catch (Exception e) {
            log.error("NPC距离检查异常: characterId={}, npcId={}",
                     userCharacter.getId(), npcId, e);
            return "❌ 位置检查失败，请稍后再试！";
        }
    }

    /**
     * 寻找玩家所在地图最近的名称对象编号
     */
    public int getTempIdByObjectName(UserCharacter character, String objectName) {
        if(objectName==null){
            return 0;
        }
        Map<String, Object> position = getCharacterPosition(character.getId());
        String mapId = (String) position.get("mapId");
        if (mapId == null) {
            return 0;
        }
        Integer playerX = (Integer) position.get("x");
        Integer playerY = (Integer) position.get("y");
        if (playerX == null || playerY == null) {
            return 0;
        }
        List<TempObject> objects = scanMonsters(character, mapId, playerX, playerY);
        for (TempObject obj : objects) {
            if (obj.getName().contains(objectName)) {
                return obj.getTempId();
            }
        }
        objects = scanPlayers(character, mapId, playerX, playerY);
        for (TempObject obj : objects) {
            if (obj.getName().contains(objectName)) {
                return obj.getTempId();
            }
        }
        return 0;
    }
    /**
     * 获取角色位置信息
     */
    public Map<String, Object> getCharacterPosition(Long characterId) {
        UserCharacter character = characterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        return Helper.getCharacterPosition(character);

    }
    /**
     * 根据临时编号移动到目标位置附近
     */
    public String moveToTempObject(Long characterId, int tempId) {
        TempObject tempObject = getTempObject(characterId, tempId);
        if (tempObject == null) {
            return "编号" + tempId + "不存在或已过期";
        }
        // 计算移动到目标附近的位置
        int targetX = tempObject.getX() + new Random().nextInt(20) - 10; // ±10米范围
        int targetY = tempObject.getY() + new Random().nextInt(20) - 10;
        
        // 更新角色位置
        eventPublisher.publishPositionEvent(characterId, targetX, targetY, tempObject.getMapId());
        
        return "🚶 你移动到了" + tempObject.getName() + "附近";
    }

    // ==================== 环境扫描系统 ====================

    /**
     * 扫描当前地图环境
     * 
     * @param character 角色ID
     * @return 环境扫描结果
     */
    public String scanEnvironment(UserCharacter character) {
        log.info("环境扫描: characterId={}", character.getId());
        
        // 获取角色当前位置
        Map<String, Object> position = getCharacterPosition(character.getId());
        String mapId = (String) position.get("mapId");
        Integer playerX = (Integer) position.get("x");
        Integer playerY = (Integer) position.get("y");
        
        if (mapId == null) {
            return "你当前不在任何地图中";
        }
        
        StringBuilder result = new StringBuilder();
        result.append("🔍 周围有：\n");
        List<TempObject> objects = new ArrayList<>();
        // 扫描怪物
        objects.addAll(scanMonsters(character, mapId, playerX, playerY));
        // 扫描NPC
        objects.addAll(scanNPCs(character, mapId, playerX, playerY));
        // 扫描其他玩家
        objects.addAll(scanPlayers(character, mapId, playerX, playerY));
        // 扫描采集点
        objects.addAll(scanCollectAreas(character, mapId, playerX, playerY));
        // 按距离排序
        objects.sort(Comparator.comparingDouble(TempObject::getDistance));

        if (objects.isEmpty()) {
            result.append("暂时没有发现任何东西\n");
        } else {
            for (TempObject obj : objects) {
                result.append(String.format("  %d. %s (%.0f米) %s\n", obj.getTempId(), obj.getName(), obj.getDistance(), obj.getStatus()));
            }
        }
        
        result.append("\n💡 使用编号交互，如：攻击 1、前往 3");
        
        return result.toString();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 检查地图等级限制
     */
    private String checkMapLevelRequirement(UserCharacter character, String mapId) {
        MapsConfig.MapDetail mapDetail = getMapDetail(mapId);
        if (mapDetail.getProperties() != null && mapDetail.getProperties().getLevelRange() != null) {
            MapsConfig.LevelRange levelRange = mapDetail.getProperties().getLevelRange();
            int playerLevel = character.getLevel();
            
            if (playerLevel < levelRange.getMin()) {
                return "你的等级太低，无法进入" + mapDetail.getName() + "（需要等级" + levelRange.getMin() + "）";
            }
        }
        return null;
    }

    /**
     * 根据地图名称获取地图ID
     */
    private String getMapIdByName(String mapName) {
        // 支持中文名称和英文ID
        switch (mapName.toLowerCase().trim()) {
            case "武林":
            case "武林主城":
            case "wulin":
                return MapsConfig.MAP_WULIN;
            case "苏堤":
            case "sudi":
                return MapsConfig.MAP_SUDI;
            case "断桥":
            case "断桥残雪":
            case "duanqiao":
                return MapsConfig.MAP_DUANQIAO;
            case "孤山":
            case "gushan":
                return MapsConfig.MAP_GUSHAN;
            case "太子湾":
            case "taiziwwan":
                return MapsConfig.MAP_TAIZIWWAN;
            case "保俶山":
            case "baoshushan":
                return MapsConfig.MAP_BAOSHUSHAN;
            case "梅家坞":
            case "meijiawu":
                return MapsConfig.MAP_MEIJIAWU;
            case "龙井村":
            case "longjingcun":
                return MapsConfig.MAP_LONGJINGCUN;
            case "雷峰塔":
            case "雷峰塔底":
            case "leifengta":
                return MapsConfig.MAP_LEIFENGTA;
            case "湖心亭":
            case "huxinting":
                return MapsConfig.MAP_HUXINTING;
            default:
                return null;
        }
    }

    /**
     * 扫描怪物（从缓存中获取怪物实例）
     */
    private List<TempObject> scanMonsters(UserCharacter character, String mapId, Integer playerX, Integer playerY) {
        List<TempObject> monsters = new ArrayList<>();

        try {
            // 从地图怪物缓存中获取怪物实例（需要分区ID，暂时使用默认分区）
            Long areaId = character.getAreaId();
            List<MonsterInstance> monstersInMap = getMonstersInMap(areaId, mapId);

            for (MonsterInstance monsterInstance : monstersInMap) {
                // 只显示存活的怪物
                if (!monsterInstance.isAlive() || monsterInstance.getCurrentHp() <= 0) {
                    continue;
                }

                // 计算距离，只显示一定范围内的怪物（比如1000米内）
                int distance = (int) calculateDistance(playerX, playerY, monsterInstance.getX(), monsterInstance.getY());
                if (distance > 1000) {
                    continue;
                }

                TempObject monsterObj = new TempObject();
                monsterObj.setObjectId(monsterInstance.getInstanceId().toString());  // 使用实例ID作为对象ID
                monsterObj.setName(monsterInstance.getMonsterName());
                monsterObj.setType(TempObject.TYPE_MONSTER);
                monsterObj.setMapId(mapId);
                monsterObj.setX(monsterInstance.getX());
                monsterObj.setY(monsterInstance.getY());
                monsterObj.setDistance(distance);

                // 使用怪物实例的临时编号
                monsterObj.setTempId(monsterInstance.getTempId());

                // 根据怪物血量设置状态
                String status = "🔴可攻击";
                double hpPercent = (double) monsterInstance.getCurrentHp() / monsterInstance.getMaxHp();
                if (hpPercent < 0.3) {
                    status = "🟡重伤";
                } else if (hpPercent < 0.7) {
                    status = "🟠轻伤";
                }

                // 从怪物模板获取等级信息
                Monster template = getMonsterTemplate(monsterInstance.getMonsterId());
                int level = template != null ? template.getLevel() : 1;
                monsterObj.setStatus(status + " Lv." + level);

                monsters.add(monsterObj);

                log.debug("扫描到怪物实例: {} ({}), 距离={}米, 血量={}/{}",
                         monsterInstance.getMonsterName(), monsterInstance.getInstanceId(),
                         distance, monsterInstance.getCurrentHp(), monsterInstance.getMaxHp());
            }

            // 按距离排序，近的在前
            monsters.sort((m1, m2) -> Double.compare(m1.getDistance(), m2.getDistance()));

        } catch (Exception e) {
            log.error("扫描怪物失败: mapId={}", mapId, e);
        }

        return monsters;
    }

    /**
     * 获取怪物最大血量（用于显示血量状态）
     */
    private int getMonsterMaxHp(Monster monster) {
        return monster.getHp(); // 直接使用怪物的血量作为最大血量
    }

    /**
     * 获取地图中的所有怪物（基于配置中的怪物类型）
     */
    private List<Monster> getMapMonsters(String mapId) {
        List<Monster> mapMonsters = new ArrayList<>();

        // 获取地图刷新配置
        MapRefreshConfig refreshConfig = configManager.getMapRefreshConfig();
        if (refreshConfig == null || refreshConfig.getMapRefresh() == null) {
            return mapMonsters;
        }

        MapRefreshConfig.MapRefreshDetail mapDetail = refreshConfig.getMapRefresh().get(mapId);
        if (mapDetail == null || mapDetail.getRefreshPoints() == null) {
            return mapMonsters;
        }

        // 遍历每个刷新点，获取对应的怪物
        for (MapRefreshConfig.RefreshPoint point : mapDetail.getRefreshPoints()) {
            String monsterNo = point.getMonsterNo();
            // 查找该类型的所有怪物实例
            List<Monster> monsters = monsterMapper.selectByMonsterNoPrefix(monsterNo);
            mapMonsters.addAll(monsters);
        }

        return mapMonsters;
    }
    /**
     * 刷新所有地图的NPC
     */
    private void refreshAllMapNpcs() {
        log.info("开始刷新所有地图NPC");

        // 获取所有地图配置
        MapsConfig mapsConfig = configManager.getMapsConfig();
        if (mapsConfig != null && mapsConfig.getMaps() != null) {
            for (String mapId : mapsConfig.getMaps().keySet()) {
                refreshMapNpcs(mapId);
            }
        }

        log.info("所有地图NPC刷新完成");
    }

    /**
     * 刷新所有地图的采集点
     */
    private void refreshAllMapCollectAreas() {
        log.info("开始刷新所有地图采集点");

        // 获取所有地图配置
        MapsConfig mapsConfig = configManager.getMapsConfig();
        if (mapsConfig != null && mapsConfig.getMaps() != null) {
            for (String mapId : mapsConfig.getMaps().keySet()) {
                refreshMapCollectAreas(mapId);
            }
        }

        log.info("所有地图采集点刷新完成");
    }
    private void refreshAllMapPlayers(){
        log.info("开始刷新所有地图玩家");

        Map<String,List<UserCharacter>> playersInMap = getPlayersInMap();
        // 获取所有地图配置
        MapsConfig mapsConfig = configManager.getMapsConfig();
        if (mapsConfig != null && mapsConfig.getMaps() != null) {
            for (String mapId : mapsConfig.getMaps().keySet()) {
                refreshMapPlayers(mapId,playersInMap.get(mapId));
            }
        }
        log.info("所有地图玩家刷新完成");
    }
    private Map<String,List<UserCharacter>> getPlayersInMap() {
        Map<String,List<UserCharacter>> playersInMap = new HashMap<>();
        userCharacterMapper.getAllOnlinePlayers().forEach(character -> {
            String mapId = Helper.getCurrentMapId(character);
            playersInMap.computeIfAbsent(mapId, k -> new ArrayList<>());
            playersInMap.get(mapId).add(character);
        });
        return playersInMap;
    }
    private void refreshMapPlayers(String mapId, List<UserCharacter> players) {
        log.info("刷新地图玩家: mapId={}", mapId);
        if(players==null){
            return;
        }
        for (UserCharacter character : players) {
            Map<String, Object> position = Helper.getCharacterPosition(character);
            int x = Integer.parseInt(position.get("x").toString());
            int y = Integer.parseInt(position.get("y").toString());
            addPlayerToMap(character, mapId, x, y);
        }
    }

    /**
     * 刷新指定地图的NPC
     */
    private void refreshMapNpcs(String mapId) {
        log.info("刷新地图NPC: mapId={}", mapId);

        try {
            // 获取地图详细信息
            MapsConfig.MapDetail mapDetail = getMapDetail(mapId);
            if (mapDetail == null || mapDetail.getNpcs() == null) {
                log.debug("地图{}没有NPC配置", mapId);
                return;
            }

            // 获取所有游戏分区
            List<Area> areas = areaMapper.selectAll();

            for (Area area : areas) {
                Long areaId = area.getId();
                // 创建NPC
                for (String npcId : mapDetail.getNpcs()) {
                    createNpcInMap(areaId, mapId, npcId);
                }
            }

            log.info("地图{}NPC刷新完成", mapId);

        } catch (Exception e) {
            log.error("刷新地图NPC失败: mapId={}", mapId, e);
        }
    }

    /**
     * 刷新指定地图的采集点
     */
    private void refreshMapCollectAreas(String mapId) {
        log.info("刷新地图采集点: mapId={}", mapId);

        try {
            // 获取该地图的采集点配置
            Map<String, CollectConfig.CollectType> collectTypes =
                configManager.getCollectConfig().getCollectTypesByMapId(mapId);

            if (collectTypes == null || collectTypes.isEmpty()) {
                log.debug("地图{}没有采集点配置", mapId);
                return;
            }

            // 获取所有游戏分区
            List<Area> areas = areaMapper.selectAll();

            for (Area area : areas) {
                Long areaId = area.getId();
                // 创建采集点
                for (String collectType : collectTypes.keySet()) {
                    createCollectAreaInMap(areaId, mapId, collectType);
                }
            }

            log.info("地图{}采集点刷新完成", mapId);

        } catch (Exception e) {
            log.error("刷新地图采集点失败: mapId={}", mapId, e);
        }
    }
    /**
     * 在地图中创建NPC
     */
    private void createNpcInMap(Long areaId, String mapId, String npcId) {
        try {
            // 获取NPC配置
            NpcsConfig.NpcDetail npcDetail = null;
            if (configManager.getNpcsConfig() != null && configManager.getNpcsConfig().getNpcs() != null) {
                npcDetail = configManager.getNpcsConfig().getNpcs().get(npcId);
            }

            if (npcDetail == null) {
                log.warn("找不到NPC配置: {}", npcId);
                return;
            }

            // 获取下一个可用的tempId
            int tempId = getNextTempId(areaId, mapId);

            // 创建NPC对象
            TempObject npc = new TempObject();
            npc.setObjectId(npcDetail.getNpcNo());
            npc.setName(npcDetail.getName());
            npc.setType(TempObject.TYPE_NPC);
            npc.setMapId(mapId);
            npc.setTempId(tempId);

            // 设置坐标
            if (npcDetail.getCoordinates() != null) {
                npc.setX(npcDetail.getCoordinates().getX());
                npc.setY(npcDetail.getCoordinates().getY());
            } else {
                // 使用地图中心点附近的随机坐标
                npc.setX(500 + new Random().nextInt(200) - 100);
                npc.setY(500 + new Random().nextInt(200) - 100);
            }

            // 根据NPC功能设置状态
            String status = "🏪可对话";
            if (npcDetail.getFunctions() != null && !npcDetail.getFunctions().isEmpty()) {
                String firstFunction = npcDetail.getFunctions().get(0);
                switch (firstFunction) {
                    case "shop":
                    case "trade":
                        status = "🛒商人";
                        break;
                    case "quest":
                    case "task":
                        status = "❗任务";
                        break;
                    case "story":
                        status = "🏪可对话";
                        break;
                    default:
                        status = "🏪可对话";
                        break;
                }
            }
            npc.setStatus(status);

            // 添加到缓存
            mapObjectsCache.computeIfAbsent(mapId, k -> new ConcurrentHashMap<>())
                          .computeIfAbsent(areaId, k -> new ConcurrentHashMap<>())
                          .put(tempId, npc);

            log.debug("创建NPC: {} ({}), 坐标=({},{}), tempId={}", npcDetail.getName(), npcId, npc.getX(), npc.getY(), tempId);

        } catch (Exception e) {
            log.error("创建NPC失败: npcId={}", npcId, e);
        }
    }

    /**
     * 在地图中创建采集点
     */
    private void createCollectAreaInMap(Long areaId, String mapId, String collectType) {
        try {
            // 获取采集点配置
            CollectConfig.CollectType collectConfig =
                configManager.getCollectConfig().getCollectTypesByMapId(mapId).get(collectType);

            if (collectConfig == null) {
                log.warn("找不到采集点配置: mapId={}, collectType={}", mapId, collectType);
                return;
            }

            // 获取下一个可用的tempId
            int tempId = getNextTempId(areaId, mapId);

            // 创建采集点对象
            TempObject collectArea = new TempObject();
            collectArea.setObjectId("collect_" + collectType);
            collectArea.setName(CollectConfig.getNameByType(collectType));
            collectArea.setType(TempObject.TYPE_COLLECT);
            collectArea.setMapId(mapId);
            collectArea.setTempId(tempId);

            // 设置随机坐标（在地图中心点附近）
            collectArea.setX(500 + new Random().nextInt(300) - 150);
            collectArea.setY(500 + new Random().nextInt(300) - 150);
            collectArea.setStatus("📦可采集");

            // 添加到缓存
            mapObjectsCache.computeIfAbsent(mapId, k -> new ConcurrentHashMap<>())
                          .computeIfAbsent(areaId, k -> new ConcurrentHashMap<>())
                          .put(tempId, collectArea);

            log.debug("创建采集点: {} ({}), 坐标=({},{}), tempId={}", collectArea.getName(), collectType, collectArea.getX(), collectArea.getY(), tempId);

        } catch (Exception e) {
            log.error("创建采集点失败: collectType={}", collectType, e);
        }
    }

    /**
     * 扫描NPC（从缓存中获取）
     */
    private List<TempObject> scanNPCs(UserCharacter character, String mapId, Integer playerX, Integer playerY) {
        List<TempObject> npcs = new ArrayList<>();

        // 从角色信息中获取areaId
        Long areaId = character.getAreaId();

        // 从缓存中获取该地图该分区的所有对象
        Map<Long, Map<Integer, TempObject>> mapCache = mapObjectsCache.get(mapId);
        if (mapCache != null) {
            Map<Integer, TempObject> areaCache = mapCache.get(areaId);
            if (areaCache != null) {
                // 筛选出NPC类型的对象
                for (TempObject obj : areaCache.values()) {
                    if (obj.getType() == TempObject.TYPE_NPC) {
                        // 创建副本，避免修改缓存中的原对象
                        TempObject npc = new TempObject();
                        npc.setObjectId(obj.getObjectId());
                        npc.setName(obj.getName());
                        npc.setType(obj.getType());
                        npc.setMapId(obj.getMapId());
                        npc.setX(obj.getX());
                        npc.setY(obj.getY());
                        npc.setStatus(obj.getStatus());
                        npc.setTempId(obj.getTempId());

                        // 计算距离
                        npc.setDistance((int) calculateDistance(playerX, playerY, npc.getX(), npc.getY()));

                        npcs.add(npc);
                        log.debug("从缓存扫描到NPC: {} ({}), 坐标=({},{}), tempId={}", npc.getName(), npc.getObjectId(), npc.getX(), npc.getY(), npc.getTempId());
                    }
                }
            }
        }

        return npcs;
    }

    /**
     * 扫描其他玩家
     */
    private List<TempObject> scanPlayers(UserCharacter character, String mapId, Integer playerX, Integer playerY) {
        List<TempObject> players = new ArrayList<>();

        try {
            List<TempObject> playersInMap = getPlayersInMap(character.getAreaId(), mapId);

            for (TempObject playerInfo : playersInMap) {
                long id = Long.parseLong(playerInfo.getObjectId());
                // 排除当前玩家自己
                if (id==character.getId()) {
                    continue;
                }

                // 死亡、离线、被禁角色不显示
                UserCharacter userCharacter = characterCacheManager.getCharacter(id);
                if (userCharacter.getStatus()==UserCharacter.STATUS_DEAD
                        || userCharacter.getGameStatus()==UserCharacter.GAME_STATUS_BANNED
                        || userCharacter.getGameStatus()==UserCharacter.GAME_STATUS_OFFLINE
                ) {
                    continue;
                }

                // 计算距离，只显示一定范围内的玩家（比如500米内）
                int distance = (int) calculateDistance(playerX, playerY, playerInfo.getX(), playerInfo.getY());
                if (distance > 500) {
                    continue;
                }

                // 创建临时对象
                TempObject player = new TempObject();
                player.setObjectId(playerInfo.getObjectId());
                player.setName(playerInfo.getName());
                player.setType(TempObject.TYPE_PLAYER);
                player.setMapId(mapId);
                player.setX(playerInfo.getX());
                player.setY(playerInfo.getY());
                player.setDistance(distance);
                player.setTempId(playerInfo.getTempId());

                // 设置玩家状态
                String status = "👤玩家";
                if (distance <= 50) {
                    status = "👤玩家(近)";
                } else if (distance <= 200) {
                    status = "👤玩家(中)";
                } else {
                    status = "👤玩家(远)";
                }
                player.setStatus(status);

                players.add(player);

                log.debug("扫描到玩家: {} ({}), 距离={}米", playerInfo.getName(), playerInfo.getObjectId(), distance);
            }

            // 按距离排序，近的在前
            players.sort((p1, p2) -> Double.compare(p1.getDistance(), p2.getDistance()));

        } catch (Exception e) {
            log.error("扫描其他玩家失败: currentCharacterId={}, mapId={}", character.getId(), mapId, e);
        }

        return players;
    }

    /**
     * 扫描采集点（从缓存中获取）
     */
    private List<TempObject> scanCollectAreas(UserCharacter character, String mapId, Integer playerX, Integer playerY) {
        List<TempObject> collectAreas = new ArrayList<>();

        // 从角色信息中获取areaId
        Long areaId = character.getAreaId();

        // 从缓存中获取该地图该分区的所有对象
        Map<Long, Map<Integer, TempObject>> mapCache = mapObjectsCache.get(mapId);
        if (mapCache != null) {
            Map<Integer, TempObject> areaCache = mapCache.get(areaId);
            if (areaCache != null) {
                // 筛选出采集点类型的对象
                for (TempObject obj : areaCache.values()) {
                    if (obj.getType() == TempObject.TYPE_COLLECT) {
                        // 创建副本，避免修改缓存中的原对象
                        TempObject collectArea = new TempObject();
                        collectArea.setObjectId(obj.getObjectId());
                        collectArea.setName(obj.getName());
                        collectArea.setType(obj.getType());
                        collectArea.setMapId(obj.getMapId());
                        collectArea.setX(obj.getX());
                        collectArea.setY(obj.getY());
                        collectArea.setStatus(obj.getStatus());
                        collectArea.setTempId(obj.getTempId());

                        // 计算距离
                        collectArea.setDistance((int) calculateDistance(playerX, playerY, collectArea.getX(), collectArea.getY()));

                        collectAreas.add(collectArea);
                        log.debug("从缓存扫描到采集点: {} ({}), 坐标=({},{}), tempId={}",
                                 collectArea.getName(), collectArea.getObjectId(), collectArea.getX(), collectArea.getY(), collectArea.getTempId());
                    }
                }
            }
        }

        return collectAreas;
    }

    /**
     * 计算两点间距离
     */
    private double calculateDistance(Integer x1, Integer y1, Integer x2, Integer y2) {
        if (x1 == null || y1 == null || x2 == null || y2 == null) {
            return 0.0;
        }
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    }

    /**
     * 根据NPC ID获取NPC名称
     */
    private String getNpcNameById(String npcId) {
        switch (npcId) {
            case "qianchu": return "钱镠";
            case "yuefei": return "岳飞";
            case "dingren": return "丁仁";
            case "jigong": return "济公";
            case "xuxian": return "许仙";
            case "songwusao": return "宋五嫂";
            default: return npcId;
        }
    }

    // ==================== 临时编号管理 ====================

    /**
     * 根据临时编号获取对象
     */
    public TempObject getTempObject(long characterId, int tempId) {
        
        // 获取角色当前地图
        Map<String, Object> position = getCharacterPosition(characterId);
        String mapId = (String) position.get("mapId");
        if (mapId == null) {
            return null;
        }
        UserCharacter character = characterCacheManager.getCharacter(characterId);
        if (character == null) {
            return null;
        }

        Map<Long, Map<Integer, TempObject>> mapCache = mapObjectsCache.get(mapId);
        if (mapCache == null) {
            return null;
        }

        Map<Integer, TempObject> areaCache = mapCache.get(character.getAreaId());
        if (areaCache == null) {
            return null;
        }
        
        return areaCache.get(tempId);
    }

    /**
     * 清除玩家在旧地图的信息
     */
    private void removePlayerFromMap(UserCharacter character, String mapId) {
        if (mapId == null) return;

        Map<Long, Map<Integer, TempObject>> mapCache = mapObjectsCache.get(mapId);
        if (mapCache == null) {
            return;
        }

        Map<Integer, TempObject> areaCache = mapCache.get(character.getAreaId());
        if (areaCache == null) {
            return;
        }
        areaCache.values().removeIf(obj -> obj.getObjectId().equals(character.getId().toString()));
        log.debug("从地图缓存移除玩家: characterId={}, mapId={}", character.getId(), mapId);
    }

    // ==================== 地图信息查询 ====================
    /**
     * 获取地图详细信息（用于查看地图命令）
     */
    public String getMapDetailInfo(Long characterId) {
        UserCharacter character = characterCacheManager.getCharacter(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 获取角色当前位置
        Map<String, Object> position = getCharacterPosition(characterId);
        String mapId = (String) position.get("mapId");

        if (mapId == null) {
            return "你当前不在任何地图中";
        }

        MapsConfig.MapDetail mapDetail = getMapDetail(mapId);
        StringBuilder result = new StringBuilder();

        result.append("🗺️ ").append(mapDetail.getName()).append("\n\n");
        result.append("📍 ").append(mapDetail.getDescription()).append("\n\n");

        // 显示可前往的地点
        List<MapsConfig.Connection> connections = getMapConnections(mapId);
        if (!connections.isEmpty()) {
            result.append("🚪 可前往的地点：\n");
            for (MapsConfig.Connection conn : connections) {
                MapsConfig.MapDetail targetMap = getMapDetail(conn.getMapId());
                result.append("  • ").append(targetMap.getName())
                      .append(" (").append(conn.getDirection()).append(")\n");
            }
            result.append("\n");
        }

        // 显示地图属性
        if (mapDetail.getProperties() != null) {
            MapsConfig.MapProperties props = mapDetail.getProperties();
            result.append("🏛️ 地图属性：\n");

            if (props.getSafeZone() != null && props.getSafeZone()) {
                result.append("  • 安全区域\n");
            }
            if (props.getPvpEnabled() != null && props.getPvpEnabled()) {
                result.append("  • 允许PK\n");
            }
            if (props.getLevelRange() != null) {
                result.append("  • 等级限制：").append(props.getLevelRange().getMin())
                      .append("-").append(props.getLevelRange().getMax()).append("级\n");
            }
        }

        return result.toString();
    }

    /**
     * 获取角色当前地图ID
     */
    public String getCurrentMapId(Long characterId) {
        Map<String, Object> position = getCharacterPosition(characterId);
        return (String) position.get("mapId");
    }

    /**
     * 检查角色是否在安全区
     */
    public boolean isInSafeZone(Long characterId) {
        String mapId = getCurrentMapId(characterId);
        if (mapId == null) {
            return false;
        }

        MapsConfig.MapDetail mapDetail = getMapDetail(mapId);
        return mapDetail.getProperties() != null &&
               mapDetail.getProperties().getSafeZone() != null &&
               mapDetail.getProperties().getSafeZone();
    }


    // ==================== 地图刷新和管理 ====================

    /**
     * 刷新地图怪物（定时任务调用）
     */
    @Transactional
    public void refreshMapMonsters(String mapId) {
        log.info("刷新地图怪物: mapId={}", mapId);

        try {
            // 获取地图刷新配置
            MapRefreshConfig refreshConfig = configManager.getMapRefreshConfig();
            if (refreshConfig == null || refreshConfig.getMapRefresh() == null) {
                log.debug("没有地图刷新配置");
                return;
            }

            MapRefreshConfig.MapRefreshDetail mapDetail = refreshConfig.getMapRefresh().get(mapId);
            if (mapDetail == null || mapDetail.getRefreshPoints() == null) {
                log.debug("地图{}没有刷新配置", mapId);
                return;
            }

            // 遍历每个刷新点
            List<Area> areas = areaMapper.selectAll();
            for (MapRefreshConfig.RefreshPoint point : mapDetail.getRefreshPoints()) {
                for(Area area : areas) {
                    refreshMonsterAtPoint(area.getId(), mapId, point);
                }
            }

            log.info("地图{}怪物刷新检查完成", mapId);

        } catch (Exception e) {
            log.error("刷新地图怪物失败: mapId={}", mapId, e);
        }
    }

    /**
     * 在指定刷新点刷新怪物（使用缓存）
     */
    private void refreshMonsterAtPoint(Long areaId, String mapId, MapRefreshConfig.RefreshPoint point) {
        try {
            String monsterNo = point.getMonsterNo();
            int maxCount = point.getCount() != null ? point.getCount() : 1;

            // 从缓存中检查该类型怪物的当前数量
            List<MonsterInstance> monstersInMap = getMonstersInMap(areaId,mapId);
            if (monstersInMap == null) {
                monstersInMap = new ArrayList<>();
            }
            long aliveCount = monstersInMap.stream()
                    .filter(monster -> monster.getMonsterId().equals(monsterNo) && monster.isAlive())
                    .count();

            // 清理死亡的怪物实例
            monstersInMap.removeIf(monster -> monster.getMonsterId().equals(monsterNo) && !monster.isAlive());

            // 计算需要刷新的数量
            int needRefresh = maxCount - (int) aliveCount;

            if (needRefresh > 0) {
                // 从数据库获取怪物模板
                Monster template = monsterMapper.selectByMonsterNo(monsterNo);
                if (template == null) {
                    log.warn("找不到怪物模板: {}", monsterNo);
                    return;
                }
                // 刷新新的怪物实例
                for (int i = 0; i < needRefresh; i++) {
                    int tempId = getNextTempId(areaId, mapId);
                    MonsterInstance newMonster = createMonsterInstanceFromTemplate(template, point,areaId, mapId,tempId);
                    addMonsterToMap(areaId,mapId, newMonster);
                    log.debug("刷新怪物实例: {} 在位置({}, {})", newMonster.getMonsterName(), newMonster.getX(), newMonster.getY());
                }

                log.info("在{}刷新了{}只{}", mapId, needRefresh, template.getName());
            }

        } catch (Exception e) {
            log.error("刷新点怪物失败: mapId={}, monsterNo={}", mapId, point.getMonsterNo(), e);
        }
    }

    /**
     * 重生怪物实例
     */
    private void respawnMonster(String mapId, MonsterInstance monster, MapRefreshConfig.RefreshPoint point) {
        // 重置怪物状态
        monster.setCurrentHp(monster.getMaxHp());
        monster.setAlive(true);
        monster.setSpawnTime(System.currentTimeMillis());
        monster.setLastUpdateTime(System.currentTimeMillis());

        // 重新设置位置（可以是固定位置或随机位置）
        if (point.getPosition() != null) {
            Random random = new Random();
            monster.setX(point.getPosition().getX() + random.nextInt(100) - 50); // ±50米范围
            monster.setY(point.getPosition().getY() + random.nextInt(100) - 50);
        }
    }

    /**
     * 根据模板创建新怪物实例（缓存版本）
     */
    private MonsterInstance createMonsterInstanceFromTemplate(Monster template, MapRefreshConfig.RefreshPoint point, Long areaId,String mapId,int tempId) {
        Long instanceId = generateMonsterInstanceId();

        // 计算位置
        int x = 50, y = 50; // 默认位置
        if (point.getPosition() != null) {
            Random random = new Random();
            x = point.getPosition().getX() + random.nextInt(100) - 50; // ±50米范围
            y = point.getPosition().getY() + random.nextInt(100) - 50;
        }


        return new MonsterInstance(
            instanceId,
            template.getMonsterNo(),
            template.getName(),
            areaId,
            mapId,
            tempId,
            Math.abs(x), Math.abs(y),
            template.getHp()
        );
    }

    /**
     * 刷新所有地图的怪物
     */
    public void refreshAllMapMonsters() {
        log.info("开始刷新所有地图怪物");
        // 从配置中获取所有有刷新配置的地图
        MapRefreshConfig refreshConfig = configManager.getMapRefreshConfig();
        if (refreshConfig != null && refreshConfig.getMapRefresh() != null) {
            for (String mapId : refreshConfig.getMapRefresh().keySet()) {
                refreshMapMonsters(mapId);
            }
        }
        log.info("所有地图怪物刷新完成");
    }

    /**
     * 根据模板创建新怪物实例
     */
    private Monster createMonsterFromTemplate(Monster template, MapRefreshConfig.RefreshPoint point) {
        Monster newMonster = new Monster();

        // 生成唯一的怪物编号
        String uniqueNo = template.getMonsterNo() + "_" + System.currentTimeMillis() + "_" +
                         (int)(Math.random() * 1000);
        newMonster.setMonsterNo(uniqueNo);
        // 复制模板属性
        newMonster.setName(template.getName());
        newMonster.setDescription(template.getDescription());
        newMonster.setLevel(template.getLevel());
        newMonster.setHp(template.getHp()); // 满血状态
        newMonster.setAttackW(template.getAttackW());
        newMonster.setAttackM(template.getAttackM());
        newMonster.setAttackF(template.getAttackF());
        newMonster.setDefenseW(template.getDefenseW());
        newMonster.setDefenseM(template.getDefenseM());
        newMonster.setDefenseF(template.getDefenseF());
        newMonster.setType(template.getType());
        return newMonster;
    }



    // ==================== 地图玩家缓存管理 ====================
    /**
     * 角色地图变更（复活、移动等）
     * @param character 玩家
     * @param oldMapId 原地图ID（可为null）
     * @param newMapId 新地图ID
     * @param newX 新X坐标（如果为null则随机生成）
     * @param newY 新Y坐标（如果为null则随机生成）
     * @param isRevive 是否为复活（复活不检查地图连接和等级限制）
     * @return 移动结果信息，如果是缓存更新则返回null
     */
    public String characterMove(UserCharacter character, String oldMapId, String newMapId, Integer newX, Integer newY, boolean isRevive) {
        if(character == null) {
            return "角色不存在";
        }
        try {
            // 1. 检查是否需要移动
            if (Objects.equals(oldMapId, newMapId) && newX != null && newY != null) {
                //playerManager.updateCharacterPosition(character.getId(), newMapId, newX, newY);
                eventPublisher.publishPositionEvent(character.getId(), newX, newY, newMapId);
                log.debug("同地图内移动: characterId={}, mapId={}, newPos=({},{})", character.getId(), newMapId, newX, newY);
                return null; // 同地图移动不返回结果信息
            }

            // 2. 检查地图连接（如果不是复活且不是从武林主城出发）
            if (!isRevive && oldMapId != null && !MapsConfig.MAP_WULIN.equals(oldMapId)) {
                if (!isMapConnected(oldMapId, newMapId)) {
                    MapsConfig.MapDetail oldMap = getMapDetail(oldMapId);
                    MapsConfig.MapDetail newMap = getMapDetail(newMapId);
                    return "无法从" + (oldMap != null ? oldMap.getName() : oldMapId) +
                           "直接前往" + (newMap != null ? newMap.getName() : newMapId);
                }
            }

            // 3. 检查地图等级限制（复活不检查等级限制）
            if (!isRevive) {
                String levelCheckResult = checkMapLevelRequirement(character, newMapId);
                if (levelCheckResult != null) {
                    return levelCheckResult;
                }
            }

            // 4. 计算随机位置（如果坐标为空）
            int finalX = newX != null ? newX : 0;
            int finalY = newY != null ? newY : 0;

            if (newX == null || newY == null) {
                MapsConfig.MapDetail targetMap = getMapDetail(newMapId);
                if (targetMap != null && targetMap.getCoordinates() != null) {
                    Random random = new Random();
                    finalX = targetMap.getCoordinates().getX() + random.nextInt(100) - 50; // ±50米范围
                    finalY = targetMap.getCoordinates().getY() + random.nextInt(100) - 50;
                } else {
                    // 默认坐标
                    finalX = 50;
                    finalY = 50;
                }
            }

            // 5. 清除旧地图缓存信息
            if (oldMapId != null && !oldMapId.isEmpty()) {
                removePlayerFromMap(character, oldMapId);
                log.debug("从原地图移除玩家: characterId={}, oldMapId={}", character.getId(), oldMapId);
            }
            // 6. 更新人物位置信息
            //playerManager.updateCharacterPosition(character.getId(), newMapId, finalX, finalY);
            eventPublisher.publishPositionEvent(character.getId(), finalX, finalY, newMapId);

            // 7.添加到新地图缓存信息
            addPlayerToMap(character, newMapId, finalX, finalY);
            log.debug("添加到新地图: characterId={}, newMapId={}, pos=({},{})", character.getId(), newMapId, finalX, finalY);

            // 8. 构建移动结果信息（仅非复活情况）
            StringBuilder result = new StringBuilder();
            if (!isRevive) {
                MapsConfig.MapDetail targetMap = getMapDetail(newMapId);
                if (targetMap != null) {
                    result.append("🚶 你来到了").append(targetMap.getName()).append("\n\n");
                    result.append("📍 ").append(targetMap.getDescription()).append("\n\n");

                    // 自动进行环境扫描
                    String scanResult = scanEnvironment(character);
                    result.append(scanResult);

                    return result.toString();
                }
            }else{
                result.append("🚶 20年后你又是一条好汉，现在你回到了武林主城，请继续你的江湖");
                return result.toString();
            }
            return null; // 复活或缓存更新不返回结果信息

        } catch (Exception e) {
            log.error("角色地图变更失败: characterId={}, oldMapId={}, newMapId={}", character.getId(), oldMapId, newMapId, e);
            return "移动失败：" + e.getMessage();
        }
    }


    /**
     * 添加玩家到地图缓存
     */
    public void addPlayerToMap(UserCharacter character, String mapId, int x, int y) {
        // 添加到地图对象缓存
        int tempId = getNextTempId(character.getAreaId(), mapId);
        mapObjectsCache.computeIfAbsent(mapId, k -> new ConcurrentHashMap<>())
                      .computeIfAbsent(character.getAreaId(), k -> new ConcurrentHashMap<>())
                      .put(tempId, new TempObject(character.getId().toString(), character.getName(), TempObject.TYPE_PLAYER, mapId, x, y, tempId));

        log.debug("添加玩家到地图缓存: name={},playerId={}, areaId={}, mapId={}, tempId={},  pos=({},{})", character.getName(), character.getId(), character.getAreaId(), mapId, tempId, x, y);
    }

    /**
     * 从所有地图缓存中移除指定玩家
     */
    private void removePlayerFromAllMaps(Long playerId) {
        String playerIdStr = playerId.toString();

        for (Map<Long, Map<Integer, TempObject>> mapCache : mapObjectsCache.values()) {
            for (Map<Integer, TempObject> areaCache : mapCache.values()) {
                // 找到并移除该玩家的所有条目
                areaCache.entrySet().removeIf(entry -> {
                    TempObject obj = entry.getValue();
                    return obj.getType() == TempObject.TYPE_PLAYER &&
                           playerIdStr.equals(obj.getObjectId());
                });
            }
        }

        log.debug("清理玩家缓存: playerId={}", playerId);
    }

    /**
     * 获取地图中的所有玩家
     */
    public List<TempObject> getPlayersInMap(long areaId, String mapId) {
        Map<Long, Map<Integer,TempObject>> mapCache = mapObjectsCache.get(mapId);
        List<TempObject> list = new ArrayList<>();
        if (mapCache == null) {
            return list;
        }
        Map<Integer,TempObject> areaMap = mapCache.get(areaId);
        if (areaMap == null) {
            return list;
        }

        for (TempObject tempObject : areaMap.values()) {
            if(tempObject.getType() == TempObject.TYPE_PLAYER) {
                list.add(tempObject);
            }
        }
        return list;
    }

    // ==================== 地图怪物缓存管理 ====================

    /**
     * 获取地图中的所有怪物实例
     */
    public List<MonsterInstance> getMonstersInMap(Long areaId, String mapId) {
        Map<Long, List<MonsterInstance>> mapCache = mapMonstersCache.get(mapId);
        if (mapCache != null) {
            List<MonsterInstance> monstersInMap = mapCache.get(areaId);
            return monstersInMap==null?new ArrayList<>():monstersInMap;
        }
        return new ArrayList<>();
    }

    /**
     * 添加怪物实例到地图缓存
     */
    public void addMonsterToMap(Long areaId, String mapId, MonsterInstance monsterInstance) {
        //判断mapMonstersCache对应的mapId和areaId是否存在，不存在则创建，然后放入monsterInstance
        mapMonstersCache.computeIfAbsent(mapId, k -> new ConcurrentHashMap<>())
                        .computeIfAbsent(areaId, k -> new ArrayList<>()).add(monsterInstance);

        //增加地图临时编号对象缓存
        TempObject tempObject = new TempObject();
        tempObject.setName(monsterInstance.getMonsterName());
        tempObject.setObjectId(monsterInstance.getInstanceId().toString());
        tempObject.setType(TempObject.TYPE_MONSTER);
        tempObject.setMapId(mapId);
        tempObject.setX(monsterInstance.getX());
        tempObject.setY(monsterInstance.getY());
        tempObject.setTempId(monsterInstance.getTempId());
        mapObjectsCache.computeIfAbsent(mapId, k -> new ConcurrentHashMap<>())
                      .computeIfAbsent(areaId, k -> new ConcurrentHashMap<>())
                      .put(monsterInstance.getTempId(), tempObject);

        log.debug("添加怪物实例到地图缓存: instanceId={}, areaId={}, mapId={}, tempId={}, pos=({},{})",
                 monsterInstance.getInstanceId(), areaId, mapId, monsterInstance.getTempId(),
                 monsterInstance.getX(), monsterInstance.getY());
    }

    /**
     * 从地图缓存中移除怪物实例
     */
    public void removeMonsterFromMap(Long areaId, String mapId, int tempId) {
        //1.移除对象实例
        Map<Long, List<MonsterInstance>> mapCache = mapMonstersCache.get(mapId);
        if (mapCache != null) {
            List<MonsterInstance> monstersInArea = mapCache.get(areaId);
            if (monstersInArea != null) {
                monstersInArea.removeIf(monster -> monster.getTempId() == tempId);
            }
        }
        //2.清理地图编号缓存
        Map<Long, Map<Integer, TempObject>> objectCache = mapObjectsCache.get(mapId);
        if (objectCache != null) {
            Map<Integer, TempObject> areaCache = objectCache.get(areaId);
            if (areaCache != null) {
                areaCache.remove(tempId);
            }
        }
        log.debug("从地图缓存移除怪物实例: areaId={}, mapId={}, tempId={}", areaId, mapId, tempId);
    }

    /**
     * 更新怪物实例血量
     */
//    public void updateMonsterHp(Long areaId, String mapId, int tempId, int newHp) {
//        Map<Long, List<MonsterInstance>> mapCache = mapMonstersCache.get(mapId);
//
//        if (mapCache != null) {
//            List<MonsterInstance> areaCache = mapCache.get(areaId);
//            Iterator<MonsterInstance> iterator = areaCache.iterator();
//            while (iterator.hasNext()) {
//                MonsterInstance monster = iterator.next();
//                if (monster.getTempId() == tempId) {
//                    monster.setCurrentHp(Math.max(0, newHp));
//                    monster.setLastUpdateTime(System.currentTimeMillis());
//                    if (newHp <= 0) {
//                        monster.setAlive(false);
//                        iterator.remove();
//                        log.debug("怪物实例死亡并移除: tempId={}, name={}", tempId, monster.getMonsterName());
//                    }
//                    break;
//                }
//            }
//        }
//    }

    /**
     * 获取怪物实例
     */
    public MonsterInstance getMonsterInstance(long areaId, String mapId, int tempId) {
         Map<Long, List<MonsterInstance>> mapCache = mapMonstersCache.get(mapId);
        if (mapCache != null) {
            List<MonsterInstance> monstersInMap = mapCache.get(areaId);
            if (monstersInMap != null) {
                return monstersInMap.stream().filter(monster -> monster.getTempId() == tempId).findFirst().orElse(null);
            }
        }
        return null;
    }

    /**
     * 生成新的怪物实例ID
     */
    public Long generateMonsterInstanceId() {
        return monsterInstanceIdGenerator.getAndIncrement();
    }

    /**
     * 获取怪物模板信息
     */
    private Monster getMonsterTemplate(String monsterId) {
        try {
            return monsterMapper.selectByMonsterNo(monsterId);
        } catch (Exception e) {
            log.warn("获取怪物模板失败: monsterId={}", monsterId, e);
            return null;
        }
    }


    // ==================== 内部类 ====================

    /**
     * 怪物实例信息
     */
    @Setter
    @Getter
    public static class MonsterInstance {
        private Long instanceId;        // 实例ID（全局唯一）
        private String monsterId;       // 怪物模板ID
        private String monsterName;     // 怪物名称
        private Long areaId;          // 分区ID
        private String mapId;           // 所在地图
        private int tempId;             // 地图临时编号（用于用户快捷命令）
        private int x;                  // X坐标
        private int y;                  // Y坐标
        private int currentHp;          // 当前血量
        private int maxHp;              // 最大血量
        private boolean isAlive;        // 是否存活
        private long spawnTime;         // 刷新时间
        private long lastUpdateTime;    // 最后更新时间

        public MonsterInstance(Long instanceId, String monsterId, String monsterName,
                              long areaId, String mapId, int tempId, int x, int y, int maxHp) {
            this.instanceId = instanceId;
            this.monsterId = monsterId;
            this.monsterName = monsterName;
            this.areaId = areaId;
            this.mapId = mapId;
            this.tempId = tempId;
            this.x = x;
            this.y = y;
            this.maxHp = maxHp;
            this.currentHp = maxHp;
            this.isAlive = true;
            this.spawnTime = System.currentTimeMillis();
            this.lastUpdateTime = System.currentTimeMillis();
        }
    }

    /**
     * 玩家地图信息
     */
    @Setter
    @Getter
    public static class PlayerMapInfo {
        // Getters and Setters
        private Long playerId;
        private String playerName;
        private Long areaId;          // 分区ID
        private String mapId;       // 所在地图
        private int tempId;         // 地图临时编号
        private int x;
        private int y;
        private boolean isAlive;
        private long lastUpdateTime;

        public PlayerMapInfo(Long playerId, String playerName, Long areaId, String mapId, int tempId, int x, int y, boolean isAlive) {
            this.playerId = playerId;
            this.playerName = playerName;
            this.areaId = areaId;
            this.mapId = mapId;
            this.tempId = tempId;
            this.x = x;
            this.y = y;
            this.isAlive = isAlive;
            this.lastUpdateTime = System.currentTimeMillis();
        }

    }

    // ==================== 临时对象内部类 ====================
    /**
     * 临时对象类
     */
    @Setter
    @Getter
    public static class TempObject {
        /*
         * 玩家类型
         */
        public final static int TYPE_PLAYER = 1;
        /*
         * 怪物类型
         */
        public final static int TYPE_MONSTER = 2;
        /*
         *  NPC
         */
        public final static int TYPE_NPC = 3;
        /*
            采集点
         */
        public final static int TYPE_COLLECT = 4;
        
        // Getters and Setters
        private String objectId;
        private String name;
        private int type;
        private String mapId;
        private Integer x;
        private Integer y;
        private double distance;
        private String status;
        private int tempId;
        private long createTime;
        public TempObject(){}
        public TempObject(String objectId, String name, int type, String mapId, int x, int y, int tempId) {
            this.objectId = objectId;
            this.name = name;
            this.type = type;
            this.mapId = mapId;
            this.x = x;
            this.y = y;
            this.tempId = tempId;
            this.createTime = System.currentTimeMillis();
        }

    }
}
