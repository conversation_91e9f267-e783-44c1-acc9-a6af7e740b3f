package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 排行榜实体
 *
 * <AUTHOR>
 */
@Data
@TableName("rank")
@EqualsAndHashCode(callSuper = true)
public class Rank extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 排行类型(1:等级 2:战力 3:财富)
     */
    private Integer type;

    /**
     * 角色ID
     */
    private Long characterId;

    /**
     * 角色名
     */
    private String characterName;

    /**
     * 区服ID
     */
    private Integer areaId;

    /**
     * 数值
     */
    private Long amount;

    /**
     * 排名
     */
    private Integer rank;
} 