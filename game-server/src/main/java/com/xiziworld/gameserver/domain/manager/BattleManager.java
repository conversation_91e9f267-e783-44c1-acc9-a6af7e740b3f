package com.xiziworld.gameserver.domain.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.GameAttributeConstant;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.entity.Monster;
import com.xiziworld.gameserver.domain.entity.Skill;

import com.xiziworld.gameserver.domain.manager.config.*;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import com.xiziworld.gameserver.domain.mapper.MonsterMapper;
import com.xiziworld.gameserver.domain.mapper.SkillMapper;
import com.xiziworld.gameserver.domain.manager.player.PlayerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 战斗管理器
 * 负责战斗系统、PVE/PVP、技能系统、伤害计算等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class BattleManager {


    
    @Autowired
    private MonsterMapper monsterMapper;
    
    @Autowired
    private SkillMapper skillMapper;
    
    @Autowired
    private ConfigManager configManager;
    
    @Autowired
    private PlayerManager playerManager;
    
    @Autowired
    private MapManager mapManager;

    @Autowired
    private AssetManager assetManager;

    // 战斗状态缓存 - 角色ID -> 战斗状态
    private final Map<Long, BattleState> battleStates = new ConcurrentHashMap<>();
    
    // 技能冷却缓存 - 角色ID -> 技能编号 -> 冷却结束时间
    private final Map<Long, Map<String, Long>> skillCooldowns = new ConcurrentHashMap<>();

    // 攻击距离常量
    public static final int ATTACK_DISTANCE_SWORDSMAN = 5;  // 剑客5米
    public static final int ATTACK_DISTANCE_MAGE = 15;      // 仙师15米
    public static final int ATTACK_DISTANCE_MONK = 8;       // 圣僧8米
    
    // 攻击冷却时间常量
    public static final int NORMAL_ATTACK_COOLDOWN = 3000;  // 普通攻击3秒

    @Autowired
    private ItemMapper itemMapper;

    // ==================== 战斗系统 ====================

    /**
     *  攻击目标
     * @param characterId
     * @param tempId 地图临时编号
     * @return
     */
    @Transactional
    public String attackByTempId(Long characterId, int tempId) {
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        // 检查角色是否死亡
        if (playerManager.isCharacterDead(characterId)) {
            throw new GameException(GameException.CHARACTER_ALREADY_DEAD);
        }
        // 检查攻击冷却
        if (isInAttackCooldown(characterId)) {
            return GameException.ATTACK_COOLDOWN_MESSAGE;
        }
        // 获取目标信息
        MapManager.TempObject tempObject = mapManager.getTempObject(characterId, tempId);
        if (tempObject == null) {
            return "🎯 大侠，目标不存在/已死亡\n💡 发送'查看地图'目标列表";
        }
        // 检查攻击距离
        String distanceCheck = checkAttackDistance(character, tempObject);
        if (distanceCheck != null) {
            return distanceCheck;
        }
        //攻击怪物
        if(tempObject.getType()==MapManager.TempObject.TYPE_MONSTER){
            return attackMonster(character, tempObject);
        //攻击玩家
        }else{
            return attackPlayer(character, tempObject);
        }
    }

    /**
     * 攻击怪物
     * 
     * @param character 角色
     * @param tempObject 怪物临时缓存信息
     * @return 战斗结果信息
     */
    private String attackMonster(UserCharacter character, MapManager.TempObject tempObject) {
        log.info("攻击怪物: characterName={}, tempName={}", character.getName(), tempObject.getName());

        // 获取怪物实例信息
        MapManager.MonsterInstance monsterInstance = mapManager.getMonsterInstance(character.getAreaId(),tempObject.getMapId(),tempObject.getTempId());

        if(monsterInstance==null){
            return "🎯 大侠手速慢了，此怪已经被消灭了\n💡 发送'查看地图'目标列表";
        }
        
        Monster monster = getMonster(monsterInstance.getMonsterId(), tempObject.getName());
        
        // 计算伤害
        int damage = calculateDamageToMonster(character, monster);
        
        // 应用伤害
        int remainingHp = Math.max(0, monsterInstance.getCurrentHp()- damage);
        monsterInstance.setCurrentHp(remainingHp);
        
        // 设置攻击冷却
        setAttackCooldown(character.getId());
        
        // 设置战斗状态
        setBattleState(character.getId(), BattleType.PVE, tempObject.getObjectId());
        
        StringBuilder result = new StringBuilder();
        result.append("⚔️ 你对").append(monster.getName()).append("造成了").append(damage).append("点伤害\n");
        
        if (remainingHp <= 0) {
            // 怪物死亡
            result.append("💀 ").append(monster.getName()).append("被击败了！\n\n");

            //清理地图怪物缓存
            mapManager.removeMonsterFromMap(character.getAreaId(), Helper.getCurrentMapId(character), tempObject.getTempId());
            
            // 获得经验和奖励
            String rewards = processMonsterKillRewards(character, monster);
            result.append(rewards);
            
            // 清除战斗状态
            clearBattleState(character.getId());
        } else {
            //更新地图缓存怪物血量, 前面 monsterInstance.setCurrentHp(remainingHp);是不是就存下来实例的血量了？
            //mapManager.updateMonsterHp(character.getAreaId(),Helper.getCurrentMapId(character), tempObject.getTempId(), remainingHp);

            result.append("🩸 ").append(monster.getName()).append("剩余血量：").append(remainingHp).append("/").append(monster.getHp());

            // 怪物反击
            String counterAttack = processMonsterCounterAttack(character, monster);
            result.append("\n").append(counterAttack);
        }
        
        return result.toString();
    }

    /**
     * 攻击玩家
     * 
     * @param attacker 攻击者
     * @param tempObject 目标玩家临时编号
     * @return 战斗结果信息
     */
    private String attackPlayer(UserCharacter attacker, MapManager.TempObject tempObject) {
        log.info("攻击玩家: attacker={}, tempName={}", attacker.getName(), tempObject.getName());

        // 检查是否在安全区
        if (mapManager.isInSafeZone(attacker.getId())) {
            return GameException.SAFE_ZONE_PVP_RESTRICTION;
        }
        
        // 根据tempObject.getObjectId()获取目标玩家
        Long targetCharacterId=null;
        try {
            targetCharacterId = Long.parseLong(tempObject.getObjectId());
        } catch (NumberFormatException e) {
            return "🎯 大侠，请指定正确的目标玩家ID格式";
        }
        UserCharacter targetCharacter = playerManager.getCharacterById(targetCharacterId);
        if (targetCharacter == null) {
            return "🎯 大侠，你打啥呢？\n💡 发送'查看地图'目标列表";
        }

        // 检查目标玩家是否死亡
        if (playerManager.isCharacterDead(targetCharacterId)) {
            return "🎯 大侠慢人一步，"+targetCharacter.getName()+"已被诛灭！\n💡 发送'查看地图'目标列表";
        }

        // 检查是否可以攻击该玩家（例如：同一区域、非安全区等）
        String pvpCheck = checkPvpConditions(attacker, targetCharacter);
        if (pvpCheck != null) {
            return pvpCheck;
        }

        // 计算对玩家的伤害
        int damage = calculateDamageToPlayer(attacker, targetCharacter);

        // 应用伤害
        int currentHp = targetCharacter.getHp();
        int remainingHp = Math.max(0, currentHp - damage);

        // 更新目标玩家血量
        playerManager.updateCharacterHpMp(targetCharacter, remainingHp, targetCharacter.getMp());

        // 设置攻击冷却
        setAttackCooldown(attacker.getId());

        // 构建战斗结果
        StringBuilder result = new StringBuilder();
        result.append(String.format("⚔️ 你对 %s 造成了 %d 点伤害！", targetCharacter.getName(), damage));

        if (remainingHp <= 0) {
            result.append(String.format("\n💀 %s 败在你的拳脚下！", targetCharacter.getName()));
            //TODO 这里可以添加PVP奖励逻辑
            //TODO 这里可以添加PVP记录逻辑
        } else {
            result.append(String.format("\n❤️ %s 剩余血量：%d", targetCharacter.getName(), remainingHp));
        }

        return result.toString();
    }

    /**
     * 使用技能攻击目标
     * @param characterId 玩家ID
     * @param skillNo 技能编号
     * @param tempId 地图临时编号
     * @return
     */
    @Transactional
    public String useSkillAttack(Long characterId, String skillNo, int tempId){
        log.info("使用技能攻击: characterId={}, skillNo={}, tempId={}", characterId, skillNo, tempId);
        // 检查角色是否存在
        UserCharacter character = playerManager.getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        // 检查角色是否死亡
        if (playerManager.isCharacterDead(characterId)) {
            throw new GameException(GameException.CHARACTER_ALREADY_DEAD);
        }
        // 检查技能是否存在
        Skill skill = skillMapper.selectBySkillNo(skillNo);
        if (skill == null) {
            throw new GameException(GameException.SKILL_NOT_EXISTS);
        }
        // 检查是否学会该技能
        if (!playerManager.hasLearnedSkill(character, skillNo)) {
            return "❌ 你尚未习得此技能";
        }
        // 检查技能冷却
        if (isSkillInCooldown(characterId, skillNo)) {
            long remainingTime = getSkillCooldownRemaining(characterId, skillNo);
            return "⏰ 技能正在冷却中，还需" + (remainingTime / 1000) + "秒";
        }
        // 检查法力值是否足够
        if (character.getMp() < skill.getCost()) {
            return "💙 内力不足，无法施展技能";
        }
        // 获取目标怪物
        MapManager.TempObject tempObject = mapManager.getTempObject(characterId, tempId);
        if (tempObject == null) {
            return "🎯 目标已消失或不存在";
        }
        if(tempObject.getType() == MapManager.TempObject.TYPE_MONSTER){
            return useSkillOnMonster(character, skill, tempObject);
        }else{
            return useSkillOnPlayer(character, skill, tempObject);
        }

    }
    /**
     * 使用技能攻击怪物
     *
     * @param character 角色
     * @param skill 技能
     * @param tempObject 地图临时编号目标
     * @return 战斗结果信息
     */
    private String useSkillOnMonster(UserCharacter character, Skill skill, MapManager.TempObject tempObject) {
        log.info("使用技能攻击怪物: character={}, skill={}, temp={}", character.getName(), skill.getName(), tempObject.getName());

        // 检查攻击距离
        String distanceCheck = checkAttackDistance(character, tempObject);
        if (distanceCheck != null) {
            return distanceCheck;
        }

        // 获取怪物实例信息
        MapManager.MonsterInstance monsterInstance = mapManager.getMonsterInstance(character.getAreaId(),tempObject.getMapId(),tempObject.getTempId());
        if(monsterInstance==null){
            return "🎯 大侠手速慢了，此怪已经被消灭了";
        }
        Monster monster = getMonster(monsterInstance.getMonsterId(), tempObject.getName());

        // 计算技能伤害
        int damage = calculateSkillDamageToMonster(character, monster, skill);

        // 消耗法力值
        playerManager.updateCharacterHpMp(character, character.getHp(), character.getMp() - skill.getCost());

        // 应用伤害
        int remainingHp = Math.max(0, monsterInstance.getCurrentHp() - damage);
        monsterInstance.setCurrentHp(remainingHp);

        // 设置技能冷却
        setSkillCooldown(character.getId(), skill.getSkillNo(), skill.getCooldown() * 1000L);

        // 设置战斗状态
        setBattleState(character.getId(), BattleType.PVE, tempObject.getObjectId());

        StringBuilder result = new StringBuilder();
        result.append("✨ 你使用").append(skill.getName()).append("对").append(monster.getName()).append("造成了").append(damage).append("点伤害\n");

        if (remainingHp <= 0) {
            // 怪物死亡
            result.append("💀 ").append(monster.getName()).append("被击败了！\n\n");
            //清理地图怪物缓存
            mapManager.removeMonsterFromMap(character.getAreaId(), Helper.getCurrentMapId(character), tempObject.getTempId());

            // 获得经验和奖励
            String rewards = processMonsterKillRewards(character, monster);
            result.append(rewards);
            //TODO 统计玩家击杀怪物数

            // 清除战斗状态
            clearBattleState(character.getId());
        } else {
            //更新地图缓存怪物血量，monsterInstance.setCurrentHp(remainingHp)已经保存了
            //mapManager.updateMonsterHp(character.getAreaId(), Helper.getCurrentMapId(character), tempId, remainingHp);

            result.append("🩸 ").append(monster.getName()).append("剩余血量：").append(remainingHp).append("/").append(monster.getHp());

            // 怪物反击
            String counterAttack = processMonsterCounterAttack(character, monster);
            result.append("\n").append(counterAttack);

        }

        return result.toString();
    }

    /**
     * 使用技能攻击玩家
     * @param attacker 角色
     * @param skill 技能
     * @param tempObject 地图临时编号目标
     * @return 战斗结果信息
     */
    private String useSkillOnPlayer(UserCharacter attacker, Skill skill, MapManager.TempObject tempObject) {
        log.info("使用技能攻击玩家: character={}, skill={}, temp={}", attacker.getName(), skill.getName(), tempObject.getName());

        // 根据tempObject.getObjectId()获取目标玩家
        Long targetCharacterId;
        try {
            targetCharacterId = Long.parseLong(tempObject.getObjectId());
        } catch (NumberFormatException e) {
            return "🎯 大侠，请指定正确的目标玩家ID格式";
        }

        UserCharacter targetCharacter = playerManager.getCharacterById(targetCharacterId);
        if (targetCharacter == null) {
            return "🎯 大侠，对方不存在或已死亡!\n💡 发送'查看地图'目标列表";
        }

        // 检查目标玩家是否死亡
        if (playerManager.isCharacterDead(targetCharacterId)) {
            return "🎯 大侠慢人一步，"+targetCharacter.getName()+"已被诛灭！";
        }

        // 检查是否可以攻击该玩家（PVP条件）
        String pvpCheck = checkPvpConditions(attacker, targetCharacter);
        if (pvpCheck != null) {
            return pvpCheck;
        }

        // 消耗法力值
        playerManager.updateCharacterHpMp(attacker, attacker.getHp(), attacker.getMp() - skill.getCost());

        // 计算技能伤害
        int damage = calculateSkillDamageToPlayer(attacker, targetCharacter, skill);

        // 应用伤害
        int currentHp = targetCharacter.getHp();
        int remainingHp = Math.max(0, currentHp - damage);

        // 更新目标玩家血量
        playerManager.updateCharacterHpMp(targetCharacter, remainingHp, targetCharacter.getMp());

        // 设置技能冷却
        setSkillCooldown(attacker.getId(), skill.getSkillNo(), skill.getCooldown());

        // 构建战斗结果
        StringBuilder result = new StringBuilder();
        result.append(String.format("✨ 你对 %s 施展了 %s，造成了 %d 点伤害！", targetCharacter.getName(), skill.getName(), damage));

        if (remainingHp <= 0) {
            result.append(String.format("\n💀 %s 败在你的绝技之下！", targetCharacter.getName()));
            //TODO 这里可以添加PVP击杀奖励逻辑
            //TODO 这里可以添加PVP记录逻辑
        } else {
            result.append(String.format("\n❤️ %s 剩余血量：%d", targetCharacter.getName(), remainingHp));
        }

        return result.toString();
    }

    // ==================== PVP相关方法 ====================

    /**
     * 检查PVP条件
     */
    private String checkPvpConditions(UserCharacter attacker, UserCharacter target) {
        // 检查等级限制（例如：30级以上才能PVP）
        if (attacker.getLevel() < LevelsConfig.PVP_LEVEL) {
            return String.format("⚔️ 大侠功力尚浅，需达到%d级方可与人切磋", LevelsConfig.PVP_LEVEL);
        }

        if (target.getLevel() < LevelsConfig.PVP_LEVEL) {
            return String.format("⚔️ 对方功力尚浅，不足%d级，不便出手", LevelsConfig.PVP_LEVEL);
        }

        // 检查是否在安全区（武林主城不能PVP）
        if (MapsConfig.MAP_WULIN.equals(Helper.getCurrentMapId(attacker))) {
            return "🏛️ 武林主城乃和谐之地，不可动武";
        }

        // 检查等级差距（防止高等级欺负低等级）
        int levelDiff = Math.abs(attacker.getLevel() - target.getLevel());
        if (levelDiff > 100) {
            return "⚖️ 双方实力悬殊过大，江湖道义不允许此战";
        }

        // 检查是否为同一玩家（防止自己攻击自己）
        if (attacker.getId().equals(target.getId())) {
            return "🤔 大侠莫要自残，江湖路还很长";
        }

        return null; // 通过所有检查
    }

    /**
     * 计算对玩家的伤害
     */
    private int calculateDamageToPlayer(UserCharacter attacker, UserCharacter target) {
        // 获取攻击者属性
        JSONObject attackerAttributes = JSON.parseObject(attacker.getAttributes());
        int attackerPhyAtk = attackerAttributes.getIntValue(GameAttributeConstant.PHY_ATK);
        int attackerMagAtk = attackerAttributes.getIntValue(GameAttributeConstant.MAG_ATK);
        int attackerBudAtk = attackerAttributes.getIntValue(GameAttributeConstant.BUD_ATK);

        // 获取目标属性
        JSONObject targetAttributes = JSON.parseObject(target.getAttributes());
        int targetPhyDef = targetAttributes.getIntValue(GameAttributeConstant.PHY_DEF);
        int targetMagDef = targetAttributes.getIntValue(GameAttributeConstant.MAG_DEF);
        int targetBudDef = targetAttributes.getIntValue(GameAttributeConstant.BUD_DEF);

        // 根据攻击者职业类型确定伤害占比
        double phyRatio, magRatio, budRatio;
        String primaryAttackType;

        switch (attacker.getType()) {
            case 1: // 剑客 - 物理攻击70%，其他各15%
                phyRatio = 0.70;
                magRatio = 0.15;
                budRatio = 0.15;
                primaryAttackType = "物理";
                break;
            case 2: // 仙师 - 法术攻击70%，其他各15%
                phyRatio = 0.15;
                magRatio = 0.70;
                budRatio = 0.15;
                primaryAttackType = "法术";
                break;
            case 3: // 圣僧 - 佛法攻击70%，其他各15%
                phyRatio = 0.15;
                magRatio = 0.15;
                budRatio = 0.70;
                primaryAttackType = "佛法";
                break;
            default: // 默认剑客类型
                phyRatio = 0.70;
                magRatio = 0.15;
                budRatio = 0.15;
                primaryAttackType = "物理";
                break;
        }

        // 计算混合伤害（每种攻击类型分别计算，然后按比例合并）
        int phyDamage = calculatePlayerDamage(attackerPhyAtk, targetPhyDef, attacker.getLevel());
        int magDamage = calculatePlayerDamage(attackerMagAtk, targetMagDef, attacker.getLevel());
        int budDamage = calculatePlayerDamage(attackerBudAtk, targetBudDef, attacker.getLevel());

        // 按比例合并伤害
        int baseDamage = (int) (phyDamage * phyRatio + magDamage * magRatio + budDamage * budRatio);

        // 添加随机波动（±20%）
        Random random = new Random();
        double variation = 0.8 + (random.nextDouble() * 0.4); // 0.8 到 1.2
        int finalDamage = (int) (baseDamage * variation);

        // 确保最小伤害为1
        finalDamage = Math.max(1, finalDamage);

        // 添加调试日志
        log.debug("PVP普通攻击: {}({}) -> {}({}), 主要攻击类型={}, 混合伤害=物理{}×{}%+法术{}×{}%+佛法{}×{}%, 最终伤害={}",
                 attacker.getName(), attacker.getLevel(), target.getName(), target.getLevel(),
                 primaryAttackType, phyDamage, (int)(phyRatio*100), magDamage, (int)(magRatio*100),
                 budDamage, (int)(budRatio*100), finalDamage);

        return finalDamage;
    }

    /**
     * 计算技能对玩家的伤害（PVP技能伤害）
     */
    private int calculateSkillDamageToPlayer(UserCharacter attacker, UserCharacter target, Skill skill) {
        // 获取攻击者属性
        JSONObject attackerAttributes = JSON.parseObject(attacker.getAttributes());
        int attackerPhyAtk = attackerAttributes.getIntValue(GameAttributeConstant.PHY_ATK);
        int attackerMagAtk = attackerAttributes.getIntValue(GameAttributeConstant.MAG_ATK);
        int attackerBudAtk = attackerAttributes.getIntValue(GameAttributeConstant.BUD_ATK);

        // 获取目标属性
        JSONObject targetAttributes = JSON.parseObject(target.getAttributes());
        int targetPhyDef = targetAttributes.getIntValue(GameAttributeConstant.PHY_DEF);
        int targetMagDef = targetAttributes.getIntValue(GameAttributeConstant.MAG_DEF);
        int targetBudDef = targetAttributes.getIntValue(GameAttributeConstant.BUD_DEF);

        // 技能攻击使用混合伤害，但根据技能类型调整比例
        double phyRatio, magRatio, budRatio;
        String skillAttackType;

        // 根据技能类型确定伤害占比
        switch (skill.getType()) {
            case 1: // 魔法技能 - 法术攻击为主
                phyRatio = 0.10;
                magRatio = 0.80;  // 技能攻击主要类型占比更高
                budRatio = 0.10;
                skillAttackType = "法术";
                break;
            case 2: // 佛法技能 - 佛法攻击为主
                phyRatio = 0.10;
                magRatio = 0.10;
                budRatio = 0.80;
                skillAttackType = "佛法";
                break;
            default: // 物理技能 - 物理攻击为主
                phyRatio = 0.80;
                magRatio = 0.10;
                budRatio = 0.10;
                skillAttackType = "物理";
                break;
        }

        // 计算混合伤害
        int phyDamage = calculatePlayerDamage(attackerPhyAtk, targetPhyDef, attacker.getLevel());
        int magDamage = calculatePlayerDamage(attackerMagAtk, targetMagDef, attacker.getLevel());
        int budDamage = calculatePlayerDamage(attackerBudAtk, targetBudDef, attacker.getLevel());

        // 按比例合并伤害
        int baseDamage = (int) (phyDamage * phyRatio + magDamage * magRatio + budDamage * budRatio);

        // 应用技能威力倍率（基于skill.power字段）
        double skillMultiplier = skill.getPower() != null ? skill.getPower() / 100.0 : 1.0;
        int skillDamage = (int) (baseDamage * skillMultiplier);

        // PVP伤害调整系数（防止PVP伤害过高）
        double pvpDamageReduction = 0.8; // PVP伤害减少20%
        int pvpDamage = (int) (skillDamage * pvpDamageReduction);

        // 添加随机波动（±15%，比普通攻击波动小一些）
        Random random = new Random();
        double variation = 0.85 + (random.nextDouble() * 0.3); // 0.85 到 1.15
        int finalDamage = (int) (pvpDamage * variation);

        // 确保最小伤害为1
        finalDamage = Math.max(1, finalDamage);

        // 添加调试日志
        log.debug("PVP技能攻击: {}({}) -> {}({}), 技能类型={}, 混合伤害=物理{}×{}%+法术{}×{}%+佛法{}×{}%, 技能倍率={}%, 最终伤害={}",
                 attacker.getName(), attacker.getLevel(), target.getName(), target.getLevel(),
                 skillAttackType, phyDamage, (int)(phyRatio*100), magDamage, (int)(magRatio*100),
                 budDamage, (int)(budRatio*100), (int)(skillMultiplier*100), finalDamage);

        return finalDamage;
    }

    // ==================== 伤害计算系统 ====================

    /**
     * 计算对怪物的伤害
     */
    private int calculateDamageToMonster(UserCharacter attacker, Monster monster) {
        // 获取攻击者属性
        JSONObject attackerAttributes = JSON.parseObject(attacker.getAttributes());
        
        // 获取基础伤害
        BattleConfig.ExpCalculation expConfig = configManager.getBattleConfig().getExpCalculation();
        int baseDamage = expConfig.getBaseDamage();
        
        // 根据职业选择对应的攻击属性
        int attackValue = getAttackValueByRole(attacker.getType(), attackerAttributes);
        int defenseValue = getDefenseValueByRole(attacker.getType(), monster);
        
        // 计算基础伤害
        double damage = baseDamage + attackValue * expConfig.getAttackFactor() - defenseValue * expConfig.getDefenseFactor();
        
        // 等级差影响
        int levelDiff = attacker.getLevel() - monster.getLevel();
        damage += levelDiff * expConfig.getLevelFactor();
        
        // 职业克制影响
        String attackerRole = getRoleConstant(attacker.getType());
        String monsterRole = getMonsterRoleConstant(monster);
        double restraintMultiplier = configManager.calculateRestraintDamage(attackerRole, monsterRole);
        damage *= restraintMultiplier;
        
        // 伤害波动
        double variance = expConfig.getDamageVariance();
        double randomFactor = 1.0 + (Math.random() - 0.5) * 2 * variance;
        damage *= randomFactor;
        
        // 暴击判断
        if (Math.random() < expConfig.getCriticalRate()) {
            damage *= expConfig.getCriticalDamage();
        }
        
        return Math.max(1, (int) damage);
    }

    /**
     * 计算技能对怪物的伤害
     */
    private int calculateSkillDamageToMonster(UserCharacter attacker, Monster monster, Skill skill) {
        // 基础技能伤害
        int baseDamage = calculateDamageToMonster(attacker, monster);
        
        // 技能威力加成
        double skillMultiplier = skill.getPower() / 100.0;
        
        return (int) (baseDamage * skillMultiplier);
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查攻击距离
     */
    private String checkAttackDistance(UserCharacter attacker, MapManager.TempObject target) {
        int maxDistance = getAttackDistanceByRole(attacker.getType());
        
        if (target.getDistance() > maxDistance) {
            return "🏃 距离过远，无法出手。请先靠近" + target.getName();
        }
        
        return null;
    }

    /**
     * 根据职业获取攻击距离
     */
    private int getAttackDistanceByRole(Integer roleType) {
        switch (roleType) {
            case 1: return ATTACK_DISTANCE_SWORDSMAN; // 剑客
            case 2: return ATTACK_DISTANCE_MAGE;      // 仙师
            case 3: return ATTACK_DISTANCE_MONK;      // 圣僧
            default: return ATTACK_DISTANCE_SWORDSMAN;
        }
    }

    /**
     * 根据职业获取攻击属性值
     */
    private int getAttackValueByRole(Integer roleType, JSONObject attributes) {
        switch (roleType) {
            case 1: return attributes.getIntValue(GameAttributeConstant.PHY_ATK); // 剑客-物攻
            case 2: return attributes.getIntValue(GameAttributeConstant.MAG_ATK); // 仙师-法攻
            case 3: return attributes.getIntValue(GameAttributeConstant.BUD_ATK); // 圣僧-佛攻
            default: return attributes.getIntValue(GameAttributeConstant.PHY_ATK);
        }
    }

    /**
     * 根据职业获取怪物对应防御值
     */
    private int getDefenseValueByRole(Integer roleType, Monster monster) {
        switch (roleType) {
            case 1: return monster.getDefenseW() != null ? monster.getDefenseW() : 10; // 剑客-物防
            case 2: return monster.getDefenseM() != null ? monster.getDefenseM() : 10; // 仙师-法防
            case 3: return monster.getDefenseF() != null ? monster.getDefenseF() : 10; // 圣僧-佛防
            default: return 10;
        }
    }

    /**
     * 根据职业ID获取职业常量
     */
    private String getRoleConstant(Integer roleType) {
        switch (roleType) {
            case 1: return BattleConfig.ROLE_SWORDSMAN;
            case 2: return BattleConfig.ROLE_MAGE;
            case 3: return BattleConfig.ROLE_MONK;
            default: return BattleConfig.ROLE_SWORDSMAN;
        }
    }

    /**
     * 根据怪物攻击属性动态判断职业类型
     */
    private String getMonsterRoleConstant(Monster monster) {
        // 根据怪物攻击属性动态判断职业类型

        // 获取三种攻击力
        int phyAtk =monster.getAttackW();  // 物理攻击
        int magAtk = monster.getAttackM();  // 法术攻击
        int budAtk = monster.getAttackF();  // 佛法攻击

        // 找出最高的攻击类型
        if (phyAtk >= magAtk && phyAtk >= budAtk) {
            // 物理攻击最高 -> 剑客类型
            log.debug("怪物{}物理攻击最高({}), 判定为剑客类型", monster.getName(), phyAtk);
            return BattleConfig.ROLE_SWORDSMAN;
        } else if (magAtk >= phyAtk && magAtk >= budAtk) {
            // 法术攻击最高 -> 仙师类型
            log.debug("怪物{}法术攻击最高({}), 判定为仙师类型", monster.getName(), magAtk);
            return BattleConfig.ROLE_MAGE;
        } else {
            // 佛法攻击最高 -> 圣僧类型
            log.debug("怪物{}佛法攻击最高({}), 判定为圣僧类型", monster.getName(), budAtk);
            return BattleConfig.ROLE_MONK;
        }

    }

    /**
     * 获取或创建怪物数据
     */
    private Monster getMonster(String monsterId, String monsterName) {
        // 首先尝试从数据库获取
        Monster monster = monsterMapper.selectByMonsterNo(monsterId);

        if (monster != null) {
            // 如果数据库中有，直接返回
            return monster;
        }

        // 如果数据库中没有，根据怪物ID创建默认数据
        return createDefaultMonster(monsterId, monsterName);
    }

    /**
     * 创建默认怪物数据（当数据库中找不到时的兜底方案）
     */
    private Monster createDefaultMonster(String monsterId, String monsterName) {
        // 尝试根据怪物ID从数据库获取模板
        Monster template = findMonsterTemplate(monsterId);

        if (template != null) {
            // 如果找到模板，复制属性
            Monster monster = new Monster();
            monster.setMonsterNo(monsterId);
            monster.setName(template.getName());
            monster.setType(template.getType());
            monster.setLevel(template.getLevel());
            monster.setHp(template.getHp());
            monster.setAttackW(template.getAttackW());
            monster.setAttackM(template.getAttackM());
            monster.setAttackF(template.getAttackF());
            monster.setDefenseW(template.getDefenseW());
            monster.setDefenseM(template.getDefenseM());
            monster.setDefenseF(template.getDefenseF());
            monster.setDescription(template.getDescription());
            return monster;
        }

        // 如果数据库中也没有，创建一个基础怪物
        Monster monster = new Monster();
        monster.setMonsterNo(monsterId);
        monster.setName(monsterName != null ? monsterName : "未知怪物");
        monster.setType("normal");
        monster.setLevel(5);
        monster.setHp(100);
        monster.setAttackW(20);
        monster.setAttackM(15);
        monster.setAttackF(15);
        monster.setDefenseW(15);
        monster.setDefenseM(10);
        monster.setDefenseF(12);
        monster.setDescription("一个未知的怪物");

        return monster;
    }

    /**
     * 查找怪物模板（尝试多种方式匹配）
     */
    private Monster findMonsterTemplate(String monsterId) {
        // 1. 直接按怪物编号查找
        Monster template = monsterMapper.selectByMonsterNo(monsterId);
        if (template != null) {
            return template;
        }

        // 2. 查询所有怪物进行匹配
        List<Monster> allMonsters = monsterMapper.selectList(null);

        // 按名称查找（如果传入的是中文名）
        for (Monster monster : allMonsters) {
            if (monsterId.equals(monster.getName())) {
                return monster;
            }
        }

        // 3. 模糊匹配（如果传入的ID包含在怪物编号中）
        for (Monster monster : allMonsters) {
            if (monster.getMonsterNo().toLowerCase().contains(monsterId.toLowerCase()) ||
                monster.getName().contains(monsterId)) {
                return monster;
            }
        }

        return null;
    }

    /**
     * 处理怪物击杀奖励
     */
    private String processMonsterKillRewards(UserCharacter character, Monster monster) {
        // 计算经验奖励
        int expReward = calculateExpReward(character, monster);
        
        // 给予经验
        boolean levelUp = playerManager.addExperience(character.getId(), (long) expReward);
        
        StringBuilder result = new StringBuilder();
        result.append("💰 获得经验：").append(expReward);
        
        if (levelUp) {
            result.append("\n🎉 恭喜升级！");
        }

        // 处理掉落物品
        String dropResult = processMonsterDrops(character, monster);
        if (!dropResult.isEmpty()) {
            result.append("\n").append(dropResult);
        }
        
        return result.toString();
    }

    /**
     * 计算经验奖励
     */
    private int calculateExpReward(UserCharacter character, Monster monster) {
        // 基础经验
        int baseExp = monster.getHp();
        
        // 等级差影响
        int levelDiff = monster.getLevel() - character.getLevel();
        //double levelMultiplier = Math.max(0.1, 1.0 + levelDiff * 0.1);
        double levelMultiplier = 1.0 + levelDiff * 0.01;
        
        return (int) (baseExp * levelMultiplier);
    }

    /**
     * 处理怪物反击
     */
    private String processMonsterCounterAttack(UserCharacter character, Monster monster) {
        try {
            // 获取玩家防御属性
            JSONObject playerAttributes = JSON.parseObject(character.getAttributes());
            if (playerAttributes == null) {
                log.warn("玩家{}属性为空，使用默认防御值", character.getName());
                playerAttributes = new JSONObject();
            }

            // 判断怪物主要攻击类型
            String monsterRole = getMonsterRoleConstant(monster);

            // 获取怪物的三种攻击属性
            int monsterPhyAtk = monster.getAttackW();  // 物理攻击
            int monsterMagAtk = monster.getAttackM();  // 法术攻击
            int monsterBudAtk = monster.getAttackF();  // 佛法攻击

            // 获取玩家的三种防御属性
            int playerPhyDef = playerAttributes.getIntValue(GameAttributeConstant.PHY_DEF);  // 物理防御
            int playerMagDef = playerAttributes.getIntValue(GameAttributeConstant.MAG_DEF);  // 法术防御
            int playerBudDef = playerAttributes.getIntValue(GameAttributeConstant.BUD_DEF);  // 佛法防御

            // 根据怪物职业类型确定伤害占比
            double phyRatio, magRatio, budRatio;
            String primaryAttackType;

            switch (monsterRole) {
                case BattleConfig.ROLE_SWORDSMAN:
                    // 剑客类型：物理攻击70%，其他各15%
                    phyRatio = 0.70;
                    magRatio = 0.15;
                    budRatio = 0.15;
                    primaryAttackType = "物理";
                    break;
                case BattleConfig.ROLE_MAGE:
                    // 仙师类型：法术攻击70%，其他各15%
                    phyRatio = 0.15;
                    magRatio = 0.70;
                    budRatio = 0.15;
                    primaryAttackType = "法术";
                    break;
                case BattleConfig.ROLE_MONK:
                    // 圣僧类型：佛法攻击70%，其他各15%
                    phyRatio = 0.15;
                    magRatio = 0.15;
                    budRatio = 0.70;
                    primaryAttackType = "佛法";
                    break;
                default:
                    // 默认剑客类型
                    phyRatio = 0.70;
                    magRatio = 0.15;
                    budRatio = 0.15;
                    primaryAttackType = "物理";
                    break;
            }

            // 计算混合伤害（每种攻击类型分别计算，然后按比例合并）
            int phyDamage = calculateSingleTypeDamage(monsterPhyAtk, playerPhyDef, monster.getLevel());
            int magDamage = calculateSingleTypeDamage(monsterMagAtk, playerMagDef, monster.getLevel());
            int budDamage = calculateSingleTypeDamage(monsterBudAtk, playerBudDef, monster.getLevel());

            // 按比例合并伤害
            int baseDamage = (int) (phyDamage * phyRatio + magDamage * magRatio + budDamage * budRatio);

            // baseDamage已经是混合伤害，包含了防御计算
            int counterDamage = Math.max(1, baseDamage);

            // 应用等级差修正
            int levelDiff = character.getLevel() - monster.getLevel();
            if (levelDiff > 0) {
                // 玩家等级高，减少受到的伤害
                double reduction = Math.min(0.5, levelDiff * 0.05); // 最多减少50%
                counterDamage = (int) (counterDamage * (1 - reduction));
            } else if (levelDiff < 0) {
                // 玩家等级低，增加受到的伤害
                double increase = Math.min(0.5, Math.abs(levelDiff) * 0.05); // 最多增加50%
                counterDamage = (int) (counterDamage * (1 + increase));
            }

            // 确保至少造成1点伤害
            counterDamage = Math.max(1, counterDamage);

            // 应用伤害
            int newHp = Math.max(0, character.getHp() - counterDamage);
            playerManager.updateCharacterHpMp(character, newHp, character.getMp());

            //死亡清理战斗状态
            if (newHp == 0) {
                clearBattleState(character.getId());
            }

            log.debug("怪物反击: {}({}) -> {}({}), 主要攻击类型={}, 混合伤害=物理{}×{}%+法术{}×{}%+佛法{}×{}%, 最终伤害={}",
                     monster.getName(), monster.getLevel(), character.getName(), character.getLevel(),
                     primaryAttackType, phyDamage, (int)(phyRatio*100), magDamage, (int)(magRatio*100),
                     budDamage, (int)(budRatio*100), counterDamage);

            return formatCounterAttackResult(monster, counterDamage, newHp);

        } catch (Exception e) {
            log.error("处理怪物反击失败: monster={}, character={}", monster.getName(), character.getName(), e);
            // 兜底处理
            int counterDamage = monster.getLevel() * 2;
            int newHp = Math.max(0, character.getHp() - counterDamage);
            playerManager.updateCharacterHpMp(character, newHp, character.getMp());
            return formatCounterAttackResult(monster, counterDamage, newHp);
        }
    }

    /**
     * 计算单一类型的伤害（攻击力 vs 防御力） - 怪物攻击用
     * @param attack 攻击力
     * @param defense 防御力
     * @param level 怪物等级
     * @return 计算后的伤害值
     */
    private int calculateSingleTypeDamage(int attack, int defense, int level) {
        // 基础伤害 = 攻击力 + 等级加成
        int baseDamage = attack + (level * 2);
        // 应用防御减伤（防御值减少伤害，但至少造成1点伤害）
        return Math.max(1, baseDamage - (int)(defense*0.9));
    }

    /**
     * 计算玩家单一类型的伤害（攻击力 vs 防御力） - 玩家攻击用
     * @param attack 攻击力
     * @param defense 防御力
     * @param level 攻击者等级
     * @return 计算后的伤害值
     */
    private int calculatePlayerDamage(int attack, int defense, int level) {
        // 玩家基础伤害 = 攻击力 + 等级加成（玩家等级加成较小）
        int baseDamage = attack + level*5;
        // 应用防御减伤（防御值减少伤害，但至少造成1点伤害），防御力要打折，不然打不出伤害
        return Math.max(1, baseDamage - (int)(defense*0.8));
    }

    /**
     * 格式化反击结果文案
     */
    private String formatCounterAttackResult(Monster monster, int damage, int newHp) {
        if (newHp <= 0) {
            return "💀 " + monster.getName() + "反击了" + damage + "点伤害，你被弄死了！";
        } else {
            return "🗡️ " + monster.getName() + "反击了" + damage + "点伤害，剩余血量：" + newHp;
        }
    }

    // ==================== 冷却和状态管理 ====================

    /**
     * 检查是否在攻击冷却中
     */
    private boolean isInAttackCooldown(Long characterId) {
        BattleState state = battleStates.get(characterId);
        if (state == null) {
            return false;
        }
        return System.currentTimeMillis() < state.getLastAttackTime() + NORMAL_ATTACK_COOLDOWN;
    }

    /**
     * 设置攻击冷却
     */
    private void setAttackCooldown(Long characterId) {
        BattleState state = battleStates.computeIfAbsent(characterId, k -> new BattleState());
        state.setLastAttackTime(System.currentTimeMillis());
    }

    /**
     * 检查技能是否在冷却中
     */
    private boolean isSkillInCooldown(Long characterId, String skillNo) {
        Map<String, Long> characterCooldowns = skillCooldowns.get(characterId);
        if (characterCooldowns == null) {
            return false;
        }
        
        Long cooldownEnd = characterCooldowns.get(skillNo);
        return cooldownEnd != null && System.currentTimeMillis() < cooldownEnd;
    }

    /**
     * 获取技能冷却剩余时间
     */
    private long getSkillCooldownRemaining(Long characterId, String skillNo) {
        Map<String, Long> characterCooldowns = skillCooldowns.get(characterId);
        if (characterCooldowns == null) {
            return 0;
        }
        
        Long cooldownEnd = characterCooldowns.get(skillNo);
        if (cooldownEnd == null) {
            return 0;
        }
        
        return Math.max(0, cooldownEnd - System.currentTimeMillis());
    }

    /**
     * 设置技能冷却
     */
    private void setSkillCooldown(Long characterId, String skillNo, long cooldownDuration) {
        skillCooldowns.computeIfAbsent(characterId, k -> new ConcurrentHashMap<>())
                     .put(skillNo, System.currentTimeMillis() + cooldownDuration);
    }

    /**
     * 设置战斗状态
     */
    private void setBattleState(Long characterId, BattleType type, String targetId) {
        BattleState state = battleStates.computeIfAbsent(characterId, k -> new BattleState());
        state.setBattleType(type);
        state.setTargetId(targetId);
        state.setInBattle(true);
    }

    /**
     * 清除战斗状态
     */
    private void clearBattleState(Long characterId) {
        battleStates.remove(characterId);
    }

    // ==================== 内部类 ====================

    /**
     * 战斗状态类
     */
    public static class BattleState {
        private boolean inBattle;
        private BattleType battleType;
        private String targetId;
        private long lastAttackTime;

        // Getters and Setters
        public boolean isInBattle() { return inBattle; }
        public void setInBattle(boolean inBattle) { this.inBattle = inBattle; }
        public BattleType getBattleType() { return battleType; }
        public void setBattleType(BattleType battleType) { this.battleType = battleType; }
        public String getTargetId() { return targetId; }
        public void setTargetId(String targetId) { this.targetId = targetId; }
        public long getLastAttackTime() { return lastAttackTime; }
        public void setLastAttackTime(long lastAttackTime) { this.lastAttackTime = lastAttackTime; }
    }

    /**
     * 战斗类型枚举
     */
    public enum BattleType {
        PVE, // 打怪
        PVP  // 玩家对战
    }

    // ==================== 公共接口方法 ====================

    /**
     * 获取角色战斗状态
     */
    public BattleState getBattleState(Long characterId) {
        return battleStates.get(characterId);
    }

    /**
     * 检查角色是否在战斗中
     */
    public boolean isInBattle(Long characterId) {
        BattleState state = battleStates.get(characterId);
        return state != null && state.isInBattle();
    }

    /**
     * 撤退（退出战斗）
     */
    public String retreat(Long characterId) {
        BattleState state = battleStates.get(characterId);
        if (state == null || !state.isInBattle()) {
            return "🤷 大侠当前并未在战斗中";
        }

        clearBattleState(characterId);
        return "🏃 大侠明智撤退，脱离了战斗";
    }

    /**
     * 获取技能冷却信息
     */
    public Map<String, Long> getSkillCooldowns(Long characterId) {
        Map<String, Long> characterCooldowns = skillCooldowns.get(characterId);
        if (characterCooldowns == null) {
            return new HashMap<>();
        }

        Map<String, Long> result = new HashMap<>();
        long currentTime = System.currentTimeMillis();

        for (Map.Entry<String, Long> entry : characterCooldowns.entrySet()) {
            long remaining = entry.getValue() - currentTime;
            if (remaining > 0) {
                result.put(entry.getKey(), remaining);
            }
        }

        return result;
    }

    /**
     * 清理过期的冷却时间（定时任务调用）
     */
    public void cleanExpiredCooldowns() {
        log.info("清理过期的技能冷却");

        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;

        for (Map.Entry<Long, Map<String, Long>> characterEntry : skillCooldowns.entrySet()) {
            Map<String, Long> characterCooldowns = characterEntry.getValue();

            Iterator<Map.Entry<String, Long>> iterator = characterCooldowns.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Long> cooldownEntry = iterator.next();

                if (currentTime >= cooldownEntry.getValue()) {
                    iterator.remove();
                    cleanedCount++;
                }
            }
        }

        if (cleanedCount > 0) {
            log.info("清理了{}个过期的技能冷却", cleanedCount);
        }
    }

    /**
     * 复活角色（清除战斗状态）
     */
    public void onCharacterRevive(Long characterId) {
        clearBattleState(characterId);
        log.info("角色复活，清除战斗状态: characterId={}", characterId);
    }

    /**
     * 角色死亡处理（清除战斗状态）
     */
    public void onCharacterDeath(Long characterId) {
        clearBattleState(characterId);
        log.info("角色死亡，清除战斗状态: characterId={}", characterId);
    }

    /**
     * 获取战斗统计信息
     */
    public Map<String, Object> getBattleStats(Long characterId) {
        Map<String, Object> stats = new HashMap<>();

        BattleState state = battleStates.get(characterId);
        stats.put("inBattle", state != null && state.isInBattle());
        stats.put("battleType", state != null ? state.getBattleType() : null);
        stats.put("targetId", state != null ? state.getTargetId() : null);

        // 攻击冷却状态
        boolean attackCooldown = isInAttackCooldown(characterId);
        stats.put("attackCooldown", attackCooldown);

        if (attackCooldown && state != null) {
            long remaining = state.getLastAttackTime() + NORMAL_ATTACK_COOLDOWN - System.currentTimeMillis();
            stats.put("attackCooldownRemaining", Math.max(0, remaining));
        }

        // 技能冷却状态
        Map<String, Long> skillCooldowns = getSkillCooldowns(characterId);
        stats.put("skillCooldowns", skillCooldowns);

        return stats;
    }

    // ==================== 掉落系统 ====================

    /**
     * 处理怪物掉落物品
     */
    private String processMonsterDrops(UserCharacter character, Monster monster) {
        try {
            log.info("处理怪物掉落: characterId={}, monsterNo={}", character.getId(), monster.getMonsterNo());

            // 获取掉落配置
            DropsConfig dropsConfig = configManager.getDropsConfig();
            if (dropsConfig == null || dropsConfig.getDrops() == null) {
                log.debug("没有掉落配置");
                return "";
            }

            DropsConfig.MonsterDrop monsterDrop = dropsConfig.getDrops().get(monster.getMonsterNo());
            if (monsterDrop == null || monsterDrop.getDropGroups() == null) {
                log.debug("怪物{}没有掉落配置", monster.getMonsterNo());
                return "";
            }

            StringBuilder dropResult = new StringBuilder();
            List<String> droppedItems = new ArrayList<>();

            // 遍历掉落组
            for (DropsConfig.DropGroup dropGroup : monsterDrop.getDropGroups()) {
                // 检查掉落组概率
                if (dropGroup.getRate() != null && Math.random() > dropGroup.getRate()) {
                    continue; // 掉落组概率不中
                }

                // 处理掉落组中的物品
                if (dropGroup.getItems() != null) {
                    for (DropsConfig.DropItem dropItem : dropGroup.getItems()) {
                        // 检查物品掉落概率
                        if (dropItem.getRate() != null && Math.random() > dropItem.getRate()) {
                            continue; // 物品概率不中
                        }

                        // 计算掉落数量
                        int dropCount = calculateDropCount(dropItem);
                        if (dropCount <= 0) {
                            continue;
                        }

                        // 应用等级差修正
                        dropCount = applyLevelDifferenceModifier(character, monster, dropCount);
                        if (dropCount <= 0) {
                            continue;
                        }

                        // 添加物品到背包
                        try {
                            //如果是装备，数量最多为1，并有一定概率掉落极品属性
                            Item item  = itemMapper.selectByItemNo(dropItem.getItemNo());
                            JSONObject itemAttributes = new JSONObject();
                            if(item.getType() == 0){
                                dropCount = 1;
                                //随机极品属性
                                setRandomAttributes(itemAttributes,item);
                            }
                            String attributes = itemAttributes.isEmpty()?null:itemAttributes.toJSONString();
                            assetManager.addItemToInventory(character.getId(), dropItem.getItemNo(),attributes, dropCount);
                            droppedItems.add(getItemDisplayName(dropItem.getItemNo()) + " x" + dropCount);
                            log.info("掉落物品: characterId={}, itemNo={}, count={}", character.getId(), dropItem.getItemNo(), dropCount);
                        } catch (Exception e) {
                            log.error("添加掉落物品失败: itemNo={}, count={}", dropItem.getItemNo(), dropCount, e);
                        }
                    }
                }
            }

            // 生成掉落结果文案
            if (!droppedItems.isEmpty()) {
                dropResult.append("🎁 获得掉落物品：");
                for (int i = 0; i < droppedItems.size(); i++) {
                    if (i > 0) {
                        dropResult.append("、");
                    }
                    dropResult.append(droppedItems.get(i));
                }
            }

            return dropResult.toString();

        } catch (Exception e) {
            log.error("处理怪物掉落失败: characterId={}, monsterNo={}", character.getId(), monster.getMonsterNo(), e);
            return "";
        }
    }

    /**
     * 掉落的物品，增加随机属性
     * @param itemAttributes
     * @param item
     */
    private void setRandomAttributes(JSONObject itemAttributes, Item item) {
        //1. 根据物品的基础属性，随机获取基础属性里面0点至20%的随机属性
        //   每个基础属性，要判断10%的概率获取到，如果获取到，再随机获取0点至20%的属性值
        JSONObject baseAttributes = JSON.parseObject(item.getAttributes());
        for (String key : baseAttributes.keySet()) {
            if (Math.random() < 0.1) {
                int value = baseAttributes.getIntValue(key);
                int randomValue = (int) (value * 0.2 * Math.random());
                itemAttributes.put(key, randomValue);
            }
        }

        //2. 根据物品的品质，随机获取稀有属性（GameAttributeConstant里面特殊属性）
        //   每个稀有属性，要判2%的概率获取到，如果获取到，再随机获取0点至10的属性值
        if (Math.random() < 0.1) {
            itemAttributes.put(GameAttributeConstant.REFLECT, (int) (10 * Math.random()));
        }
        if (Math.random() < 0.1) {
            itemAttributes.put(GameAttributeConstant.CRIT, (int) (10 * Math.random()));
        }
        if (Math.random() < 0.1) {
            itemAttributes.put(GameAttributeConstant.INNER, (int) (10 * Math.random()));
        }
        if (Math.random() < 0.1) {
            itemAttributes.put(GameAttributeConstant.HP, (int) (50 * Math.random()));
        }
        if (Math.random() < 0.1) {
            itemAttributes.put(GameAttributeConstant.MP, (int) (50 * Math.random()));
        }
    }

    /**
     * 计算掉落数量
     */
    private int calculateDropCount(com.xiziworld.gameserver.domain.manager.config.DropsConfig.DropItem dropItem) {
        if (dropItem.getCount() != null) {
            // 固定数量
            return dropItem.getCount();
        } else if (dropItem.getMin() != null && dropItem.getMax() != null) {
            // 随机数量
            return dropItem.getMin() + new Random().nextInt(dropItem.getMax() - dropItem.getMin() + 1);
        } else if (dropItem.getMin() != null) {
            // 只有最小值，默认最大值为最小值
            return dropItem.getMin();
        } else if (dropItem.getMax() != null) {
            // 只有最大值，默认最小值为1
            return 1 + new Random().nextInt(dropItem.getMax());
        }

        // 默认掉落1个
        return 1;
    }

    /**
     * 应用等级差修正
     */
    private int applyLevelDifferenceModifier(UserCharacter character, Monster monster, int originalCount) {
        try {
            DropsConfig dropsConfig = configManager.getDropsConfig();
            if (dropsConfig.getGlobalDropConfig() == null ||
                dropsConfig.getGlobalDropConfig().getBonus() == null ||
                dropsConfig.getGlobalDropConfig().getBonus().getLevelDifference() == null) {
                return originalCount; // 没有等级差配置，返回原数量
            }

            int levelDiff = character.getLevel() - monster.getLevel();
            double modifier = 1.0;

            DropsConfig.LevelDifference levelDiffConfig = dropsConfig.getGlobalDropConfig().getBonus().getLevelDifference();

            if (levelDiff > 0) {
                // 玩家等级高于怪物，应用惩罚
                DropsConfig.HigherPenalty penalty = levelDiffConfig.getHigherPenalty();
                if (penalty != null) {
                    if (levelDiff >= 10 && penalty.getOver10Levels() != null) {
                        modifier = penalty.getOver10Levels();
                    } else if (levelDiff >= 6 && penalty.getLevels6To10() != null) {
                        modifier = penalty.getLevels6To10();
                    } else if (penalty.getLevels1To5() != null) {
                        modifier = penalty.getLevels1To5();
                    }
                }
            } else if (levelDiff < 0) {
                // 玩家等级低于怪物，应用加成
                int absLevelDiff = Math.abs(levelDiff);
                DropsConfig.LowerBonus bonus = levelDiffConfig.getLowerBonus();
                if (bonus != null) {
                    if (absLevelDiff >= 10 && bonus.getOver10Levels() != null) {
                        modifier = bonus.getOver10Levels();
                    } else if (absLevelDiff >= 6 && bonus.getLevels6To10() != null) {
                        modifier = bonus.getLevels6To10();
                    } else if (absLevelDiff >= 1 && bonus.getLevels1To5() != null) {
                        modifier = bonus.getLevels1To5();
                    }
                }
            }

            int modifiedCount = (int) Math.round(originalCount * modifier);
            return Math.max(0, modifiedCount); // 确保不会是负数

        } catch (Exception e) {
            log.error("应用等级差修正失败", e);
            return originalCount;
        }
    }

    /**
     * 获取物品显示名称
     */
    private String getItemDisplayName(String itemNo) {
        if(itemNo==null){
            return "";
        }
        Item item = itemMapper.selectByItemNo(itemNo);
        return item==null?itemNo:item.getName();
    }
}
