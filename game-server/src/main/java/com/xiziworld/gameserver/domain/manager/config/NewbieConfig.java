package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 新手配置类
 * 对应 game/newbie.yml
 *
 * <AUTHOR>
 */
@Data
public class NewbieConfig {

    /**
     * 保护期配置
     */
    private Protection protection;

    /**
     * 初始资源配置
     */
    private InitialResources initialResources;

    /**
     * 初始武器配置
     * Key: 职业ID, Value: 武器配置
     */
    private Map<String, InitialWeapon> initialWeapons;

    /**
     * 新手任务配置
     */
    private TutorialQuests tutorialQuests;

    /**
     * 新手引导配置
     */
    private Guidance guidance;
    
    /**
     * 保护期配置
     */
    @Data
    public static class Protection {
        /**
         * 保护期结束等级
         */
        private Integer endLevel;

        /**
         * 保护期属性加成
         */
        private AttributeBonus attributeBonus;
    }

    /**
     * 属性加成配置
     */
    @Data
    public static class AttributeBonus {
        /**
         * 物防增加
         */
        private Integer defenseW;

        /**
         * 法防增加
         */
        private Integer defenseM;

        /**
         * 佛防增加
         */
        private Integer defenseF;

        /**
         * 物攻增加
         */
        private Integer attackW;

        /**
         * 法攻增加
         */
        private Integer attackM;

        /**
         * 佛攻增加
         */
        private Integer attackF;
    }
    
    /**
     * 初始资源配置
     */
    @Data
    public static class InitialResources {
        /**
         * 初始金币
         */
        private Integer gold;

        /**
         * 初始银两
         */
        private Integer silver;
    }

    /**
     * 初始武器配置
     */
    @Data
    public static class InitialWeapon {
        /**
         * 物品编号
         */
        private String itemNo;

        /**
         * 装备位置
         */
        private Integer position;

        /**
         * 品级
         */
        private Integer degree;
    }

    /**
     * 新手任务配置
     */
    @Data
    public static class TutorialQuests {
        /**
         * 必做新手任务序列
         */
        private List<TutorialQuest> mandatorySequence;

        /**
         * 新手任务奖励
         */
        private Map<String, TutorialReward> tutorialRewards;
    }

    /**
     * 新手任务配置
     */
    @Data
    public static class TutorialQuest {
        /**
         * 任务ID
         */
        private String questId;

        /**
         * 是否自动接受
         */
        private Boolean autoAccept;

        /**
         * 是否自动完成
         */
        private Boolean autoComplete;
    }

    /**
     * 新手任务奖励配置
     */
    @Data
    public static class TutorialReward {
        /**
         * 经验奖励
         */
        private Integer exp;

        /**
         * 银两奖励
         */
        private Integer silver;

        /**
         * 物品奖励
         */
        private List<RewardItem> items;
    }

    /**
     * 奖励物品配置
     */
    @Data
    public static class RewardItem {
        /**
         * 物品编号
         */
        private String item_no;

        /**
         * 物品数量
         */
        private Integer count;
    }

    /**
     * 新手引导配置
     */
    @Data
    public static class Guidance {
        /**
         * 强制引导步骤
         */
        private List<GuidanceStep> forcedSteps;

        /**
         * 提示消息配置
         */
        private Map<String, String> hintMessages;
    }

    /**
     * 引导步骤配置
     */
    @Data
    public static class GuidanceStep {
        /**
         * 步骤名称
         */
        private String step;

        /**
         * 步骤描述
         */
        private String description;

        /**
         * 是否可跳过
         */
        private Boolean skippable;
    }
    
    // 新手相关常量
    public static final int NEWBIE_LEVEL_LIMIT = 10;           // 新手等级上限
    public static final int NEWBIE_PROTECTION_HOURS = 24;      // 新手保护时长（小时）
    public static final int INITIAL_SILVER = 0;             // 初始银两
    public static final int INITIAL_GOLD = 0;                  // 初始金币

    // 保护地图常量
    public static final String PROTECTED_MAP_WULIN = "wulin";        // 武林主城
    public static final String PROTECTED_MAP_SUDI = "sudi";          // 苏堤

    /**
     * 检查是否在保护期内
     */
    public boolean isInProtectionPeriod(int level) {
        return protection.getEndLevel() > level;
    }
    

}
