package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 怪物实体
 *
 * <AUTHOR>
 */
@Data
@TableName("monster")
@EqualsAndHashCode(callSuper = true)
public class Monster extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 怪物编号
     */
    private String monsterNo;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型(normal:普通 elite:精英 boss:BOSS)
     */
    private String type;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 血量
     */
    private Integer hp;

    /**
     * 魔法攻击
     */
    private Integer attackM;

    /**
     * 佛法攻击
     */
    private Integer attackF;

    /**
     * 物理攻击
     */
    private Integer attackW;

    /**
     * 魔法防御
     */
    private Integer defenseM;

    /**
     * 佛法防御
     */
    private Integer defenseF;

    /**
     * 物理防御
     */
    private Integer defenseW;

    /**
     * 描述
     */
    private String description;
} 