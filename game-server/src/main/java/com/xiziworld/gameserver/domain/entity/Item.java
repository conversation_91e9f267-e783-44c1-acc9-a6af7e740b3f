package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 道具实体
 *
 * <AUTHOR>
 */
@Data
@TableName("item")
@EqualsAndHashCode(callSuper = true)
public class Item extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 道具编号
     */
    private String itemNo;

    /**
     * 类型(0:装备 1:药品 2:技能书 3:材料 9:神奇盒子)
     */
    private Integer type;

    /**
     * 子类型(装备部位/药品类型/材料类型/盒子等级等)
     */
    private Integer subType;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 属性JSON，见GameAttributeConstant定义的key属性
     */
    private String attributes;

    /**
     * 职业限制
     */
    private Integer roleLimit;

    /**
     * 等级限制
     */
    private Integer levelLimit;
} 