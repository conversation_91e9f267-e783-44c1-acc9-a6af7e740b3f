package com.xiziworld.gameserver.domain.manager.player;

import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.mapper.UserCharacterMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 用户角色缓存管理器
 * 实现写回缓存策略，提升角色数据读写性能
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserCharacterCacheManager {
    
    @Autowired
    private UserCharacterMapper userCharacterMapper;
    
    // 缓存数据：characterId -> CachedCharacter
    private final ConcurrentHashMap<Long, CachedCharacter> characterCache = new ConcurrentHashMap<>();
    
    // 脏数据标记：需要写回数据库的characterId
    private final Set<Long> dirtyCharacters = ConcurrentHashMap.newKeySet();
    
    // 统计信息
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong writeBackCount = new AtomicLong(0);
    
    /**
     * 缓存包装类
     */
    private static class CachedCharacter {
        private final UserCharacter character;
        private final long cacheTime;
        @Getter
        private volatile long lastAccessTime;
        
        public CachedCharacter(UserCharacter character) {
            this.character = character;
            this.cacheTime = System.currentTimeMillis();
            this.lastAccessTime = cacheTime;
        }
        
        public UserCharacter getCharacter() {
            this.lastAccessTime = System.currentTimeMillis();
            return character;
        }

        public void markUpdated() {
            this.lastAccessTime = System.currentTimeMillis();
        }

    }
    
    // ==================== 核心缓存操作方法 ====================
    
    /**
     * 获取角色信息（优先从缓存）
     */
    public UserCharacter getCharacter(Long characterId) {
        if (characterId == null) {
            return null;
        }
        
        // 1. 尝试从缓存获取
        CachedCharacter cached = characterCache.get(characterId);
        if (cached != null) {
            cacheHits.incrementAndGet();
            log.debug("缓存命中: characterId={}", characterId);
            return cached.getCharacter();
        }
        
        // 2. 缓存未命中，从数据库查询
        cacheMisses.incrementAndGet();
        UserCharacter character = userCharacterMapper.selectById(characterId);
        if (character != null) {
            // 3. 放入缓存
            characterCache.put(characterId, new CachedCharacter(character));
            log.debug("缓存加载: characterId={}", characterId);
        }
        
        return character;
    }
    
    /**
     * 更新角色信息（写入缓存并标记脏数据）
     */
    public void updateCharacter(UserCharacter character) {
        if (character == null || character.getId() == null) {
            return;
        }
        
        Long characterId = character.getId();
        
        // 1. 更新缓存
        CachedCharacter cached = characterCache.get(characterId);
        if (cached != null) {
            // 更新现有缓存
            updateCachedCharacter(cached.character, character);
            cached.markUpdated();
        } else {
            // 创建新缓存
            characterCache.put(characterId, new CachedCharacter(character));
        }
        
        // 2. 标记为脏数据
        dirtyCharacters.add(characterId);
        
        log.debug("缓存更新: characterId={}", characterId);
    }
    
    /**
     * 插入新角色（直接写数据库并缓存）
     */
    public void insertCharacter(UserCharacter character) {
        if (character == null) {
            return;
        }
        
        // 1. 写入数据库
        userCharacterMapper.insert(character);
        
        // 2. 放入缓存
        if (character.getId() != null) {
            characterCache.put(character.getId(), new CachedCharacter(character));
            log.debug("新角色缓存: characterId={}", character.getId());
        }
    }
    
    /**
     * 根据用户信息查询角色
     */
    public UserCharacter getCharacterByUserInfo(String userId, Integer appId, String openId, Long areaId) {
        for(Map.Entry<Long, CachedCharacter> entry : characterCache.entrySet()) {
            UserCharacter character = entry.getValue().getCharacter();
            if (character.getUserId().equals(userId) &&
                character.getAppId()==appId.intValue() &&
                character.getOpenId().equals(openId) &&
                character.getAreaId().equals(areaId)) {
                return character;
            }
        }
        UserCharacter character = userCharacterMapper.selectByUserInfo(userId, appId, openId, areaId);
        if (character != null) {
            characterCache.put(character.getId(), new CachedCharacter(character));
        }
        return character;
    }

    public UserCharacter getCharacterByAreaAndName(Long areaId, String name) {
        for(Map.Entry<Long, CachedCharacter> entry : characterCache.entrySet()) {
            UserCharacter character = entry.getValue().getCharacter();
            if (character.getAreaId().equals(areaId) && character.getName().equals(name)) {
                return character;
            }
        }
        UserCharacter character = userCharacterMapper.selectByAreaAndName(areaId, name);
        if (character != null) {
            characterCache.put(character.getId(), new CachedCharacter(character));
        }
        return character;
    }
    
    /**
     * 检查角色名是否存在
     */
    public boolean isCharacterNameExists(Long areaId, String name) {
        for(Map.Entry<Long, CachedCharacter> entry : characterCache.entrySet()) {
            UserCharacter character = entry.getValue().getCharacter();
            if (character.getAreaId().equals(areaId) && character.getName().equals(name)) {
                return true;
            }
        }
        return false;
    }
    
    // ==================== 生命周期管理方法 ====================
    
    /**
     * 角色上线时预加载到缓存
     */
    public void preloadCharacterToCache(Long characterId) {
        if (characterId != null && !characterCache.containsKey(characterId)) {
            getCharacter(characterId); // 触发缓存加载
            log.info("角色上线预加载缓存: characterId={}", characterId);
        }
    }
    
    /**
     * 角色下线时立即写回数据库
     */
    public void onCharacterOffline(Long characterId) {
        if (characterId != null) {
            // 立即写回数据库
            flushCharacterToDatabase(characterId);
            log.info("角色下线数据写回: characterId={}", characterId);
        }
    }
    
    // ==================== 定时任务 ====================
    
    /**
     * 定时任务：每分钟写回脏数据到数据库
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void flushDirtyDataToDatabase() {
        if (dirtyCharacters.isEmpty()) {
            return;
        }
        
        log.info("开始写回脏数据到数据库，数量: {}", dirtyCharacters.size());
        
        Set<Long> toFlush = new HashSet<>(dirtyCharacters);
        int successCount = 0;
        int failCount = 0;
        
        for (Long characterId : toFlush) {
            try {
                if (flushCharacterToDatabase(characterId)) {
                    dirtyCharacters.remove(characterId);
                    successCount++;
                    writeBackCount.incrementAndGet();
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("写回数据库失败: characterId={}", characterId, e);
                failCount++;
            }
        }
        
        log.info("脏数据写回完成，成功: {}, 失败: {}", successCount, failCount);
    }
    
    /**
     * 每小时清理长时间未访问的缓存
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanExpiredCache() {
        long currentTime = System.currentTimeMillis();
        long expireTime = 6 * 60 * 60 * 1000; // 6小时未访问则清理
        
        List<Long> toRemove = new ArrayList<>();
        
        for (Map.Entry<Long, CachedCharacter> entry : characterCache.entrySet()) {
            CachedCharacter cached = entry.getValue();
            if (currentTime - cached.getLastAccessTime() > expireTime) {
                Long characterId = entry.getKey();
                
                // 清理前先写回数据库
                if (dirtyCharacters.contains(characterId)) {
                    flushCharacterToDatabase(characterId);
                    dirtyCharacters.remove(characterId);
                }
                
                toRemove.add(characterId);
            }
        }
        
        // 清理过期缓存
        for (Long characterId : toRemove) {
            characterCache.remove(characterId);
        }
        
        if (!toRemove.isEmpty()) {
            log.info("清理过期缓存: {} 个", toRemove.size());
        }
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 将单个角色数据写回数据库
     */
    private boolean flushCharacterToDatabase(Long characterId) {
        CachedCharacter cached = characterCache.get(characterId);
        if (cached == null) {
            return true; // 缓存中没有，认为成功
        }
        
        try {
            userCharacterMapper.updateById(cached.character);
            log.debug("数据库写回成功: characterId={}", characterId);
            return true;
        } catch (Exception e) {
            log.error("数据库写回失败: characterId={}", characterId, e);
            return false;
        }
    }
    
    /**
     * 更新缓存中的角色数据
     */
    private void updateCachedCharacter(UserCharacter cached, UserCharacter updated) {
        // 只更新经常变化的字段，避免覆盖其他字段
        if (updated.getName() != null) cached.setName(updated.getName());
        if (updated.getType() != null) cached.setType(updated.getType());
        if (updated.getLevel() != null) cached.setLevel(updated.getLevel());
        if (updated.getExp() != null) cached.setExp(updated.getExp());
        if (updated.getHp() != null) cached.setHp(updated.getHp());
        if (updated.getMp() != null) cached.setMp(updated.getMp());
        if (updated.getMaxHp() != null) cached.setMaxHp(updated.getMaxHp());
        if (updated.getMaxMp() != null) cached.setMaxMp(updated.getMaxMp());
        if (updated.getMaxExp() != null) cached.setMaxExp(updated.getMaxExp());
        if (updated.getSkills() != null) cached.setSkills(updated.getSkills());
        if (updated.getAttributes() != null) cached.setAttributes(updated.getAttributes());
        if (updated.getStatus() != null) cached.setStatus(updated.getStatus());
        if (updated.getGameStatus() != null) cached.setGameStatus(updated.getGameStatus());
        if (updated.getPosition() != null) cached.setPosition(updated.getPosition());
    }
    
    // ==================== 监控和管理方法 ====================
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", characterCache.size());
        stats.put("dirtyCount", dirtyCharacters.size());
        stats.put("cacheHits", cacheHits.get());
        stats.put("cacheMisses", cacheMisses.get());
        stats.put("writeBackCount", writeBackCount.get());
        
        long totalRequests = cacheHits.get() + cacheMisses.get();
        double hitRate = totalRequests > 0 ? (double) cacheHits.get() / totalRequests * 100 : 0;
        stats.put("hitRate", String.format("%.2f%%", hitRate));
        
        // 估算内存使用（每个角色对象约1KB）
        stats.put("estimatedMemoryUsage", characterCache.size() + "KB");
        
        return stats;
    }
    
    /**
     * 强制刷新所有脏数据（管理接口）
     */
    public void forceFlushAll() {
        log.warn("强制刷新所有脏数据");
        flushDirtyDataToDatabase();
    }
    
    /**
     * 清空缓存（谨慎使用）
     */
    public void clearCache() {
        log.warn("清空缓存操作开始");
        
        // 先写回所有脏数据
        flushDirtyDataToDatabase();
        
        // 清空缓存
        characterCache.clear();
        dirtyCharacters.clear();
        
        // 重置统计
        cacheHits.set(0);
        cacheMisses.set(0);
        writeBackCount.set(0);
        
        log.warn("缓存已清空");
    }
}
