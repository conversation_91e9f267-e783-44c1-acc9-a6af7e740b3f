package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 地图刷怪配置类
 * 对应 world/map_refresh.yml
 * 
 * <AUTHOR>
 */
@Data
public class MapRefreshConfig {
    
    /**
     * 地图刷怪配置映射
     * Key: 地图ID, Value: 刷怪详细配置
     */
    private Map<String, MapRefreshDetail> mapRefresh;
    
    /**
     * 单个地图的刷怪配置
     */
    @Data
    public static class MapRefreshDetail {
        /**
         * 地图ID
         */
        private String mapId;

        /**
         * 地图名称
         */
        private String mapName;

        /**
         * 刷怪点列表
         */
        private List<RefreshPoint> refreshPoints;
    }
    
    /**
     * 刷怪点配置
     */
    @Data
    public static class RefreshPoint {
        /**
         * 怪物编号
         */
        private String monsterNo;

        /**
         * 刷怪位置
         */
        private Position position;

        /**
         * 刷新间隔(秒)
         */
        private Integer interval;

        /**
         * 刷新数量
         */
        private Integer count;
    }

    /**
     * 位置坐标配置
     */
    @Data
    public static class Position {
        /**
         * X坐标
         */
        private Integer x;

        /**
         * Y坐标
         */
        private Integer y;
    }

    
    // 地图ID常量（与MapsConfig保持一致）
    public static final String MAP_WULIN = "wulin";           // 武林主城
    public static final String MAP_SUDI = "sudi";             // 苏堤
    public static final String MAP_DUANQIAO = "duanqiao";     // 断桥
    public static final String MAP_GUSHAN = "gushan";         // 孤山
    public static final String MAP_TAIZIWWAN = "taiziwwan";   // 太子湾
    public static final String MAP_BAOSHUSHAN = "baoshushan"; // 保俶山
    public static final String MAP_MEIJIAWU = "meijiawu";     // 梅家坞
    public static final String MAP_LONGJINGCUN = "longjingcun"; // 龙井村
    public static final String MAP_LEIFENGTA = "leifengta";   // 雷峰塔底
    public static final String MAP_HUXINTING = "huxinting";   // 湖心亭
    
    // 怪物编号常量（与DropsConfig保持一致）
    public static final String MONSTER_WILLOW = "MON_WILLOW";     // 柳树精
    public static final String MONSTER_FROG = "MON_FROG";         // 青蛙
    public static final String MONSTER_GHOST = "MON_GHOST";       // 游魂
    public static final String MONSTER_SOUL = "MON_SOUL";         // 野鬼
    public static final String MONSTER_WOLF = "MON_WOLF";         // 恶狼
    public static final String MONSTER_FLOWER = "MON_FLOWER";     // 花妖
    public static final String MONSTER_MONK = "MON_MONK";         // 游僧
    public static final String MONSTER_FAHAI = "MON_FAHAI";       // 法海
    public static final String MONSTER_DRAGON = "MON_DRAGON";     // 蛟龙
}
