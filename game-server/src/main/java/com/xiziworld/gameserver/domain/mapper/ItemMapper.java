package com.xiziworld.gameserver.domain.mapper;

import com.xiziworld.gameserver.domain.entity.Item;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.cache.impl.PerpetualCache;
import org.apache.ibatis.cache.decorators.LruCache;

/**
 * 道具Mapper接口
 * 使用MyBatis二级缓存，适合相对静态的道具数据
 *
 * <AUTHOR>
 */
@CacheNamespace(
    implementation = PerpetualCache.class,  // 缓存实现类
    eviction = LruCache.class,              // LRU淘汰策略
    size = 1024,                            // 缓存大小
    flushInterval = 18000000,                // 300分钟刷新间隔
    readWrite = false                       // 只读缓存，提高性能
)
public interface ItemMapper extends BasicMapper<Item> {

    /**
     * 根据道具编号查询道具
     */
    @Select("SELECT * FROM item WHERE item_no = #{itemNo}")
    Item selectByItemNo(@Param("itemNo") String itemNo);

    @Select("SELECT * FROM item WHERE name = #{name}")
    Item selectByItemName(@Param("name") String name);
}