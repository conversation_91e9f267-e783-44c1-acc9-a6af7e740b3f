package com.xiziworld.gameserver.domain.manager.config;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 掉落配置类
 * 对应 world/drops.yml
 * 
 * <AUTHOR>
 */
@Data
public class DropsConfig {
    
    /**
     * 掉落配置映射
     * Key: 怪物编号, Value: 掉落详细配置
     */
    private Map<String, MonsterDrop> drops;

    /**
     * 全局掉落配置
     */
    private GlobalDropConfig globalDropConfig;

    /**
     * 单个怪物的掉落配置
     */
    @Data
    public static class MonsterDrop {
        /**
         * 怪物编号
         */
        private String monsterNo;
        
        /**
         * 掉落组列表
         */
        private List<DropGroup> dropGroups;
    }
    
    /**
     * 掉落组配置
     */
    @Data
    public static class DropGroup {
        /**
         * 掉落组ID
         */
        private String groupId;
        
        /**
         * 掉落概率
         */
        private Double rate;
        
        /**
         * 掉落物品列表
         */
        private List<DropItem> items;
    }
    
    /**
     * 掉落物品配置
     */
    @Data
    public static class DropItem {
        /**
         * 物品编号
         */
        private String itemNo;
        
        /**
         * 固定数量
         */
        private Integer count;
        
        /**
         * 最小数量
         */
        private Integer min;
        
        /**
         * 最大数量
         */
        private Integer max;
        
        /**
         * 掉落概率
         */
        private Double rate;
    }
    
    // 怪物编号常量
    public static final String MONSTER_WILLOW = "MON_WILLOW";     // 柳树精
    public static final String MONSTER_FROG = "MON_FROG";         // 青蛙
    public static final String MONSTER_GHOST = "MON_GHOST";       // 游魂
    public static final String MONSTER_SOUL = "MON_SOUL";         // 野鬼
    public static final String MONSTER_WOLF = "MON_WOLF";         // 恶狼
    public static final String MONSTER_FLOWER = "MON_FLOWER";     // 花妖
    public static final String MONSTER_MONK = "MON_MONK";         // 游僧
    public static final String MONSTER_FAHAI = "MON_FAHAI";       // 法海
    public static final String MONSTER_DRAGON = "MON_DRAGON";     // 蛟龙
    
    /**
     * 全局掉落配置
     */
    @Data
    public static class GlobalDropConfig {
        /**
         * 掉落加成配置
         */
        private DropBonus bonus;
    }

    /**
     * 掉落加成配置
     */
    @Data
    public static class DropBonus {
        /**
         * VIP掉落加成
         */
        private Map<String, Double> vipBonus;

        /**
         * 等级差掉落修正
         */
        private LevelDifference levelDifference;

        /**
         * 组队掉落加成
         */
        private Map<String, Double> partyBonus;
    }

    /**
     * 等级差掉落修正
     */
    @Data
    public static class LevelDifference {
        /**
         * 等级高于怪物时的掉落惩罚
         */
        private HigherPenalty higherPenalty;

        /**
         * 等级低于怪物时的掉落加成
         */
        private LowerBonus lowerBonus;
    }

    /**
     * 高等级惩罚
     */
    @Data
    public static class HigherPenalty {
        private Double levels1To5;     // 高1-5级
        private Double levels6To10;    // 高6-10级
        private Double over10Levels;   // 高10级以上
    }

    /**
     * 低等级加成
     */
    @Data
    public static class LowerBonus {
        private Double levels1To5;     // 低1-5级
        private Double levels6To10;    // 低6-10级
        private Double over10Levels;   // 低10级以上
    }

    // 掉落组类型常量
    public static final String GROUP_COMMON = "common";           // 普通掉落
    public static final String GROUP_RARE = "rare";               // 稀有掉落
    public static final String GROUP_EPIC = "epic";               // 史诗掉落
    public static final String GROUP_LEGENDARY = "legendary";     // 传说掉落
    public static final String GROUP_MATERIALS = "materials";     // 材料掉落
    public static final String GROUP_EQUIPMENT = "equipment";     // 装备掉落
    public static final String GROUP_SKILLS = "skills";           // 技能掉落
    public static final String GROUP_GUARANTEED = "guaranteed";   // 保底掉落
}
