package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户资产实体
 *
 * <AUTHOR>
 */
@Data
@TableName("user_asset")
@EqualsAndHashCode(callSuper = true)
public class UserAsset extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 区服ID
     */
    private Long characterId;

    /**
     * 道具编号
     */
    private String itemNo;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 道具属性JSON
     * 如果是装备，除itemNo定义之外的附加属性：degree-品级，plus-增益（玉佩特有），其他随机生成的三防三功属性，具体见GameAttributeConstant定义key)
     */
    private String attributes;

    /**
     * 位置(1:身上 2:背包 3:仓库)
     */
    private Integer position;

    /**
     * 背包格子序号(1-50，仅position=2时有效)
     */
    private Integer bagSlot;
} 