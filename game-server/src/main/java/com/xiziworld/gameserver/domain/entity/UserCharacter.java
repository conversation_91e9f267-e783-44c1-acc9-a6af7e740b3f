package com.xiziworld.gameserver.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户角色实体
 *
 * <AUTHOR>
 */
@Data
@TableName("user_character")
@EqualsAndHashCode(callSuper = true)
public class UserCharacter extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 应用ID(0-微信)
     */
    private Integer appId;

    /**
     * 应用open_id
     */
    private String openId;

    /**
     * 区服ID
     */
    private Long areaId;

    /**
     * 角色名
     */
    private String name;

    /**
     * 职业类型(1:剑客 2:仙师 3:圣僧)
     */
    private Integer type;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 经验值
     */
    private Long exp;

    /**
     * 升级所需经验
     */
    private Long maxExp;

    /**
     * 血量
     */
    private Integer hp;

    /**
     * 当前等级最大血量
     */
    private Integer maxHp;
    /**
     * 法力值
     */
    private Integer mp;
    /**
     * 当前等级最大魔法量
     */
    private Integer maxMp;

    /**
     * 习得的技能列表，存放技能编号
     */
    private String skills;

    /**
     * 属性JSON，人物其他属性，见GameAttributeConstant定义的key，一般由以下几部分影响结果：
     * 1.人物等级生成的三攻三防
     * 2.新手保护期生成的保护属性
     * 3.预留后续各种玩法生成的属性
     */
    private String attributes;

    public static final int STATUS_NORMAL = 0;
    public static final int STATUS_DEAD = 1;
    /**
     * 状态(0:正常 1:死亡)
     */
    private Integer status;

    public static final int GAME_STATUS_BANNED = -1;
    public static final int GAME_STATUS_OFFLINE = 0;
    public static final int GAME_STATUS_ONLINE = 1;
    public static final int GAME_STATUS_HANGING = 2;
    /**
     * 游戏状态(-1:账号被禁 0:离线 1:正常在线 2:挂机状态)
     */
    private Integer gameStatus;

    /**
     * 位置信息
     */
    private String position;
} 