package com.xiziworld.gameserver.domain.manager.player;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.xiziworld.gameserver.common.EventPublisher;
import com.xiziworld.gameserver.common.GameException;
import com.xiziworld.gameserver.common.constant.GameAttributeConstant;
import com.xiziworld.gameserver.common.constant.ItemConstant;
import com.xiziworld.gameserver.domain.entity.UserCharacter;
import com.xiziworld.gameserver.domain.entity.UserAsset;
import com.xiziworld.gameserver.domain.entity.Item;
import com.xiziworld.gameserver.domain.manager.AssetManager;
import com.xiziworld.gameserver.domain.mapper.UserAssetMapper;
import com.xiziworld.gameserver.domain.mapper.ItemMapper;
import com.xiziworld.gameserver.domain.manager.config.ConfigManager;
import com.xiziworld.gameserver.domain.manager.config.MapsConfig;
import com.xiziworld.gameserver.domain.manager.MapManager;
import com.xiziworld.gameserver.domain.manager.config.LevelsConfig;
import com.xiziworld.gameserver.domain.manager.config.NewbieConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.xiziworld.gameserver.domain.manager.Helper;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/**
 * 玩家管理器
 * 负责角色创建、属性计算、经验升级等核心功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class PlayerManager {

    @Autowired
    private UserCharacterCacheManager characterCacheManager;

    @Autowired
    private AssetManager assetManager;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private MapManager mapManager;
    
    @Autowired
    private UserAssetMapper userAssetMapper;
    
    @Autowired
    private ConfigManager configManager;


    // ==================== 监听事件 ====================
    @EventListener
    public void listenCharacterEvent(EventPublisher.GameEvent event) {
        if(event.getEventType() == EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_POSITION){
            EventPublisher.Position position = (EventPublisher.Position) event.getEventData();
            updateCharacterPosition(getCharacterById(position.getCharacterId()), position.getMapId(), position.getX(), position.getY());
            return;
        }
        if(event.getEventType()== EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_ATTR){
            updateCharacterAttributesWithEquipment((Long) event.getEventData());
            return;
        }
    }

    // ==================== 角色创建和查询 ====================
    /**
     * 创建角色
     * 
     * @param userId 用户ID
     * @param appId 应用ID
     * @param openId 应用openId
     * @param areaId 区服ID
     * @param name 角色名
     * @param roleType 职业类型(1:剑客 2:仙师 3:圣僧)
     * @return 创建的角色信息
     */
    @Transactional
    public UserCharacter createCharacter(String userId, Integer appId, String openId, Long areaId, String name, Integer roleType) {
        log.info("开始创建角色: userId={}, name={}, roleType={}", userId, name, roleType);
        
        // 验证参数
        validateCreateCharacterParams(userId, appId, openId, areaId, name, roleType);
        
        // 检查角色是否已存在
        UserCharacter existingCharacter = getCharacterByUser(userId, appId, openId, areaId);
        if (existingCharacter != null) {
            throw new GameException(GameException.CHARACTER_ALREADY_EXISTS);
        }
        
        // 检查角色名是否重复
        if (isCharacterNameExists(areaId, name)) {
            throw new GameException(GameException.CHARACTER_NAME_EXISTS);
        }
        
        // 创建角色实体
        UserCharacter character = new UserCharacter();
        character.setUserId(userId);
        character.setAppId(appId);
        character.setOpenId(openId);
        character.setAreaId(areaId);
        character.setName(name);
        character.setType(roleType);
        character.setLevel(1);
        character.setExp(0L);
        // 计算升级所需经验
        long nextLevelExp = configManager.getLevelsConfig().calculateLevelExp(character.getLevel());
        character.setMaxExp(nextLevelExp);

        character.setStatus(UserCharacter.STATUS_NORMAL);
        character.setGameStatus(UserCharacter.GAME_STATUS_ONLINE);
        //出生地
        Helper.setCharacterPosition(character, MapsConfig.MAP_WULIN, 50, 50);

        // 计算初始属性（包含新手保护临时属性）
        JSONObject attributes = calculatePlayerAttributes(character);
        int maxHp = attributes.getIntValue(GameAttributeConstant.HP);
        int maxMp = attributes.getIntValue(GameAttributeConstant.MP);
        character.setHp(maxHp);
        character.setMp(maxMp);
        character.setMaxHp(maxHp);
        character.setMaxMp(maxMp);
        character.setAttributes(attributes.toString());

        // 保存角色（通过缓存管理器）
        characterCacheManager.insertCharacter(character);
        
        // 创建用户资产
        createInitialUserAsset(character.getId(), roleType);
        
        log.info("角色创建成功: characterId={}, name={}", character.getId(), name);
        return character;
    }

    /**
     * 检查角色是否已学会指定技能
     * @param characterId
     * @param skillNo
     * @return
     */
    public boolean hasLearnedSkill(Long characterId, String skillNo) {
        UserCharacter character = getCharacterById(characterId);
        return hasLearnedSkill(character, skillNo);
    }
    /**
     * 检查角色是否已学会指定技能
     * @param character
     * @param skillNo
     * @return
     */
    public boolean hasLearnedSkill(UserCharacter character, String skillNo) {
        if (character == null || skillNo == null) {
            return false;
        }

        String skills = character.getSkills();
        if (skills == null || skills.isEmpty()) {
            return false;
        }

        try {
            JSONArray skillArray = JSON.parseArray(skills);
            if (skillArray == null) {
                return false;
            }

            // 遍历角色已学技能列表
            for (int i = 0; i < skillArray.size(); i++) {
                String learnedSkill = skillArray.getString(i);
                if (skillNo.equals(learnedSkill)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("解析角色技能失败: characterId={}, skills={}", character.getId(), skills, e);
        }
        return false;
    }
    /**
     * 根据用户信息获取角色（通过缓存管理器）
     */
    public UserCharacter getCharacterByUser(String userId, Integer appId, String openId, Long areaId) {
        return characterCacheManager.getCharacterByUserInfo(userId, appId, openId, areaId);
    }

    /**
     * 根据角色ID获取角色（通过缓存）
     */
    public UserCharacter getCharacterById(Long characterId) {
        return characterCacheManager.getCharacter(characterId);
    }
    public UserCharacter getCharacterByName(Long areaId, String name) {
        return characterCacheManager.getCharacterByAreaAndName(areaId, name);
    }

    /**
     * 检查角色名是否存在（通过缓存管理器）
     */
    public boolean isCharacterNameExists(Long areaId, String name) {
        return characterCacheManager.isCharacterNameExists(areaId, name);
    }


    // ==================== 属性计算系统 ====================

    /**
     * 监听事件，更新角色属性（包含装备加成）
     */
    private void updateCharacterAttributesWithEquipment(long characterId) {
        log.info("更新角色属性（包含装备加成）: characterId={}", characterId);

        UserCharacter character = getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        // 获取基础属性（包含等级、新手保护、装备加成）
        JSONObject baseAttributes = calculatePlayerAttributes(character);

        // 更新角色属性
        character.setAttributes(baseAttributes.toJSONString());

        // 更新血量法力值上限（如果当前值超过新上限，则调整）
        int newMaxHp = baseAttributes.getInteger(GameAttributeConstant.HP);
        int newMaxMp = baseAttributes.getInteger(GameAttributeConstant.MP);

        if (character.getMaxHp() < newMaxHp) {
            character.setMaxHp(newMaxHp);
        }
        if (character.getMaxMp() < newMaxMp) {
            character.setMaxMp(newMaxMp);
        }
        characterCacheManager.updateCharacter(character);
    }

    /**
     * 计算角色持久化属性（存储到数据库的属性）
     * 包含：基础属性、等级加成、新手保护、装备加成
     * 不包含：临时战斗状态（禁锢、眩晕等），这些在战斗时动态计算
     */
    public JSONObject calculatePlayerAttributes(UserCharacter character) {
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        
        // 获取基础属性
        LevelsConfig.BaseAttributes baseAttributes = configManager.getBaseAttributesByRole(character.getType());
        if (baseAttributes == null) {
            throw new GameException(GameException.INVALID_ROLE_TYPE);
        }
        
        // 计算等级属性
        JSONObject levelAttributes = calculateLevelAttributes(character.getLevel(), character.getType(), baseAttributes);
        
        // 应用新手保护
        if (configManager.isInNewbieProtection(character.getLevel())) {
            applyNewbieProtection(levelAttributes);
        }
        
        // 应用装备属性加成(初始用户创建时没有装备，所以这里需要判断一下)
        if(character.getId()!=null) {
            applyEquipmentBonus(levelAttributes, character.getId());
        }
        
        // 注意：临时战斗状态（禁锢、眩晕等）不在此计算，在战斗时动态应用
        return levelAttributes;
    }


    /**
     * 计算等级属性（优先使用预计算缓存）
     */
    private JSONObject calculateLevelAttributes(Integer level, Integer roleType, LevelsConfig.BaseAttributes baseAttributes) {
        JSONObject attributes = new JSONObject();

        // 尝试从缓存获取预计算的属性
        try {
            LevelsConfig.LevelAttributes cachedAttributes = configManager.getLevelsConfig().getLevelAttributes(roleType, level);

            if (cachedAttributes != null) {
                // 使用缓存的属性值
                attributes.put(GameAttributeConstant.HP, cachedAttributes.getHp());
                attributes.put(GameAttributeConstant.MP, cachedAttributes.getMp());
                attributes.put(GameAttributeConstant.PHY_ATK, cachedAttributes.getPhyAtk());
                attributes.put(GameAttributeConstant.MAG_ATK, cachedAttributes.getMagAtk());
                attributes.put(GameAttributeConstant.BUD_ATK, cachedAttributes.getBudAtk());
                attributes.put(GameAttributeConstant.PHY_DEF, cachedAttributes.getPhyDef());
                attributes.put(GameAttributeConstant.MAG_DEF, cachedAttributes.getMagDef());
                attributes.put(GameAttributeConstant.BUD_DEF, cachedAttributes.getBudDef());

                // 设置默认值
                attributes.put(GameAttributeConstant.REFLECT, 0);
                attributes.put(GameAttributeConstant.CRIT, 0);
                attributes.put(GameAttributeConstant.INNER, 0);

                log.debug("使用缓存属性: 职业={}, 等级={}, HP={}, MP={}",
                        roleType, level, cachedAttributes.getHp(), cachedAttributes.getMp());

                return attributes;
            }
        } catch (Exception e) {
            log.warn("获取缓存属性失败，使用实时计算: 职业={}, 等级={}, 错误={}",
                    roleType, level, e.getMessage());
        }

        // 缓存未命中或获取失败，使用实时计算（兜底方案）
        return calculateLevelAttributesFallback(level, roleType, baseAttributes);
    }

    /**
     * 兜底的等级属性计算（缓存失效时使用）
     */
    private JSONObject calculateLevelAttributesFallback(Integer level, Integer roleType, LevelsConfig.BaseAttributes baseAttributes) {
        JSONObject attributes = new JSONObject();

        // 基础属性
        int baseHp = baseAttributes.getHp();
        int baseMp = baseAttributes.getMp();
        int baseAttackM = baseAttributes.getAttackM();
        int baseAttackW = baseAttributes.getAttackW();
        int baseAttackF = baseAttributes.getAttackF();
        int baseDefenseW = baseAttributes.getDefenseW();
        int baseDefenseM = baseAttributes.getDefenseM();
        int baseDefenseF = baseAttributes.getDefenseF();

        // 根据等级计算属性成长
        double hpMpGrowth = calculateAttributeGrowth(level, true);
        double otherGrowth = calculateAttributeGrowth(level, false);

        // 计算最终属性
        attributes.put(GameAttributeConstant.HP, (int)(baseHp * (1 + hpMpGrowth)));
        attributes.put(GameAttributeConstant.MP, (int)(baseMp * (1 + hpMpGrowth)));
        attributes.put(GameAttributeConstant.MAG_ATK, (int)(baseAttackM * (1 + otherGrowth)));
        attributes.put(GameAttributeConstant.PHY_ATK, (int)(baseAttackW * (1 + otherGrowth)));
        attributes.put(GameAttributeConstant.BUD_ATK, (int)(baseAttackF * (1 + otherGrowth)));
        attributes.put(GameAttributeConstant.PHY_DEF, (int)(baseDefenseW * (1 + otherGrowth)));
        attributes.put(GameAttributeConstant.MAG_DEF, (int)(baseDefenseM * (1 + otherGrowth)));
        attributes.put(GameAttributeConstant.BUD_DEF, (int)(baseDefenseF * (1 + otherGrowth)));

        // 设置默认值
        attributes.put(GameAttributeConstant.REFLECT, 0);
        attributes.put(GameAttributeConstant.CRIT, 0);
        attributes.put(GameAttributeConstant.INNER, 0);

        log.debug("使用兜底计算: 职业={}, 等级={}", roleType, level);

        return attributes;
    }

    /**
     * 计算属性成长率（从LevelsConfig获取成长参数）
     *
     * @param level 等级
     * @param isHpMp 是否为血量法力值
     * @return 成长率
     */
    private double calculateAttributeGrowth(Integer level, boolean isHpMp) {
        double totalGrowth = 0.0;

        // 获取等级配置
        LevelsConfig levelsConfig = configManager.getLevelsConfig();
        if (levelsConfig == null || levelsConfig.getLevelRanges() == null) {
            log.warn("等级配置为空，使用默认成长参数");
            return Helper.calculateAttributeGrowthWithDefaults(level, isHpMp);
        }

        // 根据等级范围计算累积成长
        for (int i = 2; i <= level; i++) {
            LevelsConfig.LevelRange levelRange = levelsConfig.getLevelRangeForLevel(i);
            if (levelRange != null) {
                double growthRate = isHpMp ?
                    (levelRange.getHpMpIncrease() != null ? levelRange.getHpMpIncrease() : 0.05) :
                    (levelRange.getOtherIncrease() != null ? levelRange.getOtherIncrease() : 0.03);
                totalGrowth += growthRate;
            } else {
                // 如果没有找到对应的等级范围，使用默认值
                totalGrowth += isHpMp ? 0.03 : 0.01;
            }
        }

        return totalGrowth;
    }
    



    // ==================== 经验和升级系统 ====================
    /**
     * 增加经验值
     * 
     * @param characterId 角色ID
     * @param expGain 获得的经验值
     * @return 是否升级
     */
    @Transactional
    public boolean addExperience(Long characterId, Long expGain) {
        log.info("角色获得经验: characterId={}, expGain={}", characterId, expGain);
        
        UserCharacter character = getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }
        
        // 计算新的经验值
        long newExp = character.getExp() + expGain;
        
        // 检查是否可以升级
        boolean levelUp = false;
        int newLevel = character.getLevel();
        long requiredExp = 0;
        while (newLevel < LevelsConfig.MAX_LEVEL) {
            requiredExp = configManager.calculateLevelExp(newLevel);
            if (newExp >= requiredExp) {
                newLevel++;
                newExp -= requiredExp;
                levelUp = true;
            } else {
                break;
            }
        }
        
        // 更新角色数据
        character.setExp(newExp);
        if (levelUp) {
            character.setLevel(newLevel);
            character.setMaxExp(requiredExp);
            // 重新计算持久化等级属性
            JSONObject newAttributes = calculatePlayerAttributes(character);
            character.setAttributes(newAttributes.toJSONString());
            
            // 更新血量法力值上限
            character.setHp(newAttributes.getInteger(GameAttributeConstant.HP));
            character.setMp(newAttributes.getInteger(GameAttributeConstant.MP));
            character.setMaxHp(newAttributes.getInteger(GameAttributeConstant.HP));
            character.setMaxMp(newAttributes.getInteger(GameAttributeConstant.MP));
            
            log.info("角色升级: characterId={}, newLevel={}", characterId, newLevel);
        }
        
        // 通过缓存管理器更新
        characterCacheManager.updateCharacter(character);
        return levelUp;
    }

    /**
     * 验证创建角色参数
     */
    private void validateCreateCharacterParams(String userId, Integer appId, String openId, Long areaId, String name, Integer roleType) {
        if (userId == null || userId.trim().isEmpty()) {
            throw new GameException(GameException.USER_ID_REQUIRED);
        }
        if (appId == null) {
            throw new GameException(GameException.APP_ID_REQUIRED);
        }
        if (openId == null || openId.trim().isEmpty()) {
            throw new GameException(GameException.OPEN_ID_REQUIRED);
        }
        if (areaId == null) {
            throw new GameException(GameException.AREA_ID_REQUIRED);
        }
        if (name == null || name.trim().isEmpty()) {
            throw new GameException(GameException.CHARACTER_NAME_REQUIRED);
        }
        if (name.length() > 10) {
            throw new GameException(GameException.CHARACTER_NAME_TOO_LONG);
        }
        if (roleType == null || roleType < 1 || roleType > 3) {
            throw new GameException(GameException.INVALID_ROLE_TYPE);
        }
    }

    /**
     * 应用新手保护属性
     */
    private void applyNewbieProtection(JSONObject attributes) {
        // 从配置中获取新手保护数值
        NewbieConfig newbieConfig = configManager.getNewbieConfig();
        if (newbieConfig == null) {
            log.warn("新手配置为空，使用默认新手保护数值");
            applyDefaultNewbieProtection(attributes);
            return;
        }

        // 获取新手保护配置
        NewbieConfig.Protection protection = newbieConfig.getProtection();
        if (protection == null) {
            log.warn("新手保护配置为空，使用默认数值");
            applyDefaultNewbieProtection(attributes);
            return;
        }

        // 获取属性加成配置
        NewbieConfig.AttributeBonus attributeBonus = protection.getAttributeBonus();
        if (attributeBonus == null) {
            log.warn("新手属性加成配置为空，使用默认数值");
            applyDefaultNewbieProtection(attributes);
            return;
        }

        // 应用防御加成
        if (attributeBonus.getDefenseW() != null) {
            attributes.put(GameAttributeConstant.PHY_DEF,
                attributes.getInteger(GameAttributeConstant.PHY_DEF) + attributeBonus.getDefenseW());
        }
        if (attributeBonus.getDefenseM() != null) {
            attributes.put(GameAttributeConstant.MAG_DEF,
                attributes.getInteger(GameAttributeConstant.MAG_DEF) + attributeBonus.getDefenseM());
        }
        if (attributeBonus.getDefenseF() != null) {
            attributes.put(GameAttributeConstant.BUD_DEF,
                attributes.getInteger(GameAttributeConstant.BUD_DEF) + attributeBonus.getDefenseF());
        }

        // 应用攻击加成
        if (attributeBonus.getAttackW() != null) {
            attributes.put(GameAttributeConstant.PHY_ATK,
                attributes.getInteger(GameAttributeConstant.PHY_ATK) + attributeBonus.getAttackW());
        }
        if (attributeBonus.getAttackM() != null) {
            attributes.put(GameAttributeConstant.MAG_ATK,
                attributes.getInteger(GameAttributeConstant.MAG_ATK) + attributeBonus.getAttackM());
        }
        if (attributeBonus.getAttackF() != null) {
            attributes.put(GameAttributeConstant.BUD_ATK,
                attributes.getInteger(GameAttributeConstant.BUD_ATK) + attributeBonus.getAttackF());
        }

        log.debug("应用新手保护: 物防+{}, 法防+{}, 佛防+{}, 物攻+{}, 法攻+{}, 佛攻+{}",
                attributeBonus.getDefenseW(), attributeBonus.getDefenseM(), attributeBonus.getDefenseF(),
                attributeBonus.getAttackW(), attributeBonus.getAttackM(), attributeBonus.getAttackF());
    }

    /**
     * 应用默认新手保护（配置获取失败时的兜底方案）
     */
    private void applyDefaultNewbieProtection(JSONObject attributes) {
        // 默认新手保护：三防增加100，攻击增加10
        attributes.put(GameAttributeConstant.PHY_DEF,
            attributes.getInteger(GameAttributeConstant.PHY_DEF) + 100);
        attributes.put(GameAttributeConstant.MAG_DEF,
            attributes.getInteger(GameAttributeConstant.MAG_DEF) + 100);
        attributes.put(GameAttributeConstant.BUD_DEF,
            attributes.getInteger(GameAttributeConstant.BUD_DEF) + 100);

        attributes.put(GameAttributeConstant.PHY_ATK,
            attributes.getInteger(GameAttributeConstant.PHY_ATK) + 10);
        attributes.put(GameAttributeConstant.MAG_ATK,
            attributes.getInteger(GameAttributeConstant.MAG_ATK) + 10);
        attributes.put(GameAttributeConstant.BUD_ATK,
            attributes.getInteger(GameAttributeConstant.BUD_ATK) + 10);

        log.debug("应用默认新手保护: 防御+100, 攻击+10");
    }

    /**
     * 应用装备属性加成
     */
    private void applyEquipmentBonus(JSONObject attributes, Long characterId) {
        try {
            // 通过AssetManager计算装备属性加成
            JSONObject equipmentBonus = assetManager.calculateEquipmentAttributes(characterId);

            if (equipmentBonus == null || equipmentBonus.isEmpty()) {
                log.debug("角色{}没有装备属性加成", characterId);
                return;
            }

            // 应用装备加成到角色属性
            for (String attributeName : equipmentBonus.keySet()) {
                Integer bonusValue = equipmentBonus.getInteger(attributeName);

                if (bonusValue != null && bonusValue > 0) {
                    // 获取当前属性值，如果没有则默认为0
                    int currentValue = attributes.getIntValue(attributeName);
                    // 加上装备加成
                    attributes.put(attributeName, currentValue + bonusValue);
                }
            }

            log.debug("应用装备属性加成: characterId={}, bonus={}", characterId, equipmentBonus);

        } catch (Exception e) {
            log.error("应用装备属性加成失败: characterId={}", characterId, e);
        }
    }

    /**
     * 创建初始用户资产
     */
    private void createInitialUserAsset(Long characterId, Integer roleType) {
        log.info("创建初始用户资产: characterId={}, roleType={}", characterId, roleType);

        UserCharacter character = getCharacterById(characterId);
        if (character == null) {
            return;
        }

        // 创建初始货币
        createInitialCurrency(character);

        // 创建初始装备
        createInitialEquipment(character, roleType);

        // 创建初始消耗品
        createInitialConsumables(character);
    }

    /**
     * 创建初始货币（从配置获取数值）
     */
    private void createInitialCurrency(UserCharacter character) {
        // 从配置获取初始货币数量
        NewbieConfig newbieConfig = configManager.getNewbieConfig();
        int initialSilver = NewbieConfig.INITIAL_SILVER; // 默认值
        int initialGold = NewbieConfig.INITIAL_GOLD; // 默认值

        if (newbieConfig != null && newbieConfig.getInitialResources() != null) {
            NewbieConfig.InitialResources resources = newbieConfig.getInitialResources();
            if (resources.getSilver() != null) {
                initialSilver = resources.getSilver();
            }
            if (resources.getGold() != null) {
                initialGold = resources.getGold();
            }
        }

        // 创建初始银两
        UserAsset silverAsset = new UserAsset();
        silverAsset.setCharacterId(character.getId());
        silverAsset.setItemNo(ItemConstant.ITEM_SILVER);
        silverAsset.setCount(initialSilver);
        silverAsset.setPosition(2); // 背包
        silverAsset.setAttributes("{}");
        userAssetMapper.insert(silverAsset);


        // 创建初始金币
        UserAsset goldAsset = new UserAsset();
        goldAsset.setCharacterId(character.getId());
        goldAsset.setItemNo(ItemConstant.ITEM_GOLD);
        goldAsset.setCount(initialGold);
        goldAsset.setPosition(2); // 背包
        goldAsset.setAttributes("{}");

        userAssetMapper.insert(goldAsset);


        log.debug("创建初始货币: 银两={}, 金币={}", initialSilver, initialGold);
    }

    /**
     * 创建初始装备（从配置获取武器信息）
     */
    private void createInitialEquipment(UserCharacter character, Integer roleType) {
        // 从配置获取初始武器
        String weaponItemNo = getInitialWeaponFromConfig(roleType);

        // 创建新手武器
        UserAsset weaponAsset = new UserAsset();
        weaponAsset.setCharacterId(character.getId());
        weaponAsset.setItemNo(weaponItemNo);
        weaponAsset.setCount(1);
        weaponAsset.setPosition(2); // 背包
        weaponAsset.setAttributes("{\"degree\":1}"); // 1品装备

        userAssetMapper.insert(weaponAsset);

        log.debug("创建初始武器: roleType={}, weaponItemNo={}", roleType, weaponItemNo);
    }

    /**
     * 从配置获取初始武器
     */
    private String getInitialWeaponFromConfig(Integer roleType) {
        NewbieConfig newbieConfig = configManager.getNewbieConfig();

        // 尝试从配置获取
        if (newbieConfig != null && newbieConfig.getInitialWeapons() != null) {
            NewbieConfig.InitialWeapon weapon = newbieConfig.getInitialWeapons().get(roleType.toString());
            if (weapon != null && weapon.getItemNo() != null) {
                return weapon.getItemNo();
            }
        }

        // 配置获取失败，使用常量兜底
        switch (roleType) {
            case 1: // 剑客
                return ItemConstant.WEAPON_SWORDSMAN; // 青锋剑
            case 2: // 仙师
                return ItemConstant.WEAPON_MAGE; // 湖光杖
            case 3: // 圣僧
                return ItemConstant.WEAPON_MONK; // 印月禅杖
            default:
                return ItemConstant.WEAPON_SWORDSMAN; // 默认青锋剑
        }
    }

    /**
     * 创建初始消耗品（使用配置中的物品编号）
     */
    private void createInitialConsumables(UserCharacter character) {
        // 创建初始回气丹（使用配置中的物品编号）
        UserAsset hpPotionAsset = new UserAsset();
        hpPotionAsset.setCharacterId(character.getId());
        hpPotionAsset.setItemNo(ItemConstant.ITEM_HP_POTION); // 使用配置常量
        hpPotionAsset.setCount(10); // 10个回气丹
        hpPotionAsset.setPosition(2); // 背包
        hpPotionAsset.setAttributes("{}");

        userAssetMapper.insert(hpPotionAsset);

        // 创建初始回法丹（使用配置中的物品编号）
        UserAsset mpPotionAsset = new UserAsset();
        mpPotionAsset.setCharacterId(character.getId());
        mpPotionAsset.setItemNo(ItemConstant.ITEM_MP_POTION); // 使用配置常量
        mpPotionAsset.setCount(10); // 10个回法丹
        mpPotionAsset.setPosition(2); // 背包
        mpPotionAsset.setAttributes("{}");

        userAssetMapper.insert(mpPotionAsset);

        log.debug("创建初始消耗品: 回气丹x10, 回法丹x10");
    }

    /**
     * 使用物品：穿上装备、使用药品、学习技能
     */
    @Transactional
    public String useItem(Long characterId, Long assetId) {
        log.info("使用物品: characterId={}, assetId={}", characterId, assetId);

        // 1. 验证角色和物品
        UserCharacter character = getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        UserAsset asset = userAssetMapper.selectById(assetId);
        if (asset == null) {
            throw new GameException(GameException.ITEM_NOT_FOUND);
        }

        // 验证物品归属
        if (asset.getCharacterId() != characterId.longValue()) {
            throw new GameException(GameException.ITEM_NOT_BELONGS_TO_CHARACTER);
        }

        // 2. 获取物品信息
        Item item = itemMapper.selectByItemNo(asset.getItemNo());
        if (item == null) {
            throw new GameException(GameException.ITEM_NOT_FOUND);
        }

        // 3. 根据物品类型进行不同处理
        switch (item.getType()) {
            case 0: // 装备
                return assetManager.equipEquipment(character, asset);
            case 1: // 药品
                return usePotion(character, asset, item);
            case 2: // 技能书
                return useSkillBook(character, asset, item);
            default:
                return "该物品无法使用";
        }
    }

    /**
     * 更新角色属性（装备变化后）
     */
    private void updateCharacterAttributes(UserCharacter character) {
        JSONObject newAttributes = calculatePlayerAttributes(character);
        character.setAttributes(newAttributes.toJSONString());
        characterCacheManager.updateCharacter(character);
    }

    /**
     * 使用药品：恢复血量或法力值
     */
    private String usePotion(UserCharacter character, UserAsset asset, Item item) {
        try {
            // 检查等级限制
            if (item.getLevelLimit() != null && character.getLevel() < item.getLevelLimit()) {
                return String.format("需要%d级才能使用该药品", item.getLevelLimit());
            }

            // 解析药品效果
            JSONObject itemAttributes = JSON.parseObject(item.getAttributes());
            if (itemAttributes == null || itemAttributes.isEmpty()) {
                return "药品效果配置错误";
            }

            int currentHp = character.getHp();
            int currentMp = character.getMp();

            StringBuilder result = new StringBuilder();
            result.append(String.format("使用了 %s", item.getName()));

            // 处理血量恢复
            if (itemAttributes.containsKey("HP")) {
                int hpRestore = itemAttributes.getIntValue("HP");
                int newHp = Math.min(character.getMaxHp(), currentHp + hpRestore);
                int actualRestore = newHp - currentHp;
                currentHp = newHp;
                result.append(String.format("，恢复了%d点血量", actualRestore));
            }

            // 处理法力值恢复
            if (itemAttributes.containsKey("MP")) {
                int mpRestore = itemAttributes.getIntValue("MP");
                int newMp = Math.min(character.getMaxMp(), currentMp + mpRestore);
                int actualRestore = newMp - currentMp;
                currentMp = newMp;
                result.append(String.format("，恢复了%d点法力值", actualRestore));
            }

            // 处理复活效果
            if (itemAttributes.containsKey("life")) {
                if (character.getHp() <= 0) {
                    currentHp = character.getMaxHp() / 2; // 复活后50%血量
                    currentMp = character.getMaxMp() / 2; // 复活后50%法力
                    result.append("，✨ 起死回生！");
                } else {
                    result.append("，🤷 大侠尚未死亡，无需复活");
                }
            }

            // 更新角色血量法力值
            updateCharacterHpMp(character, currentHp, currentMp);

            // 消耗药品
            if (asset.getCount() > 1) {
                asset.setCount(asset.getCount() - 1);
                userAssetMapper.updateById(asset);
            } else {
                userAssetMapper.deleteById(asset.getId());
            }

            result.append(String.format("！当前血量：%d/%d，法力：%d/%d", currentHp, character.getMaxHp(), currentMp, character.getMaxMp()));
            return result.toString();

        } catch (Exception e) {
            log.error("使用药品失败: characterId={}, assetId={}", character.getId(), asset.getId(), e);
            return "药品使用失败：" + e.getMessage();
        }
    }

    /**
     * 使用技能书：学习技能
     */
    private String useSkillBook(UserCharacter character, UserAsset asset, Item item) {
        try {
            // 检查职业限制
            if (item.getRoleLimit() != null && !item.getRoleLimit().equals(character.getType())) {
                return "该技能书不适合你的职业";
            }

            // 检查等级限制
            if (item.getLevelLimit() != null && character.getLevel() < item.getLevelLimit()) {
                return String.format("需要%d级才能学习该技能", item.getLevelLimit());
            }

            // 获取技能NO（从物品编号中提取）
            String skillNo = Helper.extractSkillNoFromItem(item.getItemNo());
            if (skillNo == null) {
                return "技能书配置错误";
            }

            // 检查是否已经学会该技能
            if (hasLearnedSkill(character, skillNo)) {
                return "你已经学会了这个技能";
            }

            // 学习技能
            learnSkill(character, skillNo);

            // 消耗技能书
            if (asset.getCount() > 1) {
                asset.setCount(asset.getCount() - 1);
                userAssetMapper.updateById(asset);
            } else {
                userAssetMapper.deleteById(asset.getId());
            }

            return String.format("成功学会了 %s！", item.getName());

        } catch (Exception e) {
            log.error("使用技能书失败: characterId={}, assetId={}", character.getId(), asset.getId(), e);
            return "技能书使用失败：" + e.getMessage();
        }
    }

    /**
     * 学习技能
     */
    private void learnSkill(UserCharacter character, String skillId) {
        JSONArray skills;

        if (character.getSkills() == null || character.getSkills().isEmpty()) {
            skills = new JSONArray();
        } else {
            try {
                skills = JSON.parseArray(character.getSkills());
            } catch (Exception e) {
                log.warn("解析角色技能失败，重新创建: characterId={}", character.getId(), e);
                skills = new JSONArray();
            }
        }
        // 添加新技能
        skills.add(skillId);

        // 更新角色技能
        character.setSkills(skills.toJSONString());
        characterCacheManager.updateCharacter(character);

        log.info("角色学会新技能: characterId={}, skillId={}", character.getId(), skillId);
    }
    /**
     * 更新角色位置
     *
     * @param character 玩家角色
     * @param mapId 地图ID
     * @param x X坐标
     * @param y Y坐标
     */
    private void updateCharacterPosition(UserCharacter character, String mapId, Integer x, Integer y) {
        log.info("更新角色位置: character={}, mapId={}, x={}, y={}", character.getName(), mapId, x, y);

        // 更新位置信息
        Helper.setCharacterPosition(character, mapId, x, y);

        // 保存更新（通过缓存管理器）
        characterCacheManager.updateCharacter(character);
    }

    /**
     * 更新角色血量和法力值
     */
    public void updateCharacterHpMp(UserCharacter character, Integer hp, Integer mp) {
        log.info("更新角色血量法力: characterId={}, hp={}, mp={}", character.getName(), hp, mp);

        // 限制在最大值范围内
        hp = Math.min(Math.max(hp, 0), character.getMaxHp());
        mp = Math.min(Math.max(mp, 0), character.getMaxMp());
        int oldHp = character.getHp();
        character.setHp(hp);
        character.setMp(mp);

        //人物死亡
        if(oldHp>0 && hp==0){
            character.setStatus(UserCharacter.STATUS_DEAD);
        }
        //人物复活
        if(oldHp==0 && hp>0){
            character.setStatus(UserCharacter.STATUS_NORMAL);
        }
        // 通过缓存管理器更新
        characterCacheManager.updateCharacter(character);
    }

    /**
     * 检查角色是否死亡
     */
    public boolean isCharacterDead(Long characterId) {
        UserCharacter character = getCharacterById(characterId);
        return character != null && character.getHp() <= 0;
    }

    /**
     * 复活角色
     * 恢复50%血量法力值，并传送回武林主城
     */
    @Transactional
    public String reviveCharacter(Long characterId) {
        log.info("复活角色: characterId={}", characterId);

        UserCharacter character = getCharacterById(characterId);
        if (character == null) {
            throw new GameException(GameException.CHARACTER_NOT_EXISTS);
        }

        if (character.getHp() > 0) {
            throw new GameException(GameException.CHARACTER_NOT_DEAD);
        }

        character.setHp(character.getMaxHp() / 2);
        character.setMp(character.getMaxMp() / 2);
        Map<String, Object> position = Helper.getCharacterPosition(character);
        String oldMapId = position.get("mapId").toString();

        // 复活后传送回武林主城（复活点坐标）
        mapManager.characterMove(character, oldMapId, MapsConfig.MAP_WULIN, 50, 50, true);

        //正常状态
        character.setStatus(UserCharacter.STATUS_NORMAL);

        // 通过缓存管理器更新
        characterCacheManager.updateCharacter(character);

        log.info("角色复活完成: characterId={}, 传送至武林主城", characterId);

        return "✨ 20年后又是一条好汉，大侠重获新生！\n 🗺️ 当前位置：武林主城";
    }

    // ==================== 缓存生命周期管理 ====================

    /**
     * 角色上线时预加载缓存
     */
    public void onCharacterLogin(Long characterId) {
        if (characterId != null) {
            characterCacheManager.preloadCharacterToCache(characterId);
            log.info("角色上线: characterId={}", characterId);
        }
    }

    /**
     * 角色下线时写回数据
     */
    public void onCharacterLogout(Long characterId) {
        if (characterId != null) {
            characterCacheManager.onCharacterOffline(characterId);
            log.info("角色下线: characterId={}", characterId);
        }
    }

    /**
     * 获取缓存统计信息（管理接口）
     */
    public Map<String, Object> getCacheStats() {
        return characterCacheManager.getCacheStats();
    }

    /**
     * 强制刷新缓存数据（管理接口）
     */
    public void forceFlushCache() {
        characterCacheManager.forceFlushAll();
    }
}
