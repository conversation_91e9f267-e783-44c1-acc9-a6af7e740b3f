package com.xiziworld.gameserver.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    SUCCESS("200", "操作成功"),
    ERROR("500", "操作失败"),
    VALIDATE_FAILED("400", "参数检验失败"),
    UNAUTHORIZED("401", "暂未登录或token已经过期"),
    FORBIDDEN("403", "没有相关权限"),
    NOT_FOUND("404", "资源不存在"),

    // 用户相关: 1000xx
    USER_NOT_EXIST("100001", "用户不存在"),
    USER_LOGIN_ERROR("100002", "账号不存在或密码错误"),
    USER_ACCOUNT_FORBIDDEN("100003", "账号已被禁用"),
    USER_NOT_LOGIN("100004", "用户未登录"),
    USER_NO_AUTH("100005", "用户无权限"),

    // 业务异常: 2000xx
    NO_PERMISSION("200001", "无权限操作"),
    INVALID_PARAM("200002", "参数错误"),
    BUSINESS_ERROR("200003", "业务异常"),

    // 系统异常: 3000xx
    SYSTEM_ERROR("300001", "系统异常"),
    NETWORK_ERROR("300002", "网络异常"),
    DB_ERROR("300003", "数据库异常"),
    FILE_ERROR("300004", "文件异常");

    private final String code;
    private final String message;
} 