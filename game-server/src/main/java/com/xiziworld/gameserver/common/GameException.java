package com.xiziworld.gameserver.common;

import lombok.Getter;

/**
 * 游戏业务异常
 * 统一管理游戏风格的异常消息
 *
 * <AUTHOR>
 */
@Getter
public class GameException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private final String code;

    public GameException(String message) {
        super(message);
        this.code = ResultCode.BUSINESS_ERROR.getCode();
    }

    public GameException(String code, String message) {
        super(message);
        this.code = code;
    }

    public GameException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
    }

    public GameException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
    }

    public GameException(ResultCode resultCode, Throwable cause) {
        super(resultCode.getMessage(), cause);
        this.code = resultCode.getCode();
    }

    public GameException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResultCode.BUSINESS_ERROR.getCode();
    }
    
    // ==================== 角色相关异常消息 ====================
    public static final String CHARACTER_NOT_EXISTS = "🤔 大侠还未在江湖中立名，请先创建角色！\n💡 发送 '加入 角色名' 开始你的江湖之路";
    public static final String CHARACTER_ALREADY_EXISTS = "🎭 大侠已经在江湖中闯荡了，无需重复创建角色！\n💡 发送 '状态' 查看角色信息";
    public static final String CHARACTER_NAME_EXISTS = "📝 江湖中已有同名大侠，请换个响亮的名号！\n💡 尝试不同的角色名称";
    public static final String CHARACTER_DEAD = "💀 大侠已魂归西天，请先复活再继续闯荡江湖！\n💡 发送 '复活' 重获新生";
    public static final String CHARACTER_LEVEL_NOT_ENOUGH = "📈 大侠功力尚浅，等级不够！\n💡 多多历练，提升等级后再来尝试";
    public static final String CHARACTER_NAME_REQUIRED = "🎭 大侠闯荡江湖，岂能无名？\n💡 请提供角色名称";

    // ==================== 物品和装备相关异常消息 ====================
    public static final String ITEM_NOT_FOUND = "🔍 大侠要找的宝物不见踪影！\n💡 检查物品名称是否正确";
    public static final String ITEM_NOT_EQUIPMENT = "⚔️ 此物并非装备，无法穿戴！\n💡 只有武器防具才能装备";
    public static final String ITEM_CANNOT_USE = "🚫 此物暂时无法使用！\n💡 检查物品类型和使用条件";
    public static final String INVENTORY_FULL = "🎒 大侠的行囊已经装不下更多宝物了！\n💡 发送 '存+物品名' 将物品存入仓库";
    public static final String INVENTORY_ITEM_NOT_FOUND = "🎒 背包中没有找到此物品！\n💡 发送 '背包' 查看所有物品";
    public static final String STORAGE_ITEM_NOT_FOUND = "📦 仓库中没有找到此物品！\n💡 发送 '仓库' 查看存储物品";
    public static final String EQUIPMENT_POSITION_ERROR = "🎯 装备位置信息有误！\n💡 请检查装备类型";
    public static final String EQUIPMENT_ROLE_LIMIT = "🚫 此装备不适合大侠的门派！\n💡 选择适合职业的装备";
    public static final String EQUIPMENT_LEVEL_LIMIT = "📊 大侠等级不足，无法使用此装备！\n💡 提升等级后再来尝试";

    // ==================== 货币相关异常消息 ====================
    public static final String SILVER_NOT_ENOUGH = "💰 大侠囊中羞涩，银两不够用了！\n💡 通过战斗、采集或交易来获得更多银两";
    public static final String GOLD_NOT_ENOUGH = "🏆 大侠的金币不足！\n💡 通过兑换银两或完成任务获得金币";
    public static final String EXCHANGE_AMOUNT_TOO_SMALL = "💱 兑换数量太少，不值得跑腿！\n💡 增加兑换数量";

    // ==================== 战斗相关异常消息 ====================
    public static final String TARGET_NOT_FOUND = "🎯 找不到指定的目标！\n💡 发送 '查看地图' 刷新目标列表";
    public static final String TARGET_TOO_FAR = "📏 大侠距离目标太远，鞭长莫及！\n💡 发送 '前往+目标编号' 靠近目标";
    public static final String ATTACK_COOLDOWN = "⏰ 大侠动作太快了，请稍作休息！\n💡 等待攻击冷却时间结束";
    public static final String SKILL_COOLDOWN = "🔮 技能施展需要时间恢复内力！\n💡 等待技能冷却时间结束";
    public static final String SKILL_NOT_LEARNED = "📚 大侠尚未学会此技能！\n💡 通过技能书学习或找师父传授";
    public static final String IN_BATTLE = "⚔️ 大侠正在激战中，无法进行其他操作！\n💡 先结束当前战斗";
    public static final String NOT_IN_BATTLE = "🕊️ 大侠当前并未在战斗中！\n💡 先找个对手切磋一下";

    // ==================== 地图和移动相关异常消息 ====================
    public static final String MAP_NOT_FOUND = "🗺️ 找不到指定的地图！\n💡 检查地点名称是否正确";
    public static final String MAP_NOT_CONNECTED = "🚧 此路不通，无法到达目的地！\n💡 寻找其他路径或传送点";
    public static final String POSITION_INVALID = "📍 位置信息有误！\n💡 请检查坐标是否正确";
    public static final String SAFE_ZONE_RESTRICTION = "🛡️ 安全区内不可进行此操作！\n💡 前往其他区域尝试";

    // ==================== 交易相关异常消息 ====================
    public static final String TRADE_IN_PROGRESS = "🤝 大侠正在进行其他交易！\n💡 完成当前交易后再试";
    public static final String TRADE_NOT_FOUND = "📋 找不到指定的交易！\n💡 交易可能已过期或被取消";
    public static final String TRADE_EXPIRED = "⏰ 交易已过期！\n💡 重新发起交易请求";
    public static final String NPC_NOT_FOUND = "👤 找不到指定的NPC！\n💡 检查NPC名称或编号";
    public static final String SHOP_ITEM_NOT_FOUND = "🏪 商店中没有此商品！\n💡 发送 '询问+NPC' 查看商品列表";

    // ==================== 采集相关异常消息 ====================
    public static final String COLLECT_COOLDOWN = "⏰ 采集需要耐心，请稍作休息！\n💡 等待冷却时间结束";
    public static final String COLLECT_LOCATION_INVALID = "🌍 此地无法进行该采集活动！\n💡 前往合适的地点尝试";
    public static final String COLLECT_IN_PROGRESS = "🔄 大侠正在进行其他采集活动！\n💡 完成当前活动后再试";
    public static final String FISHING_LOCATION_INVALID = "🎣 当前地图无法钓鱼，请前往有水的地方！\n💡 寻找湖泊、河流等水域";
    public static final String FISHING_TOOL_REQUIRED = "🎣 你没有钓鱼工具，请先购买鱼竿！\n💡 前往商店购买钓鱼装备";

    // ==================== 任务相关异常消息 ====================
    public static final String QUEST_NOT_FOUND = "📜 找不到指定的任务！\n💡 检查任务名称是否正确";
    public static final String QUEST_ALREADY_ACCEPTED = "✅ 大侠已经接取了此任务！\n💡 发送 '任务' 查看进度";
    public static final String QUEST_NOT_COMPLETED = "📋 任务尚未完成！\n💡 继续努力完成任务目标";
    public static final String QUEST_LIMIT_REACHED = "📚 大侠同时进行的任务太多了！\n💡 完成一些任务后再接新的";

    // ==================== 系统相关异常消息 ====================
    public static final String SYSTEM_ERROR = "⚡ 江湖风云突变，系统出现了一些问题！\n🙏 请大侠稍后再试，或联系客服小二\n💫 您的数据都很安全，请放心！";
    public static final String PERMISSION_DENIED = "🚫 大侠权限不足，无法进行此操作！\n💡 联系管理员获取权限";
    public static final String RATE_LIMIT_EXCEEDED = "🌪️ 大侠手脚太快了，请稍作休息！\n💡 等待一会儿再继续操作";
    public static final String INVALID_PARAMETER = "📝 参数格式有误！\n💡 检查命令格式是否正确";
    public static final String UNKNOWN_COMMAND = "🤷‍♂️ 大侠说的话小的听不懂呢...\n💡 发送 '!帮助' 查看可用命令\n🎯 或者试试这些常用命令：状态、地图、背包";

    // ==================== 游戏状态相关异常消息 ====================
    public static final String ALREADY_IN_GAME = "🎮 大侠已经在游戏中了！\n💡 发送 '状态' 查看当前信息";
    public static final String NOT_IN_GAME = "🚪 大侠当前不在游戏中！\n💡 发送 '!游戏模式' 进入游戏";
    public static final String GAME_MODE_SWITCH_FAILED = "🔄 游戏模式切换失败！\n💡 请稍后重试";

    // ==================== 参数验证相关异常消息 ====================
    public static final String USER_ID_REQUIRED = "🆔 用户ID不能为空！\n💡 请检查登录状态";
    public static final String APP_ID_REQUIRED = "📱 应用ID不能为空！\n💡 请检查应用配置";
    public static final String OPEN_ID_REQUIRED = "🔑 OpenID不能为空！\n💡 请检查授权状态";
    public static final String AREA_ID_REQUIRED = "🌍 区服ID不能为空！\n💡 请选择游戏区服";
    public static final String CHARACTER_NAME_TOO_LONG = "📏 角色名太长了！\n💡 角色名不能超过10个字符";
    public static final String INVALID_ROLE_TYPE = "🎭 无效的职业类型！\n💡 职业类型必须为1(剑客)、2(仙师)或3(圣僧)";
    public static final String TARGET_REQUIRED = "🎯 请指定目标！\n💡 命令格式：命令+目标";
    public static final String AT_TARGET_REQUIRED = "👥 请使用@指定目标！\n💡 在消息中@目标玩家";

    // ==================== 战斗状态相关异常消息 ====================
    public static final String CHARACTER_ALREADY_DEAD = "💀 大侠已魂归西天，无法进行此操作！\n💡 发送 '复活' 重获新生";
    public static final String SKILL_NOT_EXISTS = "📚 技能不存在！\n💡 检查技能名称是否正确";
    public static final String SKILL_NOT_LEARNED_BY_CHARACTER = "📖 大侠尚未学会此技能！\n💡 通过技能书学习或找师父传授";

    // ==================== 装备和物品相关异常消息 ====================
    public static final String ASSET_NOT_EXISTS = "🔍 装备不存在或不属于该角色！\n💡 检查装备是否在背包中";
    public static final String NOT_EQUIPMENT = "⚔️ 该物品不是装备！\n💡 只有武器防具才能进行此操作";
    public static final String NOT_JADE = "💎 该物品不是玉佩！\n💡 只有玉佩才能进行浣灵";
    public static final String MAX_QUALITY_REACHED = "⭐ 该装备已达到最高品质！\n💡 无法继续升品";
    public static final String UPGRADE_CONFIG_ERROR = "⚙️ 升品配置错误！\n💡 请联系管理员检查配置";
    public static final String INVENTORY_SPACE_INSUFFICIENT = "🎒 背包已满，无法添加物品！\n💡 整理背包或存储物品到仓库";

    // ==================== 地图相关异常消息 ====================
    public static final String MAP_NOT_EXISTS = "🗺️ 地图不存在！\n💡 检查地点名称是否正确";

    // ==================== 管理员相关异常消息 ====================
    public static final String AREA_NAME_EXISTS = "🌍 区服名称已存在！\n💡 请选择其他区服名称";
    public static final String AREA_ALREADY_CREATED = "🏗️ 区服已建立！\n💡 当前区服名为：";

    // ==================== 数量和参数相关异常消息 ====================
    public static final String QUANTITY_MUST_POSITIVE = "🔢 数量必须大于0！\n💡 请输入有效的数量";
    public static final String LEVEL_NOT_ENOUGH_FOR_TRADE = "📊 等级不足，无法进行交易！\n💡 需要等级：";

    // ==================== 角色状态相关异常消息 ====================
    public static final String CHARACTER_BANNED = "🔒 大侠被关进大牢了！\n💡 联系管理员了解详情";
    public static final String CHARACTER_VIEW_AT_REQUIRED = "👀 查看他人信息请使用@！\n💡 在消息中@目标玩家";

    // ==================== 配置相关异常消息 ====================
    public static final String CONFIG_LOAD_FAILED = "⚙️ 配置文件加载失败！\n💡 请联系管理员检查配置";
    public static final String COMMAND_CONFIG_LOAD_FAILED = "📋 命令配置加载失败！\n💡 请联系管理员检查配置";
    public static final String MAP_CONFIG_LOAD_FAILED = "🗺️ 地图配置加载失败！\n💡 请联系管理员检查配置";
    public static final String NPC_CONFIG_LOAD_FAILED = "👤 NPC配置加载失败！\n💡 请联系管理员检查配置";
    public static final String LEVEL_CONFIG_LOAD_FAILED = "📊 等级配置加载失败！\n💡 请联系管理员检查配置";
    public static final String SUIT_CONFIG_LOAD_FAILED = "👔 套装配置加载失败！\n💡 请联系管理员检查配置";

    // ==================== 命令验证相关异常消息 ====================
    public static final String CHARACTER_CREATE_REQUIRED = "🎭 请先创建角色！\n💡 发送 '加入 剑客' 开始游戏\n 角色有 剑客 仙师 圣僧 可选";
    public static final String MOVE_TARGET_REQUIRED = "🗺️ 请指定目标位置！\n💡 命令格式：前往+地点名";
    public static final String BATTLE_TARGET_REQUIRED = "⚔️ 请指定攻击目标！\n💡 命令格式：攻击+目标编号";
    public static final String OPERATION_ITEM_REQUIRED = "📦 请指定操作物品！\n💡 命令格式：命令+物品名";

    // ==================== 区服相关异常消息 ====================
    public static final String OPEN_ID_REQUIRED_FOR_AREA = "🔑 群聊ID不能为空！\n💡 请在群聊中使用游戏命令";
    public static final String AREA_NOT_CREATED = "🌍 区服未创建！\n💡 请联系管理员创建区服";
    public static final String AREA_NOT_OPEN = "🔒 区服未开启！\n💡 请联系管理员开启区服";

    // ==================== 装备相关异常消息 ====================
    public static final String EQUIPMENT_INVENTORY_ONLY = "🎒 只能穿戴背包中的装备！\n💡 请先将装备放入背包";
    public static final String EQUIPMENT_ROLE_MISMATCH = "🚫 该装备不适合你的职业！\n💡 选择适合的装备";

    // ==================== 物品归属相关异常消息 ====================
    public static final String ITEM_NOT_BELONGS_TO_CHARACTER = "🚫 该物品不属于你！\n💡 只能使用自己的物品";
    public static final String CHARACTER_NOT_DEAD = "💚 大侠生龙活虎，无需复活！\n💡 只有死亡后才能复活";

    // ==================== 战斗冷却和状态相关消息 ====================
    public static final String ATTACK_COOLDOWN_MESSAGE = "⏰ 大侠手脚太快了！\n💡 请先喘口气再上手";
    public static final String SAFE_ZONE_PVP_RESTRICTION = "🛡️ 该地有重兵把手！\n💡 前往其他区域斗殴";

    // ==================== 仓库相关异常消息 ====================
    public static final String ITEM_NOT_IN_INVENTORY = "🎒 只能存储背包中的物品！\n💡 请先将物品放入背包";
    public static final String ITEM_NOT_IN_STORAGE = "📦 该物品不在仓库中！\n💡 检查物品是否已存储";

    // ==================== 货币和交易相关异常消息 ====================
    public static final String CURRENCY_NOT_ENOUGH = "💰 货币不足，无法扣除！\n💡 通过战斗、采集或交易获得更多货币";
    public static final String EQUIPMENT_LEVEL_LIMIT_ERROR = "📊 等级不足，无法穿戴该装备！\n💡 需要等级：";
    public static final String EQUIPMENT_POSITION_INVALID = "🎯 装备位置信息错误！\n💡 请检查装备类型";
    public static final String INVENTORY_FULL_CANNOT_RETRIEVE = "🎒 背包已满，无法取出物品！\n💡 整理背包后再试";

}
