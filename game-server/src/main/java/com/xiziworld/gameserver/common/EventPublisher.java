package com.xiziworld.gameserver.common;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 事件发布器，用于异步处理
 *
 * <AUTHOR>
 */
@Component
public class EventPublisher {


    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public void publishEvent(int eventType, Object eventData) {
        GameEvent event = new GameEvent(eventType, eventData);
        eventPublisher.publishEvent(event);
    }
    /**
     * 发布角色位置更新事件
     */
    public void publishPositionEvent(long characterId, int x, int y, String mapId) {
        Position position = new Position(characterId, x, y, mapId);
        publishEvent(GameEvent.TYPE_CHARACTER_UPDATE_POSITION, position);
    }

    /**
     * 玩家位置信息
     */
    @Getter
    public class Position{
        private long characterId;
        private int x;
        private int y;
        private String mapId;
        public Position(long characterId, int x, int y, String mapId) {
            this.characterId = characterId;
            this.x = x;
            this.y = y;
            this.mapId = mapId;
        }
    }

    @Getter
    public class GameEvent {
        /**
         * 角色属性更新事件
         */
        public static final int TYPE_CHARACTER_UPDATE_ATTR = 1;
        /**
         * 角色位置更新事件
         */
        public static final int TYPE_CHARACTER_UPDATE_POSITION = 2;

        private Integer eventType;
        private Object eventData;

        public GameEvent(int eventType, Object eventData) {
            this.eventType = eventType;
            this.eventData = eventData;
        }

    }

}
