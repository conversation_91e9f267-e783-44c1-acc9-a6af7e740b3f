package com.xiziworld.gameserver.common.constant;

/**
 * 游戏常量
 *
 * <AUTHOR>
 */
public interface GameConstant {

    /**
     * 角色状态
     */
    interface CharacterStatus {
        /**
         * 正常
         */
        Integer NORMAL = 0;

        /**
         * 死亡
         */
        Integer DEAD = 1;
    }

    /**
     * 职业类型
     */
    interface RoleType {
        /**
         * 剑客
         */
        String SWORDSMAN = "swordsman";

        /**
         * 仙师
         */
        String MAGE = "mage";

        /**
         * 圣僧
         */
        String MONK = "monk";
    }

    interface Currency{
        /**
         * 金币
         */
        String GOLD = "gold";

        String SILVER = "silver";
    }
    /**
     * 物品类型
     */
    interface ItemType {
        /**
         * 装备
         */
        Integer EQUIPMENT = 0;

        /**
         * 药品
         */
        Integer DRUG = 1;

        /**
         * 技能书
         */
        Integer SKILL_BOOK = 2;

        /**
         * 材料
         */
        Integer MATERIAL = 3;

        /**
         * 神奇盒子
         */
        Integer MAGIC_BOX = 9;
    }

    /**
     * 物品位置
     */
    interface ItemPosition {
        /**
         * 身上
         */
        Integer EQUIP = 1;

        /**
         * 背包
         */
        Integer BAG = 2;

        /**
         * 仓库
         */
        Integer STORAGE = 3;
    }

    /**
     * 命令类型
     */
    interface CommandType {
        /**
         * 查看命令
         */
        Integer VIEW = 1;

        /**
         * 移动命令
         */
        Integer MOVE = 2;

        /**
         * 战斗命令
         */
        Integer BATTLE = 3;
        /**
         * 物品命令
         */
        Integer ITEM = 4;

        /**
         * 交易类命令
         */
        Integer TRADE = 5;

        /**
         * 挂机类命令
         */
        Integer HOLD = 6;

        /**
         * 管理类命令
         */
        Integer MANAGER = 99;

    }

    /**
     * 任务类型
     */
    interface QuestType {
        /**
         * 主线任务
         */
        Integer MAIN = 1;

        /**
         * 支线任务
         */
        Integer SIDE = 2;

        /**
         * 日常任务
         */
        Integer DAILY = 3;
    }

    /**
     * 任务状态
     */
    interface QuestStatus {
        /**
         * 进行中
         */
        Integer IN_PROGRESS = 0;

        /**
         * 已完成
         */
        Integer COMPLETED = 1;

        /**
         * 已失败
         */
        Integer FAILED = 2;
    }

    /**
     * 排行榜类型
     */
    interface RankType {
        /**
         * 等级排行
         */
        Integer LEVEL = 1;

        /**
         * 战力排行
         */
        Integer POWER = 2;

        /**
         * 财富排行
         */
        Integer WEALTH = 3;
    }

    /**
     * 充值状态
     */
    interface RechargeStatus {
        /**
         * 处理中
         */
        Integer PROCESSING = 0;

        /**
         * 成功
         */
        Integer SUCCESS = 1;

        /**
         * 失败
         */
        Integer FAILED = 2;
    }
} 