package com.xiziworld.gameserver.common.constant;

/**
 * 游戏属性常量定义，用于各种对象（用户、物品等）属性解析，保持属性定义字符串一致性
 * 基于技术设计/sql/item.sql中的属性定义
 */
public class GameAttributeConstant {

    // ==================== 攻击属性 ====================
    /**
     * 物理攻击
     */
    public static final String PHY_ATK = "phy_atk";

    /**
     * 法术攻击
     */
    public static final String MAG_ATK = "mag_atk";

    /**
     * 佛法攻击
     */
    public static final String BUD_ATK = "bud_atk";

    // ==================== 防御属性 ====================
    /**
     * 物理防御
     */
    public static final String PHY_DEF = "phy_def";

    /**
     * 法术防御
     */
    public static final String MAG_DEF = "mag_def";

    /**
     * 佛法防御
     */
    public static final String BUD_DEF = "bud_def";

    // ==================== 生命值和法力值 ====================
    /**
     * 生命值/血气
     */
    public static final String HP = "hp";

    /**
     * 法力值
     */
    public static final String MP = "mp";

    // ==================== 特殊属性 ====================
    /**
     * 反伤
     */
    public static final String REFLECT = "reflect";

    /**
     * 暴击率
     */
    public static final String CRIT = "crit";

    /**
     * 内力(影响回hp速度)
     */
    public static final String INNER = "inner";

    /**
     * 加成倍率（玉佩专用）
     */
    public static final String PLUS = "plus";

    /**
     *  品级（装备属性特有）
     */
    public static final String DEGREE = "degree";

    /**
     * 获取属性显示名称
     */
    public static String getDisplayName(String key) {
        switch (key) {
            case PHY_ATK: return "物攻";
            case MAG_ATK: return "法攻";
            case BUD_ATK: return "佛攻";
            case PHY_DEF: return "物防";
            case MAG_DEF: return "法防";
            case BUD_DEF: return "佛防";
            case HP: return "血量";
            case MP: return "法力";
            case REFLECT: return "反伤";
            case CRIT: return "暴击";
            case INNER: return "内力";
            default: return key;
        }
    }
}
