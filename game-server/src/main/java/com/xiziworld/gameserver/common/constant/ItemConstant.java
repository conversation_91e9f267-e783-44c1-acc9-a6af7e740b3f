package com.xiziworld.gameserver.common.constant;

public class ItemConstant {
    public static final String ITEM_BOX_01 = "ITEM_BOX_01";        // 初级神奇盒子
    public static final String ITEM_BOX_02 = "ITEM_BOX_02";        // 中级神奇盒子
    public static final String ITEM_BOX_03 = "ITEM_BOX_03";        // 高级神奇盒子
    public static final String ITEM_BOX_04 = "ITEM_BOX_04";        // 稀有神奇盒子
    public static final String ITEM_GOLD = "ITEM_GOLD";              // 金币
    public static final String ITEM_SILVER = "ITEM_SILVER";        // 银两

    public static final String WEAPON_SWORDSMAN = "EQ_YOUHU_WQ_01";  // 青锋剑
    public static final String WEAPON_MAGE = "EQ_HUGUANG_WQ_01";     // 湖光杖
    public static final String WEAPON_MONK = "EQ_YINYUE_WQ_01";      // 印月禅杖

    public static final String ITEM_HP_POTION = "ITEM_DRUG_HP_01";   // 回气丹
    public static final String ITEM_MP_POTION = "ITEM_DRUG_MP_01";   // 回法丹


    // 装备位置常量
    public static final int POSITION_WEAPON = 1;    // 武器
    public static final int POSITION_HEAD = 2;      // 头部
    public static final int POSITION_CLOTHES = 3;   // 衣服
    public static final int POSITION_HANDS = 4;     // 护手
    public static final int POSITION_KNEES = 5;     // 护膝
    public static final int POSITION_PANTS = 6;     // 裤子
    public static final int POSITION_SHOES = 7;     // 鞋子
    public static final int POSITION_JADE = 8;      // 玉佩

    /**
     * 获取装备位置名称
     */
    public static String getSlotName(Integer slot) {
        if (slot == null) return "未知";

        switch (slot) {
            case POSITION_WEAPON: return "武器";
            case POSITION_HEAD: return "头部";
            case POSITION_CLOTHES: return "衣服";
            case POSITION_HANDS: return "护手";
            case POSITION_KNEES: return "护膝";
            case POSITION_PANTS: return "裤子";
            case POSITION_SHOES: return "鞋子";
            case POSITION_JADE: return "玉佩";
            default: return "未知";
        }
    }
}
