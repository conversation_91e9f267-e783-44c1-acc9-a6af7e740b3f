package com.xiziworld.gameserver.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

/**
 * JSON工具类
 *
 * <AUTHOR>
 */
public class JsonUtils {

    /**
     * 对象转JSON字符串
     */
    public static String toJsonString(Object object) {
        return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue);
    }

    /**
     * JSON字符串转对象
     */
    public static <T> T parseObject(String text, Class<T> clazz) {
        return JSON.parseObject(text, clazz);
    }

    /**
     * 对象转JSONObject
     */
    public static JSONObject toJsonObject(Object object) {
        return (JSONObject) JSON.toJSON(object);
    }

    /**
     * 判断字符串是否为JSON格式
     */
    public static boolean isJsonObject(String text) {
        try {
            JSON.parseObject(text);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断字符串是否为JSON数组格式
     */
    public static boolean isJsonArray(String text) {
        try {
            JSON.parseArray(text);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
} 