package com.xiziworld.gameserver.util;

import org.apache.commons.lang3.RandomStringUtils;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

    /**
     * 生成指定长度的随机字符串
     */
    public static String randomString(int count) {
        return RandomStringUtils.randomAlphanumeric(count);
    }

    /**
     * 生成指定长度的随机数字字符串
     */
    public static String randomNumeric(int count) {
        return RandomStringUtils.randomNumeric(count);
    }

    /**
     * 生成指定长度的随机字母字符串
     */
    public static String randomAlphabetic(int count) {
        return RandomStringUtils.randomAlphabetic(count);
    }

    /**
     * 截取字符串，超出部分用...代替
     */
    public static String ellipsis(String str, int maxLength) {
        if (str == null) {
            return null;
        }
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength) + "...";
    }

    /**
     * 判断字符串是否为数字
     */
    public static boolean isNumeric(String str) {
        if (str == null || str.length() == 0) {
            return false;
        }
        return str.matches("-?\\d+(\\.\\d+)?");
    }
} 