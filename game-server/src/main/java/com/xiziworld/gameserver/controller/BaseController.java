package com.xiziworld.gameserver.controller;

import com.xiziworld.gameserver.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基础Controller
 *
 * <AUTHOR>
 */
public class BaseController {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 返回成功
     */
    protected <T> Result<T> success() {
        return Result.ok();
    }

    /**
     * 返回成功数据
     */
    protected <T> Result<T> success(T data) {
        return Result.ok(data);
    }

    /**
     * 返回失败
     */
    protected <T> Result<T> failed() {
        return Result.error();
    }

    /**
     * 返回失败消息
     */
    protected <T> Result<T> failed(String msg) {
        return Result.error(msg);
    }

    /**
     * 根据布尔值返回成功或失败
     */
    protected <T> Result<T> decide(boolean b) {
        return b ? success() : failed();
    }
} 