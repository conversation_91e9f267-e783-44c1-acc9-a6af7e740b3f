package com.xiziworld.gameserver.controller;

import com.alibaba.fastjson.JSONObject;
import com.xiziworld.gameserver.command.Command;
import com.xiziworld.gameserver.command.CommandProcessor;
import com.xiziworld.gameserver.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 命令处理Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/command")
public class CommandController extends BaseController {

    @Autowired
    private CommandProcessor commandProcessor;

    /**
     * 处理命令
     */
    @PostMapping("/execute")
    public Result<String> execute(@RequestBody Command command) {
        logger.debug("收到原始请求："+ JSONObject.toJSONString(command));
        String result = commandProcessor.processCommand(command);
        return success(result);
    }
} 