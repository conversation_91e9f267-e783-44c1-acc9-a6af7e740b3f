package com.xiziworld.gameserver.controller;

import com.xiziworld.gameserver.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/health")
public class HealthController extends BaseController {

    /**
     * 健康检查
     */
    @GetMapping
    public Result<String> check() {
        return success("OK");
    }

    /**
     * 获取版本信息
     */
    @GetMapping("/version")
    public Result<String> version() {
        return success("1.0.0");
    }
} 