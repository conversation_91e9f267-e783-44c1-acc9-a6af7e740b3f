package com.xiziworld.gameserver.controller;

import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.time.Instant;
import java.io.*;
import java.nio.file.*;
import java.util.Base64;

@RestController
@RequestMapping("/voice")
public class VoiceController {
    /**
     * 请求参数格式
     {
     "requestId": "uuid",
     "deviceId": "device001",
     "timestamp": 1640995200000,
     "audioData": "base64编码的音频数据",
     "audioFormat": {
     "sampleRate": 16000,
     "channels": 1,
     "encoding": "PCM_16BIT"
     },
     "extends": {
     // H5注入的业务参数
     }
     }
     顺应参数格式：

     {
     "requestId": "uuid",
     "success": true,
     "data": {
     "text": "识别的文本内容",
     "audioUrl": "http://server/audio/response.mp3", // 可选
     "audioBase64": "base64编码的音频", // 可选，与audioUrl二选一
     "businessData": {
     // 业务相关数据
     }
     },
     "error": {
     "code": "ERROR_CODE",
     "message": "错误描述"
     }
     }

     */

    @PostMapping("/receive")
    public Map<String, Object> receiveVoice(@RequestBody Map<String, Object> request) {
        // Mock响应数据
        Map<String, Object> response = new HashMap<>();

        try {
            // 获取请求参数
            String requestId = (String) request.get("requestId");
            String deviceId = (String) request.get("deviceId");
            String audioData = (String) request.get("audioData");

            // 验证必要参数
            if (requestId == null || deviceId == null || audioData == null) {
                return createErrorResponse(requestId, "INVALID_PARAMS", "缺少必要参数");
            }
            System.out.println("Received voice request: " + requestId);
            System.out.println("audioData: " + audioData);
            // 处理音频数据：解码Base64并保存为WAV文件
            String audioFilePath = processAudioData(audioData, requestId);

            // Mock语音识别结果
            String recognizedText = mockVoiceRecognition(audioData);

            // 构建成功响应
            response.put("requestId", requestId);
            response.put("success", true);

            Map<String, Object> data = new HashMap<>();
            data.put("text", recognizedText);
            data.put("audioUrl", "http://server/audio/response_" + requestId + ".mp3");
            data.put("savedAudioPath", audioFilePath); // 保存的音频文件路径

            // Mock业务数据
            data.put("processTime", System.currentTimeMillis() - (Long) request.getOrDefault("timestamp", System.currentTimeMillis()));
            data.put("confidence", 0.95);
            data.put("language", "zh-CN");
            data.put("code","0");
            response.put("data", data);

        } catch (Exception e) {
            return createErrorResponse((String) request.get("requestId"), "INTERNAL_ERROR", "服务器内部错误: " + e.getMessage());
        }

        return response;
    }

    /**
     * Mock语音识别逻辑
     */
    private String mockVoiceRecognition(String audioData) {
        // 模拟不同的识别结果
        String[] mockTexts = {
            "你好，我想查询天气",
            "播放音乐",
            "设置闹钟",
            "今天几号",
            "帮我搜索附近的餐厅",
            "打开导航",
            "发送消息给张三",
            "查看日程安排"
        };

        // 根据音频数据长度模拟不同结果
        int index = Math.abs(audioData.hashCode()) % mockTexts.length;
        return mockTexts[index];
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String requestId, String errorCode, String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        response.put("requestId", requestId);
        response.put("success", false);
        response.put("message", errorMessage);
        response.put("code", errorCode);

        return response;
    }
    /**
     * 处理音频数据：解码Base64并保存为WAV文件
     * @param audioDataBase64 Base64编码的PCM音频数据
     * @param requestId 请求ID，用于生成文件名
     * @return 保存的文件路径
     */
    private String processAudioData(String audioDataBase64, String requestId) {
        try {
            // 1. 解码Base64数据
            byte[] pcmData = Base64.getDecoder().decode(audioDataBase64);

            // 2. 创建WAV文件头
            byte[] wavHeader = createWavHeader(pcmData.length);

            // 3. 合并WAV头和PCM数据
            byte[] wavData = new byte[wavHeader.length + pcmData.length];
            System.arraycopy(wavHeader, 0, wavData, 0, wavHeader.length);
            System.arraycopy(pcmData, 0, wavData, wavHeader.length, pcmData.length);

            // 4. 创建保存目录
            String audioDir = "audio_files";
            Path audioDirPath = Paths.get(audioDir);
            if (!Files.exists(audioDirPath)) {
                Files.createDirectories(audioDirPath);
            }

            // 5. 生成文件名并保存
            String fileName = "audio_" + requestId + "_" + System.currentTimeMillis() + ".wav";
            String filePath = audioDir + File.separator + fileName;

            Files.write(Paths.get(filePath), wavData);

            System.out.println("音频文件已保存: " + filePath + " (大小: " + wavData.length + " bytes)");

            return filePath;

        } catch (Exception e) {
            System.err.println("处理音频数据失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 创建WAV文件头
     * 音频参数：16000Hz, 16-bit, 单声道
     * @param pcmDataLength PCM数据长度
     * @return WAV文件头字节数组
     */
    private byte[] createWavHeader(int pcmDataLength) {
        // WAV文件头总共44字节
        byte[] header = new byte[44];

        // 音频参数
        int sampleRate = 16000;    // 采样率
        int bitsPerSample = 16;    // 位深
        int channels = 1;          // 声道数
        int byteRate = sampleRate * channels * bitsPerSample / 8;  // 字节率
        int blockAlign = channels * bitsPerSample / 8;             // 块对齐

        // RIFF头 (12字节)
        header[0] = 'R'; header[1] = 'I'; header[2] = 'F'; header[3] = 'F';
        writeInt(header, 4, 36 + pcmDataLength);  // 文件大小 - 8
        header[8] = 'W'; header[9] = 'A'; header[10] = 'V'; header[11] = 'E';

        // fmt子块 (24字节)
        header[12] = 'f'; header[13] = 'm'; header[14] = 't'; header[15] = ' ';
        writeInt(header, 16, 16);           // fmt子块大小
        writeShort(header, 20, (short) 1); // 音频格式 (1 = PCM)
        writeShort(header, 22, (short) channels);     // 声道数
        writeInt(header, 24, sampleRate);            // 采样率
        writeInt(header, 28, byteRate);              // 字节率
        writeShort(header, 32, (short) blockAlign);  // 块对齐
        writeShort(header, 34, (short) bitsPerSample); // 位深

        // data子块头 (8字节)
        header[36] = 'd'; header[37] = 'a'; header[38] = 't'; header[39] = 'a';
        writeInt(header, 40, pcmDataLength);  // PCM数据大小

        return header;
    }

    /**
     * 将int值写入字节数组 (小端序)
     */
    private void writeInt(byte[] data, int offset, int value) {
        data[offset] = (byte) (value & 0xFF);
        data[offset + 1] = (byte) ((value >> 8) & 0xFF);
        data[offset + 2] = (byte) ((value >> 16) & 0xFF);
        data[offset + 3] = (byte) ((value >> 24) & 0xFF);
    }

    /**
     * 将short值写入字节数组 (小端序)
     */
    private void writeShort(byte[] data, int offset, short value) {
        data[offset] = (byte) (value & 0xFF);
        data[offset + 1] = (byte) ((value >> 8) & 0xFF);
    }
}
