package com.xiziworld.gameserver;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 游戏服务器启动类
 *
 * <AUTHOR>
 */
@EnableAsync
@EnableScheduling
@SpringBootApplication
@MapperScan("com.xiziworld.gameserver.domain.mapper")
public class GameServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(GameServerApplication.class, args);
    }
} 