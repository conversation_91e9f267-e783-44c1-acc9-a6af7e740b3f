---
description: 需求/环境和约束
globs: 
alwaysApply: false
---

# 项目介绍

- 西子江湖是一款基于社交聊天APP的文字RPG游戏。游戏以杭州西湖的历史文化为背景，融合了白蛇传、雷峰塔等传统故事元素，让玩家在充满江湖气息的西湖世界中体验升级、战斗、社交等丰富玩法。
- 玩法类似于MUD游戏，但是基本定位在聊天APP里面玩，比如微信群，但不排除后期开发专有客户端；
- 已经开发实现一个homebot工程与微信对接（获取微信群消息，发送微信群消息等功能，可发送含文字、图片等消息）


# 工作阶段划分

- 需求脑暴：由人工完成，已完成
- 需求设计：人工与Cursor结合，已完成
- 技术方案设计：人工与Cursor，已完成部分，和需求没有完全对齐
- 游戏服务端开发：未开始
- homebot与游戏服务器对接完成功能闭环：未开始
- 游戏测试：未开始


# 工作目录

- 需求设计/                      存放需求设计文档目录
  - 西子江湖需求设计.md           需求文档，一切以这个为准
  - 装备需求设计.md              游戏装备设计
- 技术设计/                      存放技术方案设计文档目录
   - 技术方案设计.md             技术方案设计文档，部分可能和需求有出入，内容也没有完善
   - sql/                       技术方案设计涉及的几个领域表的SQL初始化（物品、怪物、技能、任务）
   - progress.md                技术方案设计的相关任务列表和进度，未建立
- 开发过程/                      开发过程相关记录文档目录
   - task.md                    开发任务列表
   - progress.md                开发进度记录
   - code_desc.md               game-server工程代码模块功能详细描述
- 测试文档/                      存放测试方档的目录
- 产品文档/                      存放产品文档，如宣传图、维护配置手册、玩家使用手册等

- homebot/                      已完成微信对接的微信机器人工程代码，采用python实现
- game-server/                  游戏主程序工程，采用java实现

# 工作环境

- 数据库
  - 地址：127.0.0.0
  - 端口: 3306
  - 用户名：xiziworld
  - 密码：xiziworld

- 操作系统：windows 10，注意dos命令和文件路径


# 约束要求

- 所有的需求和功能严格按照需求设计里面的范围内容，但你可以提出你的专业的建议，由我确定是否采纳，否则严格按照需求范围
- 开发实现，95%以上基本按技术方案设计实现，里面有部分细节或者你觉得有问题的，可以调整思路，但必须让我知道
- 每次执行任务前，需要回顾上下文，了解task.md和progress.md
- 每次执行任务前，需要阅读本projectrules内容以及“西子江湖需求设计.md”
- 特别强调下，不要脱离需求！不要增加需求以外的功能！如果必要，先由我确认!
- 不需要我提醒，在必要时，及时更新开发过程中的progress.md开发进度文档和code_desc.md代码描述文档
- 一个一个模块开发，开发完成后需要我来确认
