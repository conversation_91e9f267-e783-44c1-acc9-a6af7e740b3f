# Manager层接口文档

本文档详细描述了游戏服务端所有Manager类对外提供的public接口，供CommandHandler开发时参考使用。

## 1. ConfigManager - 配置管理器

### 1.1 配置获取接口

```java
// 获取各类配置对象
public CommandConfig getCommandConfig()
public MapsConfig getMapsConfig()
public NpcsConfig getNpcsConfig()
public ShopsConfig getShopsConfig()
public DropsConfig getDropsConfig()
public CollectConfig getCollectConfig()
public TreasureConfig getTreasureConfig()
public MapRefreshConfig getMapRefreshConfig()
public LevelsConfig getLevelsConfig()
public NewbieConfig getNewbieConfig()
public BattleConfig getBattleConfig()
public UpgradeConfig getUpgradeConfig()
public TradeConfig getTradeConfig()
public SuitConfig getSuitConfig()
```

### 1.2 便捷计算接口

```java
// 等级相关计算
public int calculateLevelExp(int level)                    // 计算等级所需经验值
public String getTitleByLevel(int level)                   // 根据等级获取称号

// 职业属性获取
public LevelsConfig.BaseAttributes getBaseAttributesByRole(int roleId)  // 获取职业基础属性

// 新手保护检查
public boolean isInNewbieProtection(int level, long createTime)  // 检查是否在新手保护期

// 战斗计算
public double calculateRestraintDamage(String attackerRole, String defenderRole)  // 计算职业克制伤害倍率

// 交易计算
public int calculateTradeTax(int tradeValue)               // 计算交易税收
```

### 1.3 配置管理接口

```java
public void reloadConfigs()                                // 重载配置文件
public String getCacheStats()                              // 获取缓存统计信息
public void printExpRange(int startLevel, int endLevel)    // 打印经验值范围（调试用）
```

## 2. PlayerManager - 玩家管理器

### 2.1 角色创建和查询

```java
// 角色创建
public UserCharacter createCharacter(String userId, Integer appId, String openId, 
                                   Long areaId, String name, Integer roleType)

// 角色查询
public UserCharacter getCharacterByUser(String userId, Integer appId, String openId, Long areaId)
public UserCharacter getCharacterById(Long characterId)
public boolean isCharacterNameExists(Long areaId, String name)
```

### 2.2 属性计算系统

```java
// 属性计算
public JSONObject calculatePlayerAttributes(UserCharacter character)  // 计算角色持久化属性

// 详细信息获取
public Map<String, Object> getCharacterDetailInfo(Long characterId)   // 获取角色详细信息
public Map<String, Object> getCharacterBasicInfo(Long characterId)    // 获取角色基础信息
```

### 2.3 经验和升级系统

```java
public boolean addExperience(Long characterId, Long expGain)  // 增加经验值，返回是否升级
```

### 2.4 技能系统

```java
public boolean hasLearnedSkill(Long characterId, String skillNo)      // 检查是否已学会技能
public boolean hasLearnedSkill(UserCharacter character, String skillNo)
```

### 2.5 物品使用

```java
public String useItem(Long characterId, Long assetId)  // 使用物品（装备、药品、技能书）
```

### 2.6 血量法力值管理

```java
public void updateCharacterHpMp(Long characterId, Integer hp, Integer mp)  // 更新血量法力值
public boolean isCharacterDead(Long characterId)                          // 检查是否死亡
public void reviveCharacter(Long characterId)                             // 复活角色
```

### 2.7 缓存生命周期管理

```java
public void onCharacterLogin(Long characterId)     // 角色上线预加载缓存
public void onCharacterLogout(Long characterId)    // 角色下线写回数据
public Map<String, Object> getCacheStats()         // 获取缓存统计信息
public void forceFlushCache()                      // 强制刷新缓存数据
```

## 3. MapManager - 地图管理器

### 3.1 地图基础功能

```java
// 地图信息获取
public MapsConfig.MapDetail getMapDetail(String mapId)                    // 获取地图详细信息
public boolean isMapConnected(String fromMapId, String toMapId)          // 检查地图是否相连
public List<MapsConfig.Connection> getMapConnections(String mapId)       // 获取地图连接信息
```

### 3.2 移动系统

```java
// 移动相关
public String moveToMapByName(Long characterId, String mapName)          // 根据地图名称移动
public void characterMove(UserCharacter character, String oldMapId, String newMapId, int newX, int newY)
public String moveToTempObject(Long characterId, int tempId)             // 移动到临时编号位置

// 位置查询
public Map<String, Object> getCharacterPosition(Long characterId)        // 获取角色位置信息
public String getCurrentMapId(Long characterId)                          // 获取当前地图ID
public boolean isInSafeZone(Long characterId)                           // 检查是否在安全区
```

### 3.3 环境扫描系统

```java
public String scanEnvironment(UserCharacter character)  // 扫描当前地图环境
```

### 3.4 地图信息查询

```java
public String getMapDetailInfo(Long characterId)  // 获取地图详细信息（用于查看地图命令）
```

### 3.5 临时编号管理

```java
public TempObject getTempObject(long characterId, int tempId)  // 根据临时编号获取对象
```

### 3.6 地图刷新和管理

```java
public void refreshMapMonsters(String mapId)     // 刷新地图怪物
public void refreshAllMapMonsters()              // 刷新所有地图怪物
```

### 3.7 地图缓存管理

```java
// 玩家缓存管理
public void addPlayerToMap(UserCharacter character, String mapId, int x, int y)
public List<PlayerMapInfo> getPlayersInMap(long areaId, String mapId)

// 怪物缓存管理
public List<MonsterInstance> getMonstersInMap(Long areaId, String mapId)
public void addMonsterToMap(Long areaId, String mapId, MonsterInstance monsterInstance)
public void removeMonsterFromMap(Long areaId, String mapId, int tempId)
public void updateMonsterHp(Long areaId, String mapId, int tempId, int newHp)
public MonsterInstance getMonsterInstance(long areaId, String mapId, int tempId)
public Long generateMonsterInstanceId()
```

## 4. BattleManager - 战斗管理器

### 4.1 PVE战斗系统

```java
// 攻击怪物
public String attackMonster(Long characterId, int tempId)  // 普通攻击怪物

// 技能攻击
public String useSkillOnMonster(Long characterId, String skillNo, int tempId)  // 使用技能攻击怪物
```

### 4.2 PVP战斗系统

```java
// 玩家对战
public String attackPlayer(Long attackerId, int targetTempId)  // 攻击其他玩家
public String useSkillOnPlayer(Long attackerId, String skillNo, int targetTempId)  // 对玩家使用技能
```

### 4.3 战斗状态管理

```java
// 战斗状态查询
public BattleState getBattleState(Long characterId)  // 获取角色战斗状态
public boolean isInBattle(Long characterId)          // 检查角色是否在战斗中

// 冷却管理
public boolean isInAttackCooldown(Long characterId)                    // 检查攻击冷却
public boolean isSkillInCooldown(Long characterId, String skillNo)    // 检查技能冷却
public long getSkillCooldownRemaining(Long characterId, String skillNo)  // 获取技能剩余冷却时间
```

### 4.4 复活系统

```java
public String reviveCharacter(Long characterId)  // 复活角色
```

## 5. AssetManager - 资产管理器

### 5.1 装备系统

```java
// 装备穿戴
public String equipEquipment(UserCharacter character, UserAsset asset)  // 穿戴装备
public String unequipEquipment(Long characterId, int position)          // 卸下装备

// 装备属性计算
public JSONObject calculateEquipmentAttributes(Long characterId)  // 计算装备属性加成
```

### 5.2 资产查询

```java
// 背包和装备查询
public List<UserAsset> getInventoryItems(Long characterId)     // 获取背包物品列表
public List<UserAsset> getEquippedItems(Long characterId)      // 获取已装备物品列表
public List<UserAsset> getStorageItems(Long characterId)       // 获取仓库物品列表

// 货币查询
public int getSilverCount(Long characterId)                    // 获取银两数量
public int getGoldCount(Long characterId)                      // 获取金币数量

// 物品查询
public UserAsset findAssetByItemNo(Long characterId, String itemNo, int position)  // 查找指定物品
public boolean hasEnoughAsset(Long characterId, String itemNo, int count, int position)  // 检查物品数量
```

### 5.3 资产操作

```java
// 物品获得和消耗
public void addAsset(Long characterId, String itemNo, int count, int position)     // 添加物品
public boolean consumeAsset(Long characterId, String itemNo, int count, int position)  // 消耗物品

// 货币操作
public boolean addSilver(Long characterId, int amount)         // 增加银两
public boolean consumeSilver(Long characterId, int amount)     // 消耗银两
public boolean addGold(Long characterId, int amount)           // 增加金币
public boolean consumeGold(Long characterId, int amount)       // 消耗金币
```

### 5.4 装备升级系统

```java
// 装备升品
public String upgradeEquipment(Long characterId, Long assetId)  // 装备升品

// 玉佩浣灵
public String refineJade(Long characterId, Long assetId)        // 玉佩浣灵
```

### 5.5 背包管理

```java
// 背包整理和查看
public String getInventoryDisplay(Long characterId)            // 获取背包显示信息
public String getEquipmentDisplay(Long characterId)            // 获取装备显示信息
public String getStorageDisplay(Long characterId)              // 获取仓库显示信息

// 物品移动
public String moveItemToStorage(Long characterId, Long assetId)     // 移动物品到仓库
public String moveItemFromStorage(Long characterId, Long assetId)   // 从仓库取出物品
```

## 6. TradeManager - 交易管理器

### 6.1 NPC交易系统

```java
// NPC商店交易
public String buyFromNPC(Long characterId, String npcId, String itemNo, int quantity)  // 从NPC购买物品
public String sellToNPC(Long characterId, String npcId, Long assetId, int quantity)    // 向NPC出售物品
public String askNPC(Long characterId, String npcId, String question)                  // 询问NPC信息

// 商店信息查询
public String getNPCShopInfo(String npcId)                     // 获取NPC商店信息
public List<ShopsConfig.ShopItem> getNPCShopItems(String npcId)  // 获取NPC商店物品列表
```

### 6.2 玩家交易系统

```java
// 玩家间交易
public String requestTrade(Long buyerId, Long sellerId, Long assetId, int quantity, int price)  // 发起交易请求
public String acceptTrade(Long sellerId, String tradeId)        // 接受交易
public String rejectTrade(Long sellerId, String tradeId)        // 拒绝交易
public String cancelTrade(Long characterId, String tradeId)     // 取消交易
```

### 6.3 交易状态查询

```java
// 交易状态管理
public boolean isInTrade(Long characterId)                     // 检查角色是否在交易中
public void cancelAllTrades(Long characterId)                  // 取消角色的所有交易

// 交易记录
public String getTradeHistory(Long characterId, int limit)     // 获取交易历史记录
public Map<String, Object> getTradeStats(Long characterId)     // 获取交易统计信息
```

### 6.4 定时任务

```java
public void cleanExpiredTradeSessions()  // 清理过期的交易会话
public void refreshNPCShopStock()        // 刷新NPC商店库存
```

## 7. QuestManager - 任务管理器

### 7.1 任务查询

```java
// 任务列表查询
public String getQuestList(Long characterId, Integer questType)  // 获取角色任务列表
public String getQuestDetail(Long characterId, String questName)  // 获取任务详细信息
```

### 7.2 任务操作

```java
// 任务接取和提交
public String acceptQuest(Long characterId, String questName)   // 接取任务
public String submitQuest(Long characterId, String questName)   // 提交任务
public String abandonQuest(Long characterId, String questName)  // 放弃任务
```

### 7.3 任务进度管理

```java
// 任务进度更新（系统调用）
public void updateQuestProgress(Long characterId, String questType, String target, int amount)  // 更新任务进度
public void checkAndCompleteQuests(Long characterId)            // 检查并完成任务
```

### 7.4 任务状态查询

```java
// 任务状态查询
public boolean hasActiveQuests(Long characterId)               // 检查是否有进行中的任务
public int getCompletedQuestCount(Long characterId)            // 获取可提交的任务数量
public String getQuestSummary(Long characterId)                // 获取任务简要信息
public Map<String, Object> getQuestStats(Long characterId)     // 获取任务统计信息
```

### 7.5 定时任务

```java
public void cleanExpiredQuests()  // 清理过期任务
```

---

## 使用说明

1. **事务处理**: 带有`@Transactional`注解的方法会自动处理数据库事务
2. **异常处理**: 所有方法都会抛出`GameException`，需要在CommandHandler中捕获处理
3. **返回值**: 大部分操作方法返回String类型的结果信息，可直接返回给用户
4. **参数验证**: Manager层已包含基本的参数验证，但CommandHandler仍需要进行用户输入验证
5. **缓存机制**: 角色、地图等数据使用缓存机制，Manager层会自动处理缓存同步

## 8. CollectManager - 采集管理器

### 8.1 手动采集操作（立即执行）

```java
// 手动采集功能 - 立即返回结果
public String fishing(Long characterId)         // 手动钓鱼
public String mulberry(Long characterId)        // 手动采桑
public String tea(Long characterId)             // 手动采茶
public String mining(Long characterId)          // 手动挖矿
```

### 8.2 挂机采集操作（异步执行）

```java
// 挂机采集功能 - 异步执行，定时返回结果
public String startIdleFishing(Long characterId)    // 开始挂机钓鱼
public String startIdleMulberry(Long characterId)   // 开始挂机采桑
public String startIdleTea(Long characterId)        // 开始挂机采茶
public String startIdleMining(Long characterId)     // 开始挂机挖矿

// 挂机采集管理
public String stopIdleCollect(Long characterId)     // 停止挂机采集
public String getIdleCollectStatus(Long characterId) // 获取挂机采集状态
public String collectIdleRewards(Long characterId)   // 收取挂机采集收益
```

### 8.3 采集状态查询

```java
// 冷却和状态查询
public boolean isInCooldown(Long characterId, String collectType)     // 检查采集冷却
public long getCooldownRemaining(Long characterId, String collectType) // 获取剩余冷却时间
public boolean isInIdleCollect(Long characterId)                      // 检查是否在挂机采集中
public Map<String, Object> getCollectStats(Long characterId)          // 获取采集统计信息
```

## 9. Helper - 工具类

### 9.1 角色位置操作

```java
public static void setCharacterPosition(UserCharacter character, String mapId, Integer x, Integer y)
public static Map<String, Object> getCharacterPosition(UserCharacter character)
public static String getCurrentMapId(UserCharacter character)
```

### 9.2 属性计算工具

```java
public static void addAttributes(JSONObject target, JSONObject source)  // 属性叠加
public static double calculateAttributeGrowthWithDefaults(Integer level, boolean isHpMp)
```

### 9.3 资产操作工具

```java
public static boolean isAssetBelongsToCharacter(UserAsset asset, UserCharacter character)
public static int getAssetQuality(UserAsset asset)
public static void setAssetQuality(UserAsset asset, int quality)
```

### 9.4 其他工具方法

```java
public static String getRoleTypeName(Integer roleType)             // 获取职业名称
public static String extractSkillNoFromItem(String itemNo)         // 从物品编号提取技能编号
```

---

## 使用说明

1. **事务处理**: 带有`@Transactional`注解的方法会自动处理数据库事务
2. **异常处理**: 所有方法都会抛出`GameException`，需要在CommandHandler中捕获处理
3. **返回值**: 大部分操作方法返回String类型的结果信息，可直接返回给用户
4. **参数验证**: Manager层已包含基本的参数验证，但CommandHandler仍需要进行用户输入验证
5. **缓存机制**: 角色、地图等数据使用缓存机制，Manager层会自动处理缓存同步

## 开发建议

1. 在CommandHandler中优先使用Manager提供的高级接口
2. 复杂业务逻辑应该在Manager层实现，CommandHandler只负责参数解析和结果返回
3. 注意处理Manager方法可能抛出的异常
4. 合理使用事务方法，避免数据不一致
5. 注意Manager之间的依赖关系，避免循环调用
6. 使用Helper工具类简化常用操作
