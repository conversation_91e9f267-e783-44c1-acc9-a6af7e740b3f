# 西子江湖技术方案设计

## 文档更新记录

| 版本   | 日期         | 更新内容                                       | 更新人  |
| ---- | ---------- | ------------------------------------------ | ---- |
| v2.0 | 2025-01-12 | 重大架构重构：完成Manager层实现，引入Event机制解决循环依赖，更新架构设计 | AI助手 |
| v1.1 | 2025-01-06 | 优化命令系统设计，修正战斗距离机制，补充挂机系统、内存状态管理、日志系统设计     | AI助手 |
| v1.0 | 2024-XX-XX | 初始版本                                       | 原作者  |

## 第一章 系统架构设计

### 1.1 整体架构

#### 1.1.1 系统架构图

```mermaid
graph TB
    Bot[HomeBot] <--> |HTTP| GS[游戏服务]
    GS --> |命令处理| Core[游戏核心]
    Core --> |读写| DB[(MySQL)]
    Core --> |缓存| Cache[(本地缓存)]
    Core --> |异步任务| Queue[内存队列]
    Core --> |事件发布| Event[Event机制]
    Event --> |异步处理| Core
    GS -.-> |消息推送| Bot

    subgraph 游戏核心服务
        Core
        Cache
        Queue
        Event
    end

    subgraph 数据层
        DB
        Config[配置文件]
    end

    Core --> |读取| Config
```

### 1.2 代码层次调用架构

主要代码包调用层次：Controller—>Command—>Manager—>Mapper

#### 1.2.1 完整架构调用图

```mermaid
graph TB
    %% 控制层
    subgraph "Controller Layer 控制层"
        GC[GameController<br/>游戏控制器]
        HC[HealthController<br/>健康检查控制器]
    end

    %% 命令层
    subgraph "Command Layer 命令层"
        CP[CommandProcessor<br/>命令处理器]
        subgraph "Command Handlers 命令处理器"
            PCH[PlayerCommandHandler<br/>玩家命令处理器]
            BCH[BattleCommandHandler<br/>战斗命令处理器]
            ECH[EquipCommandHandler<br/>装备命令处理器]
            TCH[TradeCommandHandler<br/>交易命令处理器]
            MCH[MapCommandHandler<br/>地图命令处理器]
            HCH[HangCommandHandler<br/>挂机命令处理器]
        end
    end

    %% 管理层
    subgraph "Manager Layer 管理层"
        PM[PlayerManager<br/>玩家管理器]
        BM[BattleManager<br/>战斗管理器]
        EM[AssetManager<br/>资产管理器]
        TM[TradeManager<br/>交易管理器]
        MM[MapManager<br/>地图管理器]
        HM[HangManager<br/>挂机管理器]
        CM[CacheManager<br/>缓存管理器]
        CFM[ConfigManager<br/>配置管理器]
    end

    %% 数据访问层
    subgraph "Mapper Layer 数据访问层"
        UMapper[UserMapper<br/>用户数据访问]
        AMapper[AssetMapper<br/>资产数据访问]
        CMapper[CharacterMapper<br/>角色数据访问]
        RMapper[RankMapper<br/>排行榜数据访问]
    end

    %% 缓存层
    subgraph "Cache Layer 缓存层"
        Redis[(Redis缓存)]
        Memory[(内存缓存)]
    end

    %% 数据库层
    subgraph "Database Layer 数据库层"
        MySQL[(MySQL数据库)]
    end

    %% 配置层
    subgraph "Config Layer 配置层"
        YML[YAML配置文件]
    end

    %% Controller到Command的调用
    GC --> CP
    HC --> CP

    %% CommandProcessor到Handler的调用
    CP --> PCH
    CP --> BCH
    CP --> ECH
    CP --> TCH
    CP --> MCH
    CP --> HCH

    %% Handler到Manager的调用
    PCH --> PM
    BCH --> BM
    ECH --> EM
    TCH --> TM
    MCH --> MM
    HCH --> HM

    %% Manager之间的调用关系
    PM --> CM
    BM --> CM
    BM --> CFM
    EM --> CM
    TM --> CM
    MM --> CM
    HM --> CM
    PM --> CFM
    EM --> CFM
    TM --> CFM

    %% Manager到Mapper的调用
    PM --> UMapper
    PM --> CMapper
    EM --> AMapper
    TM --> AMapper
    BM --> CMapper

    %% Cache调用关系
    CM --> Redis
    CM --> Memory

    %% 数据库调用
    UMapper --> MySQL
    AMapper --> MySQL
    CMapper --> MySQL
    RMapper --> MySQL

    %% 配置调用
    CFM --> YML

    %% 样式定义
    classDef controllerClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef commandClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef handlerClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef managerClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef mapperClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef cacheClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef dbClass fill:#ffebee,stroke:#c62828,stroke-width:2px

    class GC,HC controllerClass
    class CP commandClass
    class PCH,BCH,ECH,TCH,MCH,HCH handlerClass
    class PM,BM,EM,TM,MM,HM,CM,CFM managerClass
    class UMapper,AMapper,CMapper,RMapper mapperClass
    class Redis,Memory cacheClass
    class MySQL,YML dbClass
```

#### 1.2.2 架构层次说明

1. **Controller层（控制层）**
   
   - 接收HomeBot请求，参数校验
   - 统一入口，路由到命令处理器
   - 返回响应结果和异常处理

2. **Command层（命令层）**
   
   - **CommandProcessor（命令处理器）**：
     - 命令解析和验证
     - 命令路由到具体Handler
     - 采用命令模式设计
   - **CommandHandler（命令处理器）**：
     - 具体命令的业务逻辑实现
     - 每个Handler负责一个领域的命令
     - 调用相应的Manager完成业务操作

3. **Manager层（管理层）**
   
   - 领域业务管理和数据处理
   - 通过Event机制实现Manager间松耦合通信
   - 缓存管理和配置管理
   - 内存状态管理（挂机、冷却、交易等）
   - 基于Spring事件机制的异步处理

4. **Mapper层（数据访问层）**
   
   - 数据库CRUD操作
   - SQL映射和结果处理
   - 数据持久化

5. **Cache层（缓存层）**
   
   - Redis：分布式缓存
   - Memory：本地内存缓存

6. **Database层（数据库层）**
   
   - MySQL：持久化存储

7. **Config层（配置层）**
   
   - YAML：游戏配置文件

#### 1.2.3 命令模式设计说明

**命令处理流程**：

```
HomeBot请求 → GameController → CommandProcessor → 具体CommandHandler → Manager → Mapper
```

**命令模式优势**：

- **解耦**：Controller与具体业务逻辑解耦
- **扩展性**：新增命令只需添加新的Handler
- **可维护性**：每个Handler职责单一，易于维护
- **可测试性**：Handler可以独立测试

**Handler设计原则**：

- 每个Handler负责一个业务领域
- Handler内部调用相应的Manager
- Handler之间不直接调用，通过Manager协作

#### 1.2.4 命令模式实现示例

**CommandProcessor实现**：

```java
@Component
public class CommandProcessor {

    private final Map<String, CommandHandler> handlerMap;

    public CommandProcessor(List<CommandHandler> handlers) {
        this.handlerMap = handlers.stream()
            .collect(Collectors.toMap(
                CommandHandler::getCommandType,
                Function.identity()
            ));
    }

    public GameResponse processCommand(GameRequest request) {
        // 1. 命令解析
        String commandType = parseCommandType(request.getCommand());

        // 2. 获取对应的Handler
        CommandHandler handler = handlerMap.get(commandType);
        if (handler == null) {
            return GameResponse.error("未知命令");
        }

        // 3. 执行命令
        return handler.handle(request);
    }
}
```

**CommandHandler接口**：

```java
public interface CommandHandler {
    String getCommandType();
    GameResponse handle(GameRequest request);
}
```

**具体Handler实现示例**：

```java
@Component
public class PlayerCommandHandler implements CommandHandler {

    private final PlayerManager playerManager;

    @Override
    public String getCommandType() {
        return "player";
    }

    @Override
    public GameResponse handle(GameRequest request) {
        String action = request.getAction();

        switch (action) {
            case "create":
                return handleCreatePlayer(request);
            case "status":
                return handlePlayerStatus(request);
            case "move":
                return handlePlayerMove(request);
            default:
                return GameResponse.error("未知玩家操作");
        }
    }

    private GameResponse handleCreatePlayer(GameRequest request) {
        // 调用PlayerManager处理业务逻辑
        return playerManager.createPlayer(request);
    }
}
```

#### 1.1.2 系统组件说明

1. HTTP服务
   
   - 提供RESTful API接口
   - 处理HomeBot请求
   - 返回游戏响应
   - 消息推送

2. 游戏核心服务
   
   - 命令解析和验证
   - 游戏逻辑处理(命令处理、异步任务、消息发送、定时任务)
   - 状态管理
   - 数据处理

3. 内存队列服务
   
   - 异步任务队列
   - 消息推送队列
   - 定时任务队列

4. 本地缓存服务
   
   - 游戏玩法数据缓存
   - 玩家实例状态缓存
   - 地图实例状态缓存
   - 全局游戏配置缓存

5. 数据存储服务
   
   - 数据持久化
   - 数据查询
   - 数据统计

### 1.2 技术选型

1. 开发语言和框架
   
   - Java 8+
   - SpringBoot 2.7.x
   - MyBatis-Plus 3.5.x

2. 数据存储
   
   - MySQL 5.7: 数据持久化
   - ConcurrentHashMap: 本地内存缓存

3. 消息队列
   
   - BlockingQueue: 本地内存队列
   - 异步任务处理

### 1.3 模块划分

#### 1.3.1 核心模块

1. 命令处理模块（command）
   
   - 命令格式定义
     
     - 基础格式：`<命令> [参数1] [参数2]...`
     - 示例：玩家输入 `使用 回气丹 1` 转换为系统命令 `use 回气丹 1`
     - 参数规则：
       - 空格分隔参数
       - 引号支持空格内容
       - 方括号表示可选参数
       - 尖括号表示必选参数
     - 命令标识机制：
       - 系统命令标识：使用英文（如：use, buy, sell）作为代码中的唯一标识符
       - 玩家输入别名：支持多个中文别名，最终转换为英文标识符
       - 示例：use命令支持[使用,服用,喝,装备]等别名，代码中统一使用use处理
   
   - 命令类型
     
     - 游戏模式命令
       
       - 进入游戏模式：`gamemode` [!游戏模式]
       - 退出游戏模式：`exitgame` [!退出游戏模式]
       - 游戏帮助：`help` [!帮助]
       - 职业介绍：`profession` [!职业介绍]
       - 新手指南：`guide [章节号]` [!新手指南, !新手指南 1]
       - 创建区服：`createserver <区服名称>` [建服]
     
     - 人物操作命令
       
       - 创建角色：`create <名称> <职业>` [创建角色]
       - 查看状态：`status [@玩家]` [查看状态, 状态, st]
       - 查看装备：`equip [@玩家]` [查看装备, 装备, zb]
       - 查看背包：`bag [@玩家] [序号]` [查看背包, 背包, bb]
       - 使用物品：`use <物品> [数量]` [使用,服用,喝,装备]
       - 兑换货币：`exchange <银两数量>` [兑现]
       - 仓库存取：
         - `store <物品> [数量]` [存]
         - `fetch <物品> [数量]` [取]
       - 查看排名：`rank [排行榜类型]` [查看排名, 排名, pm]
       - 复活：`revive` [复活]
     
     - 地图操作命令
       
       - 移动：`move <地点名/编号>` [前往, 去]
       - 查看地图：`map` [查看地图, 地图, map]
       - 环境扫描：`scan` [查看环境, 扫描, 环境]
       - 查看怪物：`monster <怪物名/编号>` [查看怪物]
     
     - 挂机
       
       - 挂机：`hold <采集/钓鱼/采矿/打怪> [使用技能]` [挂机]
       - 取消挂机：`cancelhold` [取消挂机]
     
     - 战斗命令
       
       - 攻击：`attack <目标编号/名称> [技能]` [攻击, 打, 杀, 砍]
       - 使用技能：`skill <技能名> <目标编号/名称>` [技能, 施法]
     
     - 采集命令
       
       - 钓鱼：`fish` [钓鱼]
       - 采矿：`mine` [采矿, 挖矿]
       - 采集：`collect` [采集, 采桑, 采茶]
     
     - NPC交易命令
       
       - 购买物品：`buy <物品> [数量]` [买]
       - 出售物品：`sell <物品> [数量]` [卖]
       - 询问信息：`ask <NPC> <内容>` [询问]
     
     - 人物交易命令
       
       - 邀请交易：
         - `buyinvite <玩家编号> <背包序号> <价格> [货币类型]` [买@玩家, 买1]
         - 简化格式：`买1 1 1000银` (买编号1玩家背包第1格物品，1000银两)
       - 同意交易：`tradeagree` [同意, 卖@玩家]
     
     - 装备操作命令
       
       - 升级品质：`upgrade <装备>` [升品]
       - 浣灵玉佩：`refine <玉佩>` [浣灵]
     
     - 任务命令
       
       - 接受任务：`accept <任务名/编号>` [接受任务, 接任务]
       - 提交任务：`submit <任务名/编号>` [提交任务, 完成任务]
       - 查看任务：`quest` [查看任务, 任务]
       - 取消任务：`cancel <任务名/编号>` [取消任务]
- 命令解析器（CommandParser）
  
  - 命令预处理
    - 去除首尾空格
    - 合并多余空格
    - 提取命令和参数
    - 汉字命令转义英文
    - 人物名转游戏ID
  - 格式验证
    - 命令是否存在
    - 参数个数检查
    - 参数格式检查
    - 对象目标检查

- 命令验证器（CommandValidator）
  
  - 权限检查
    - 角色状态检查
    - 命令权限检查
    - 场景限制检查
    - 命令限流检查
  - 参数验证
    - 参数类型转换
    - 参数值验证
    - 业务规则检查
  - 错误处理
    - 统一错误反馈格式：`❌ 操作失败：[具体原因] 💡 建议：[解决方案]`
    - 常见错误类型处理：
      - 距离过远：提示使用"前往 目标编号"靠近
      - 命令不存在：提示发送"!帮助"查看可用命令
      - 参数错误：显示正确的命令格式示例
      - 资源不足：显示当前资源和所需资源
      - 冷却中：显示剩余冷却时间
      - 目标不存在：提示发送"查看地图"刷新目标列表

- 命令执行器（CommandExecutor）
  
  - 前置处理
    - 加载角色数据
    - 检查执行条件
    - 准备执行环境
  - 命令执行
    - 执行业务逻辑
    - 更新游戏状态
    - 记录操作日志
  - 后置处理
    - 保存状态变更
    - 生成反馈消息
    - 触发相关事件

- 命令逻辑实现(Handler)：游戏玩法核心处理
  
  各种命令的具体实现，根据命令支持类型等，确定找到对应的Handler实现类进行调用执
2. 消息处理模块（MessageModule）
   
   - 消息队列管理(MessageQuenceService)
     
     - 系统通知队列
       - 系统公告
       - 战斗结果
       - 任务更新
     - 定时任务队列
       - 地图刷新
       - 状态恢复
       - 数据统计
   
   - 消息处理
     
     - 消息生成
     - 消息发送
     - 错误处理

#### 1.3.2 领域数据模块

##### 处理数理

1. 玩家（player）
   
   - 角色管理（CharacterManager）
   - 背包管理（InventoryManager）
   - 装备管理（EquipmentManager）
   - 技能管理（SkillManager）

2. 地图（map）
   
   - 地图状态管理（MapManager）
     - 地图基础信息
     - 地图状态维护（刷怪信息）
     - 玩家位置信息
   - NPC管理（NPCManager）
     - NPC基础信息
     - NPC位置管理
     - NPC交互管理
   - 怪物管理（MonsterManager）
     - 怪物基础信息
     - 怪物状态维护
     - 怪物掉落计算

3. 交易（trade）
   
   - 玩家交易管理（PlayerTradeManager）
     - 交易请求处理
     - 物品转移
     - 交易日志
   - NPC商店管理（ShopManager）
     - 商品配置管理
     - 买入处理
     - 卖出处理

4. 任务模块（quest）
   
   - 任务管理（QuestManager）
   - 进度管理（ProgressManager）
   - 奖励管理（RewardManager

##### 数据缓存（cache）

- 本地缓存管理
  
  - 玩家数据缓存
  - 战斗数据缓存
  - 地图状态数据缓存
    - 地图基础信息
    - NPC/怪物分布
    - 玩家位置信息
  - 游戏配置数据缓存

- 缓存策略
  
  - 启动时加载配置
  - 按需加载数据
  - 定期清理过期

##### 数据存储

- 数据对象（Entity）
  - 道具实体（ItemDO）- 包含装备和物品
  - 技能实体（SkillDO）
  - 怪物实体（MonsterDO）
  - 用户角色实体（UserCharacterDO）
  - 用户资产实体（UserAssetDO）
  - 排行榜实体（RankDO）
  - 充值记录实体（RechargeRecordDO）
  - 任务实体（QuestDO）
  - 任务进度实体（QuestProgressDO）
- 数据访问层（Mapper）
  - ItemMapper
  - SkillMapper
  - MonsterMapper
  - UserCharacterMapper
  - UserAssetMapper
  - RankMapper
  - RechargeRecordMapper
  - QuestMapper
  - QuestProgressMapper

#### 1.3.3 异步任务模块(schedule)

1. 地图怪物刷新定时任务

2. 排行榜更新定时任务

3. 统计数据收集定时任务

#### 1.3.4 支持模块

1. 游戏配置模块（config）
   
   - 配置加载器（ConfigLoader）
   - 配置管理器（ConfigManager）

2. 通用模块(common)
   
   - 游戏常量
   
   - 统一响应封装对象
   
   - 公共缓存对象

### 1.4 核心数据流

#### 1.4.1 命令处理流程

```mermaid
sequenceDiagram
    participant 微信群
    participant Bot as 微信机器人
    participant CMD as 命令引擎
    participant Core as 游戏核心
    participant Cache as 缓存层
    participant DB as 数据库
    participant MQ as 消息队列

    微信群->>Bot: 发送命令
    Bot->>CMD: 转发命令
    CMD->>CMD: 解析命令
    CMD->>Core: 执行命令
    Core->>Cache: 读取状态
    Core->>DB: 读取数据
    Core->>Core: 处理逻辑
    Core->>Cache: 更新状态
    Core->>DB: 持久化数据
    Core->>MQ: 生成反馈消息
    MQ->>Bot: 推送消息
    Bot->>微信群: 返回结果
```

#### 1.4.2 数据流转说明

1. 命令接收流程
   
   - 微信消息接收
   - 命令格式转换
   - 命令解析和验证
   - 命令入队处理

2. 状态管理流程
   
   - 状态数据加载
   - 状态更新处理
   - 状态数据缓存
   - 状态持久化

3. 消息处理流程
   
   - 消息生成
   - 消息合并
   - 消息队列管理
   - 消息推送

4. 数据持久化流程
   
   - 数据变更检测
   - 数据缓存更新
   - 数据库写入
   - 数据同步确认

#### 1.4.3 关键数据流

1. 玩家操作流
   
   - 命令接收
   - 状态检查
   - 逻辑处理
   - 状态更新
   - 结果返回

2. 战斗数据流
   
   - 战斗请求
   - 属性计算
   - 战斗处理
   - 结果生成
   - 状态更新

3. 交易数据流
   
   - 交易请求
   - 物品校验
   - 交易执行
   - 物品转移
   - 日志记录

## 第二章 Event机制设计

### 2.1 Event机制概述

#### 2.1.1 设计目标

- **解决循环依赖**：Manager间通过事件通信，避免直接依赖
- **异步处理**：基于Spring事件机制，提升系统性能
- **松耦合架构**：模块间通过事件接口交互，降低耦合度
- **扩展性**：新增事件类型和监听器非常容易

#### 2.1.2 架构设计

```mermaid
graph TB
    subgraph "Event机制架构"
        EP[EventPublisher<br/>事件发布器]
        AEP[ApplicationEventPublisher<br/>Spring事件发布器]

        subgraph "事件类型"
            GE[GameEvent<br/>游戏事件]
            CAE[角色属性更新事件]
            CPE[角色位置更新事件]
        end

        subgraph "事件监听器"
            PM[PlayerManager<br/>监听位置和属性事件]
            MM[MapManager<br/>监听位置事件]
            AM[AssetManager<br/>发布属性事件]
        end
    end

    EP --> AEP
    AEP --> GE
    GE --> CAE
    GE --> CPE
    CAE --> PM
    CPE --> PM
    CPE --> MM
    AM --> EP
```

### 2.2 Event机制实现

#### 2.2.1 核心组件

**EventPublisher（事件发布器）**：

```java
@Component
public class EventPublisher {
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    // 发布通用事件
    public void publishEvent(int eventType, Object eventData) {
        GameEvent event = new GameEvent(eventType, eventData);
        eventPublisher.publishEvent(event);
    }

    // 发布角色位置更新事件
    public void publishPositionEvent(long characterId, int x, int y, String mapId) {
        Position position = new Position(characterId, x, y, mapId);
        publishEvent(GameEvent.TYPE_CHARACTER_UPDATE_POSITION, position);
    }
}
```

**GameEvent（游戏事件）**：

```java
public class GameEvent {
    // 事件类型常量
    public static final int TYPE_CHARACTER_UPDATE_ATTR = 1;     // 角色属性更新
    public static final int TYPE_CHARACTER_UPDATE_POSITION = 2; // 角色位置更新

    private Integer eventType;
    private Object eventData;
}
```

#### 2.2.2 事件监听实现

**PlayerManager事件监听**：

```java
@EventListener
public void listenCharacterEvent(EventPublisher.GameEvent event) {
    if(event.getEventType() == EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_POSITION){
        EventPublisher.Position position = (EventPublisher.Position) event.getEventData();
        updateCharacterPosition(position.getCharacterId(), position.getMapId(),
                               position.getX(), position.getY());
        return;
    }
    if(event.getEventType() == EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_ATTR){
        updateCharacterAttributesWithEquipment((Long) event.getEventData());
        return;
    }
}
```

### 2.3 Event使用场景

#### 2.3.1 装备穿戴触发属性更新

```java
// AssetManager中穿戴装备后
eventPublisher.publishEvent(EventPublisher.GameEvent.TYPE_CHARACTER_UPDATE_ATTR, characterId);

// PlayerManager自动监听并更新角色属性
```

#### 2.3.2 地图移动触发位置更新

```java
// MapManager中移动后
eventPublisher.publishPositionEvent(characterId, newX, newY, mapId);

// PlayerManager自动监听并更新角色位置
```

### 2.4 Event机制优势

#### 2.4.1 解决循环依赖

- **传统方式**：PlayerManager ↔ AssetManager 直接相互调用
- **Event方式**：AssetManager → Event → PlayerManager 单向依赖

#### 2.4.2 提升系统性能

- **异步处理**：事件处理不阻塞主流程
- **批量处理**：可以批量处理相同类型事件
- **缓存友好**：减少不必要的数据库访问

#### 2.4.3 增强扩展性

- **新增事件类型**：只需添加常量和监听器
- **新增监听器**：任何Manager都可以监听任何事件
- **事件链**：一个事件可以触发多个后续事件

## 第三章 数据库设计

### 3.1 数据对象模型设计

1. 核心数据对象
   
   - 用户角色对象
     - 基础属性
     - 状态属性
     - 位置信息
   - 道具对象
     - 装备
     - 物品
     - 技能书
   - 技能对象
     - 职业技能
   - 怪物对象
     - 基础属性
     - 掉落配置
   - 任务对象
     - 任务定义
     - 任务进度
   - 地图对象
     - 地图基础属性
     - 地图怪物信息
     - 地图玩家信息

2. 数据表关系
   
   1. 核心关系图

```mermaid
erDiagram
 user_character ||--o{ user_asset : "拥有"
 user_character ||--o{ quest_progress : "进行"
 quest ||--o{ quest_progress : "包含"
 item ||--o{ user_asset : "实例化"
```

2. 关系说明
   
   - 用户角色与用户资产：一个角色可以拥有多个资产实例
   - 用户角色与任务进度：一个角色可以同时进行多个任务实例
   - 任务与任务进度：一种任务可以被多个用户同时进行
   - 道具与用户资产：一种道具可以被多个用户拥有实例
   - 区服：用户、地图实例、怪物实例、用户资产、用户任务进度、排行傍、充值记录实例都在某一个区服下

3. 数据初始化设计
   详见"技术设计\sql"目录下初始化SQL文件
   
   1. 区服数据(area表)
      
      ```sql
      -- 初始区服
      INSERT INTO area (open_id, area_name, app_id) VALUES
      ('AREA_001', '西子湖畔', 'XZJH_001'),
      ('AREA_002', '雷峰夕照', 'XZJH_001'),
      ('AREA_003', '断桥残雪', 'XZJH_001');
      ```
   
   2. 道具数据(item表)
      
      - 装备数据
        
        ```sql
        -- 游湖套装
        INSERT INTO item (item_no, type, sub_type, name, description, attributes, role_limit, level_limit) VALUES
        ('EQ_YOUHU_WQ_01', 0, 1, '青锋剑', '游湖套装武器', '{"物攻":10}', 1, 1),
        ('EQ_YOUHU_TG_01', 0, 2, '游湖冠', '游湖套装头冠', '{"物防":8}', 1, 1);
        -- 更多装备数据...
        ```
      
      - 物品数据
        
        ```sql
        -- 药品
        INSERT INTO item (item_no, type, sub_type, name, description, attributes) VALUES
        ('ITEM_DRUG_HP_01', 1, 1, '回气丹', '恢复100点血气', '{"恢复":100}'),
        ('ITEM_DRUG_MP_01', 1, 2, '回法丹', '恢复100点法气', '{"恢复":100}');
        -- 更多物品数据...
        ```
   
   3. 技能数据(skill表)
      
      ```sql
      -- 剑客技能
      INSERT INTO skill (skill_no, name, type, description, power, cooldown, cost) VALUES
      ('SKILL_JK_01', '破军剑法', 0, '剑客基础物理攻击技能', 100, 15, 20),
      ('SKILL_JK_02', '御剑术', 0, '提升移动速度', 0, 30, 50);
      -- 更多技能数据...
      ```
   
   4. 怪物数据(monster表)
      
      ```sql
      -- 第一阶段怪物
      INSERT INTO monster (monster_no, name, description, level, hp, attack_w, attack_m, attack_f, defense_w, defense_m, defense_f, type) VALUES
      -- 苏堤怪物
      ('MON_WILLOW', '柳树精', '苏堤上的柳树成精，喜欢用柳条抽打路人', 5, 200, 15, 10, 5, 12, 8, 5, 'normal'),
      ('MON_FROG', '青蛙精', '苏堤边的青蛙成精，经常在夜晚发出怪笑', 5, 180, 12, 15, 8, 10, 12, 8, 'normal'),
      -- 更多怪物数据...
      ```

### 2.2 数据表结构设计

1. 区服表(area)
   
   - 字段设计:
     - id: 主键
     - open_id: 区服标识，如微信room_id
     - area_name: 区服名称(区服别名)
     - app_id: 应用标识，如微信wx
     - status: 区服状态(0:正常 1:维护 2:关闭)
     - create_time: 创建时间
     - update_time: 更新时间

2. 道具表(item)
   
   - 字段设计:
     - id: 主键
     - item_no: 道具编号(唯一)
     - type: 类型(0:装备 1:药品 2:技能书 3:材料 9:神奇盒子)
     - sub_type: 子类型(装备部位/药品类型/材料类型/盒子等级等)
     - name: 名称
     - description: 描述
     - attributes: 属性JSON
     - role_limit: 职业限制
     - level_limit: 等级限制
     - create_time: 创建时间
     - update_time: 更新时间

3. 技能表(skill)
   
   - 字段设计:
     - id: 主键
     - skill_no: 技能编号(唯一)
     - name: 技能名称
     - type: 技能类型（0-物理攻击，1-魔法攻击，2-佛法攻击，3-物理防御，4-魔法防御，5-佛法防御,6-反伤）
     - description: 描述
     - power: 威力
     - cooldown: 冷却时间
     - cost: 消耗
     - create_time: 创建时间
     - update_time: 更新时间

4. 怪物表(monster)
   
   - 字段设计:
     - id: 主键
     - monster_no: 怪物编号
     - name: 怪物名称
     - type: 怪物类型
     - level: 等级
     - hp: 血量
     - attack_m: 魔法攻击
     - attack_f: 佛法攻击
     - attack_w: 物理攻击
     - defense_m: 魔法防御
     - defense_f: 佛法防御
     - defense_w: 物理防御
     - description: 描述
     - create_time: 创建时间
     - update_time: 更新时间

5. 用户角色表(user_character)
   
   - 字段设计:
     - id: 主键
     - user_id: 用户ID
     - app_id: 应用ID(0-微信)
     - open_id: 应用open_id，外部应用区服
     - area_id: 区服ID
     - name: 角色名
     - type: 职业类型
     - level: 等级
     - exp: 经验值
     - maxHp: 当前等级最大血量
     - maxMp: 当前等级最法力值
     - hp: 血量
     - mp: 法力值
     - attributes: 属性JSON
     - status: 状态
     - position: 位置
     - create_time: 创建时间
     - update_time: 更新时间

6. 用户资产表(user_asset)
   
   - 字段设计:
     - id: 主键
     - character_id: 角色ID
     - item_no: 道具编号
     - count: 数量
     - attributes: item_no对应属性之外的实例属性JSON(degree-品级，装备才有，plus-增益，玉佩才有，其他随机生成的三防三功属性)
     - position: 位置(1:身上 2:背包 3:仓库)
     - bag_slot: 背包格子序号(1-50，仅position=2时有效)
     - create_time: 创建时间
     - update_time: 更新时间

7. 排行榜表(rank)
   
   - 字段设计:
     - id: 主键
     - type: 排行类型（1:等级 2:战力 3:财富）
     - character_id: 角色ID
     - area_id: 区服ID
     - amount: 数值
     - rank: 排名
     - update_time: 更新时间

8. 充值记录表(recharge_record)
   
   - 字段设计:
     - id: 主键
     - character_id 角色ID
     - amount: 充值金额
     - silver: 银两数量
     - status: 状态
     - create_time: 创建时间
     - update_time: 更新时间

9. 任务表(quest)
   
   - 字段设计:
     - id: 主键
     - name: 任务名称
     - type: 任务类型
     - description: 描述
     - rewards: 奖励JSON
     - create_time: 创建时间

10. 任务进度表(quest_progress)
    
    - 字段设计:
      - id: 主键
      - character_id: 角色ID
      - quest_id: 任务I
      - progress: 进度
      - status: 状态
      - create_time: 创建时间
      - update_time: 更新时间

### 2.3 数据表初始化sql

1. 区服表(area)
   
   ```sql
   CREATE TABLE `area` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `open_id` varchar(32) NOT NULL COMMENT '外部区服识别ID',
    `area_name` varchar(32) NOT NULL COMMENT '区服名称(区服别名)',
    `app_id` varchar(32) NOT NULL COMMENT '应用标识',
    `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '区服状态(0:正常 1:维护 2:关闭)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_open_id` (`open_id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_status` (`status`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='区服表';
   ```

2. 道具表(item)
   
   ```sql
   CREATE TABLE `item` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `item_no` varchar(32) NOT NULL COMMENT '道具编号',
    `type` tinyint(4) NOT NULL COMMENT '类型(0:装备 1:药品 2:技能书 3:材料 9:神奇盒子)',
    `sub_type` tinyint(4) NOT NULL COMMENT '子类型(装备部位/药品类型/材料类型/盒子等级等)',
    `name` varchar(32) NOT NULL COMMENT '名称',
    `description` varchar(255) NOT NULL COMMENT '描述',
    `attributes` json DEFAULT NULL COMMENT '属性JSON',
    `role_limit` tinyint(4) DEFAULT NULL COMMENT '职业限制',
    `level_limit` int(11) DEFAULT NULL COMMENT '等级限制',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_item_no` (`item_no`),
    KEY `idx_type` (`type`),
    KEY `idx_sub_type` (`sub_type`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='道具表';
   ```

3. 技能表(skill)
   
   ```sql
   CREATE TABLE `skill` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `skill_no` varchar(32) NOT NULL COMMENT '技能编号',
     `name` varchar(32) NOT NULL COMMENT '技能名称',
     `type` tinyint(4) NOT NULL COMMENT '（0-物理攻击，1-魔法攻击，2-佛法攻击，3-物理防御，4-魔法防御，5-佛法防御,6-反伤）',
     `description` varchar(255) NOT NULL COMMENT '描述',
     `power` int(11) NOT NULL COMMENT '威力',
     `cooldown` int(11) NOT NULL COMMENT '冷却时间',
     `cost` int(11) NOT NULL COMMENT '消耗',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_skill_no` (`skill_no`),
     KEY `idx_type` (`type`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能表';
   ```

4. 怪物表(monster)
   
   ```sql
   CREATE TABLE `monster` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `monster_no` varchar(32) NOT NULL COMMENT '怪物编号',
     `name` varchar(32) NOT NULL COMMENT '怪物名称',
     `type` tinyint(4) NOT NULL COMMENT '怪物类型',
     `level` int(11) NOT NULL COMMENT '等级',
     `hp` int(11) NOT NULL COMMENT '血量',
     `attack_m` int(11) NOT NULL COMMENT '魔法攻击',
     `attack_f` int(11) NOT NULL COMMENT '佛法攻击',
     `attack_w` int(11) NOT NULL COMMENT '物理攻击',
     `defense_m` int(11) NOT NULL COMMENT '魔法防御',
     `defense_f` int(11) NOT NULL COMMENT '佛法防御',
     `defense_w` int(11) NOT NULL COMMENT '物理防御',
     `description` varchar(255) NOT NULL COMMENT '描述',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     KEY `idx_type` (`type`),
     KEY `idx_level` (`level`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='怪物表';
   ```

5. 用户角色表(user_character)
   
   ```sql
   CREATE TABLE `user_character` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `user_id` varchar(32) NOT NULL COMMENT '用户ID',
     `app_id` tinyint(4) NOT NULL DEFAULT 0 COMMENT '应用ID(0-微信)',
     `open_id` varchar(64) NOT NULL COMMENT '外部区服识别ID',
     `area_id` int(11) NOT NULL DEFAULT 1 COMMENT '区服ID',
     `name` varchar(32) NOT NULL COMMENT '角色名',
     `type` tinyint(4) NOT NULL COMMENT '职业类型(1:剑客 2:仙师 3:圣僧)',
     `level` int(11) NOT NULL DEFAULT 1 COMMENT '等级',
     `exp` bigint(20) NOT NULL DEFAULT 0 COMMENT '经验值',
     `maxExp` bigint(20) NOT NULL DEFAULT 0 COMMENT '升级所需经验值',  
     `hp` int(11) NOT NULL COMMENT '血量',
     `mp` int(11) NOT NULL COMMENT '法力值',
     `max_hp` int(11) NOT NULL COMMENT '当前等级最大血量',
     `max_mp` int(11) NOT NULL COMMENT '当前等级最大法力值',  
     `skills` json NOT NULL COMMENT '习得的技能列表，存放技能编号',
     `attributes` json DEFAULT NULL COMMENT '属性JSON',
     `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态(0:正常 1:死亡)',
     `game_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '游戏状态(0:离线 1:正常在线 2:账号被禁)',
     `position` json NOT NULL COMMENT '位置信息',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_user_app_area` (`user_id`,`app_id`,`area_id`),
     KEY `idx_open_id` (`open_id`),
     KEY `idx_type` (`type`),
     KEY `idx_level` (`level`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色表';
   ```

6. 用户资产表(user_asset)
   
   ```sql
   CREATE TABLE IF NOT EXISTS `user_asset` (
       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
       `character_id` int(11) NOT NULL DEFAULT 1 COMMENT '角色ID',
       `item_no` varchar(32) NOT NULL COMMENT '道具编号',
       `count` int(11) NOT NULL DEFAULT 1 COMMENT '数量',
       `attributes` json DEFAULT NULL COMMENT '属性JSON，装备属性见GameAttributeConstant定义的key属性',
       `position` tinyint(4) NOT NULL COMMENT '位置(1:身上 2:背包 3:仓库)',
       `bag_slot` tinyint(4) DEFAULT NULL COMMENT '背包格子序号(1-50，仅position=2时有效)',
       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       PRIMARY KEY (`id`),
       KEY `idx_character_id` (`character_id`),
       KEY `idx_item` (`item_no`),
       KEY `idx_position` (`position`),
       KEY `idx_bag_slot` (`character_id`, `position`, `bag_slot`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资产表';
   ```

7. 排行榜表(rank)
   
   ```sql
   CREATE TABLE `rank` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `type` tinyint(4) NOT NULL COMMENT '排行类型(1:等级 2:战力 3:财富)',
     `character_id` int(11) NOT NULL COMMENT '角色ID',
     `character_name` varchar(32) NOT NULL COMMENT '角色名',
     `area_id` int(11) NOT NULL DEFAULT 1 COMMENT '区服ID',
     `amount` bigint(20) NOT NULL COMMENT '数值',
     `rank` int(11) NOT NULL COMMENT '排名',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_type_character_id` (`type`,`character_id`),
     KEY `idx_area_id` (`area_id`),
     KEY `idx_amount` (`amount`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜表';
   ```

8. 充值记录表(recharge_record)
   
   ```sql
   CREATE TABLE `recharge_record` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `character_id` int(11) NOT NULL COMMENT '角色ID',
     `amount` decimal(10,2) NOT NULL COMMENT '充值金额',
     `silver` int(11) NOT NULL COMMENT '银两数量',
     `status` tinyint(4) NOT NULL COMMENT '状态(0:处理中 1:成功 2:失败)',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     KEY `idx_character_id` (`character_id`),
     KEY `idx_status` (`status`),
     KEY `idx_create_time` (`create_time`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';
   ```

9. 任务表(quest)
   
   ```sql
   CREATE TABLE `quest` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `name` varchar(32) NOT NULL COMMENT '任务名称',
     `type` tinyint(4) NOT NULL COMMENT '任务类型(1:主线 2:支线 3:日常)',
     `description` varchar(255) NOT NULL COMMENT '描述',
     `rewards` json DEFAULT NULL COMMENT '奖励JSON',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     PRIMARY KEY (`id`),
     KEY `idx_type` (`type`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';
   ```

10. 任务进度表(quest_progress)
    
    ```sql
    CREATE TABLE `quest_progress` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `character_id` int(11) NOT NULL COMMENT '角色ID',
     `quest_id` bigint(20) NOT NULL COMMENT '任务ID',
     `progress` int(11) NOT NULL DEFAULT 0 COMMENT '进度',
     `status` tinyint(4) NOT NULL COMMENT '状态(0:进行中 1:已完成 2:已失败)',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     KEY `idx_character_id` (`character_id`),
     KEY `idx_quest_id` (`quest_id`),
     KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务进度表';
    ```

## 第四章 配置系统设计

### 4.1 配置文件结构

#### 3.1.1 目录结构

- config/
  - system/
    - cache.yml # 缓存配置
    - mq.yml # 消息队列配置
    - command.yml # 命令别名和限流配置
  - world/
    - maps.yml # 地图基础信息配置
    - map_refresh.yml # 地图刷怪配置
    - npcs.yml # NPC基础信息配置
    - shops.yml # NPC商店配置
    - drops.yml # 怪物掉落配置
    - collect.yml # 地图采集配置
    - treasure.yml # 宝箱道具配置
  - game/
    - levels.yml # 等级称号升级配置
    - newbie.yml # 新手初始化配置
    - battle.yml # 战斗系统配置
    - upgrade.yml # 装备升品和玉佩浣灵配置
    - trade.yml # 交易税收配置
    - suit.yml   # 装备套装例外加成属性配置

#### 3.1.2 详细配置文件结构示例

##### ******* system/command.yml - 命令别名和限流配置

```yaml
# 基于需求文档4.1命令别名配置
commands:
  # 查看类命令
  view:
    aliases: ["查看", "看", "状态"]
    description: "查看角色状态、装备、背包等信息"
    rate_limit:
      seconds: 1
      max_count: 3
      message: "查看信息过于频繁，请稍作休息 👀"

  # 移动类命令
  move:
    aliases: ["前往", "去", "移动", "跑到"]
    description: "移动到指定地点或坐标"
    rate_limit:
      seconds: 5
      max_count: 1
      message: "移动需要时间，请稍等片刻 🏃‍♂️"

  # 战斗类命令
  attack:
    aliases: ["攻击", "打", "杀", "砍"]
    description: "攻击指定目标"
    rate_limit:
      seconds: 3
      max_count: 1
      message: "少侠功夫了得，手脚太快了，请停下来休息下喝杯龙井茶 🍵"

  # 采集类命令
  collect:
    aliases: ["采集", "采桑", "采茶", "钓鱼", "挖矿"]
    description: "在指定区域进行采集活动"
    rate_limit:
      seconds: 3
      max_count: 1
      message: "采集需要耐心，请稍作休息 🌿"

  # 物品类命令
  use:
    aliases: ["使用", "服用", "喝"]
    description: "使用指定物品"
    rate_limit:
      seconds: 2
      max_count: 2
      message: "使用物品需要时间消化 💊"

  equip:
    aliases: ["装备", "穿", "戴"]
    description: "装备指定物品"
    rate_limit:
      seconds: 2
      max_count: 2
      message: "装备更换需要时间 🎽"
```

##### ******* game/newbie.yml - 新手初始化配置

```yaml
# 基于需求文档4.2新手初始化配置
newbie:
  # 保护等级配置
  protection:
    end_level: 10  # 保护期结束等级
    attribute_bonus:
      # 保护期属性增加
      defense_w: 100  # 物防增加100
      defense_m: 100  # 法防增加100
      defense_f: 100  # 佛防增加100
      attack_w: 10    # 物攻增加10
      attack_m: 10    # 法攻增加10
      attack_f: 10    # 佛攻增加10

  # 初始化资源
  initial_resources:
    gold: 1000  # 初始化金币数量
    silver: 0   # 初始化银两数量

  # 初始化职业武器(基于数据库item.sql)
  initial_weapons:
    1:  # 剑客
      item_no: "ITEM_018"  # 使用数据库道具编号
      position: 1  # 装备位置
      degree: 0    # 品级
    2:  # 仙师
      item_no: "ITEM_019"  # 使用数据库道具编号
      position: 1
      degree: 0
    3:  # 圣僧
      item_no: "ITEM_020"  # 使用数据库道具编号
      position: 1
      degree: 0
```

##### 3.1.2.3 game/levels.yml - 等级称号升级配置

```yaml
# 基于需求文档4.3角色升级属性配置
levels:
  # 1级角色基础属性
  base_attributes:
    swordsman:  # 剑客
      hp: 20
      mp: 10
      attack_m: 0
      attack_w: 10
      attack_f: 0
      defense_w: 10
      defense_m: 0
      defense_f: 0
      base_exp: 100
    mage:  # 仙师
      hp: 10
      mp: 20
      attack_m: 10
      attack_w: 0
      attack_f: 0
      defense_w: 0
      defense_m: 10
      defense_f: 0
      base_exp: 100
    monk:  # 圣僧
      hp: 15
      mp: 15
      attack_m: 0
      attack_w: 0
      attack_f: 10
      defense_w: 0
      defense_m: 0
      defense_f: 10
      base_exp: 100

  # 升级属性配置
  level_ranges:
    - range: [1, 10]
      hp_mp_increase: 0.20    # 血量法气每级增加20%
      other_increase: 0.08    # 其他属性每级增加8%
    - range: [11, 20]
      hp_mp_increase: 0.15    # 血量法气每级增加15%
      other_increase: 0.04    # 其他属性每级增加4%
    - range: [21, 40]
      hp_mp_increase: 0.10    # 血量法气每级增加10%
      other_increase: 0.02    # 其他属性每级增加2%
    - range: [41, 60]
      hp_mp_increase: 0.05    # 血量法气每级增加5%
      other_increase: 0.015   # 其他属性每级增加1.5%
    - range: [61, 999]
      hp_mp_increase: 0.03    # 血量法气每级增加3%
      other_increase: 0.01    # 其他属性每级增加1%
  # 升级经验计算配置
  level_exp:
    # 经验计算公式：下级经验 = (上级经验 * 1.035) + (当前等级 * 800)
    formula: "(prev_exp * 1.035) + (current_level * 800)"
    base_exp: 100  # 1级基础经验
    multiplier: 1.035  # 经验增长倍数
    level_factor: 800  # 等级影响因子

  # 等级称号配置
  titles:
    - range: [1, 9]
      title: "初出茅庐"
    - range: [10, 29]
      title: "江湖新手"
    - range: [30, 59]
      title: "江湖有名"
    - range: [60, 99]
      title: "武林高手"
    - range: [100, 999]
      title: "西湖神话"
```

##### ******* world/maps.yml - 地图基础信息配置

```yaml
# 基于需求文档4.4地图配置
maps:
  # 西湖十景地图配置
  duanqiao:
    map_id: "duanqiao"
    name: "断桥残雪"
    description: "西湖十景之一，白蛇传说的发源地，断桥上雪花纷飞，美不胜收"
    coordinates: {x: 0, y: 0}
    connections: ["sudi", "huagang"]  # 使用地图ID
    environment_image: "/images/maps/duanqiao/"
    main_image: "/images/maps/duanqiao/main.jpg"
    monsters:["MONSTER_001", "MONSTER_002"]  # 使用怪物编号
    npcs: ["xuxian"]  # 使用NPC编号
    collect_areas:
      - type: "采桑"
        item_no: "ITEM_012"  # 使用道具编号
        success_rate: 0.8
      - type: "采茶"
        item_no: "ITEM_013"  # 使用道具编号
        success_rate: 0.6

  sudi:
    map_id: "sudi"
    name: "苏堤春晓"
    description: "苏东坡修建的长堤，春天柳絮飞舞，是踏青的好去处"
    coordinates: {x: -1, y: 0}
    connections: ["duanqiao", "leifeng"]  # 使用地图ID
    environment_image: "/images/maps/sudi/"
    main_image: "/images/maps/sudi/main.jpg"
    monsters: ["MONSTER_003", "MONSTER_004"]  # 使用怪物编号
    npcs: ["sudongpo"]  # 使用NPC编号
    collect_areas:
      - type: "钓鱼"
        item_no: "ITEM_014"  # 使用道具编号
        success_rate: 0.7

  leifeng:
    map_id: "leifeng"
    name: "雷峰夕照"
    description: "雷峰塔巍峨耸立，夕阳西下时金光闪闪，传说白蛇被镇压于此"
    coordinates: {x: -1, y: -1}
    connections: ["sudi"]  # 使用地图ID
    environment_image: "/images/maps/leifeng/"
    main_image: "/images/maps/leifeng/main.jpg"
    monsters: ["MONSTER_005", "MONSTER_006"]  # 使用怪物编号
    npcs: ["fahai", "bainianzi"]  # 使用NPC编号
    collect_areas:
      - type: "挖矿"
        item_no: "ITEM_015"  # 使用道具编号
        success_rate: 0.3
```

##### ******* world/map_refresh.yml - 地图刷怪配置

```yaml
# 基于数据库monster.sql的怪物刷新配置
map_refresh:
  # 断桥残雪刷怪配置
  duanqiao:
    map_id: "duanqiao"
    map_name: "断桥残雪"
    refresh_points:
      - monster_no: "MONSTER_001"  # 对应数据库中的怪物编号
        position: {x: 0, y: 0}
        interval: 300  # 刷新间隔(秒)
        count: 3       # 刷新数量
      - monster_no: "MONSTER_002"
        position: {x: 1, y: 0}
        interval: 600
        count: 2

  # 苏堤春晓刷怪配置
  sudi:
    map_id: "sudi"
    map_name: "苏堤春晓"
    refresh_points:
      - monster_no: "MONSTER_003"
        position: {x: -1, y: 0}
        interval: 400
        count: 2
      - monster_no: "MONSTER_004"
        position: {x: -2, y: 0}
        interval: 800
        count: 1

  # 雷峰夕照刷怪配置
  leifeng:
    map_id: "leifeng"
    map_name: "雷峰夕照"
    refresh_points:
      - monster_no: "MONSTER_005"  # BOSS怪物
        position: {x: -1, y: -1}
        interval: 3600  # 1小时刷新一次
        count: 1
      - monster_no: "MONSTER_006"
        position: {x: 0, y: -1}
        interval: 900
        count: 2
```

##### ******* world/npcs.yml - NPC基础信息配置

```yaml
# 基于需求文档4.8 NPC配置
npcs:
  xuxian:
    npc_no: "xuxian"
    name: "许仙"
    description: "善良的药铺老板，精通医术"
    location: "duanqiao"  # 使用地图ID
    coordinates: {x: 0, y: 0}
    dialogue:
      - "欢迎来到许仙药铺，这里有最好的丹药！"
      - "白娘子...唉，往事不堪回首。"
      - "需要什么丹药吗？我这里应有尽有。"
    functions: ["shop", "quest"]  # 功能：商店、任务

  sudongpo:
    npc_no: "sudongpo"
    name: "苏东坡"
    description: "北宋文学家，西湖的建设者"
    location: "sudi"  # 使用地图ID
    coordinates: {x: -1, y: 0}
    dialogue:
      - "欲把西湖比西子，淡妆浓抹总相宜。"
      - "这苏堤是我主持修建的，你觉得如何？"
      - "文章千古事，得失寸心知。"
    functions: ["quest", "story"]  # 功能：任务、剧情

  bainianzi:
    npc_no: "bainianzi"
    name: "白娘子"
    description: "美丽的白蛇精，与许仙相恋"
    location: "leifeng"  # 使用地图ID
    coordinates: {x: -1, y: -1}
    dialogue:
      - "官人...我等了你千年..."
      - "法海那老和尚，拆散了我们！"
      - "只要心中有爱，人妖又如何？"
    functions: ["quest", "story"]
```

##### ******* world/shops.yml - NPC商店配置

```yaml
# 基于需求文档4.8商店配置和数据库item.sql
shops:
  xuxian_pharmacy:
    shop_id: "xuxian_pharmacy"
    name: "许仙药铺"
    npc_no: "xuxian"  # 使用NPC编号
    location: "duanqiao"  # 使用地图ID
    description: "许仙开设的药铺，专门出售各种丹药"
    items:
      ITEM_001:  # 使用数据库中的道具编号
        price: 50
        currency: "silver"  # silver/gold
        stock: -1  # -1表示无限库存
      ITEM_002:
        price: 30
        currency: "silver"
        stock: -1
    buy_rate: 1.0   # 购买价格倍率
    sell_rate: 0.5  # 出售价格倍率

  fahai_weapon:
    shop_id: "fahai_weapon"
    name: "法海武器店"
    npc_no: "fahai"  # 使用NPC编号
    location: "leifeng"  # 使用地图ID
    description: "法海经营的武器店，出售各种法器和武器"
    items:
      ITEM_003:  # 使用数据库中的道具编号
        price: 500
        currency: "silver"
        stock: 10
      ITEM_004:
        price: 400
        currency: "silver"
        stock: 8
    buy_rate: 1.0
    sell_rate: 0.6
```

##### ******* world/drops.yml - 怪物掉落配置

```yaml
# 基于数据库monster.sql和item.sql的掉落配置
drops:
  MONSTER_001:  # 使用数据库中的怪物编号
    monster_no: "MONSTER_001"
    drop_groups:
      - group_id: "common"
        rate: 0.8  # 80%概率掉落
        items:
          - item_no: "ITEM_005"  # 银两，使用数据库道具编号
            min: 10
            max: 30
            rate: 1.0
      - group_id: "materials"
        rate: 0.3  # 30%概率掉落材料
        items:
          - item_no: "ITEM_006"  # 材料1
            count: 1
            rate: 0.7
          - item_no: "ITEM_007"  # 材料2
            count: 1
            rate: 0.3

  MONSTER_002:  # 使用数据库中的怪物编号
    monster_no: "MONSTER_002"
    drop_groups:
      - group_id: "common"
        rate: 1.0
        items:
          - item_no: "ITEM_005"  # 银两
            min: 100
            max: 200
            rate: 1.0
          - item_no: "ITEM_008"  # 金币
            min: 1
            max: 3
            rate: 0.5
      - group_id: "equipment"
        rate: 0.4  # 40%概率掉落装备
        items:
          - item_no: "ITEM_009"  # 装备1
            count: 1
            rate: 0.3
          - item_no: "ITEM_010"  # 装备2
            count: 1
            rate: 0.7
      - group_id: "rare"
        rate: 0.1  # 10%概率掉落稀有物品
        items:
          - item_no: "ITEM_011"  # 稀有物品
            count: 1
            rate: 1.0
```

##### ******* game/battle.yml - 战斗系统配置

```yaml
# 基于需求文档4.10战斗配置
battle:
  # 职业克制关系
  class_restraint:
    1:  # 剑客
      name: "剑客"
      restrains: 3      # 克制圣僧
      restrained_by: 2  # 被仙师克制
      damage_bonus: 1.5 # 克制时伤害加成
      damage_reduction: 0.7 # 被克制时伤害减免
    2:  # 仙师
      name: "仙师"
      restrains: 1      # 克制剑客
      restrained_by: 3  # 被圣僧克制
      damage_bonus: 1.5
      damage_reduction: 0.7
    3:  # 圣僧
      name: "圣僧"
      restrains: 2      # 克制仙师
      restrained_by: 1  # 被剑客克制
      damage_bonus: 1.5
      damage_reduction: 0.7

  # 伤害计算公式
  damage_formula:
    base_damage: 10  # 基础伤害
    attack_factor: 0.1   # 攻击力影响因子
    defense_factor: 0.05 # 防御力影响因子
    level_factor: 0.02   # 等级差影响因子

  # 经验计算公式
  exp_formula:
    base_exp: 50     # 基础经验
    level_factor: 1.2    # 等级影响因子
    max_level_diff: 10   # 最大等级差
    min_exp_rate: 0.1    # 最小经验倍率

  # PVP配置
  pvp:
    exp_bonus: 2.0       # PVP经验加成
    evil_value_gain: 50  # 击杀玩家获得恶名值
    loot_rate: 0.3       # 掠夺概率
    protection_level: 10 # 新手保护等级

  # 复活配置
  revive:
    cost_type: "silver"  # 复活消耗类型
    base_cost: 100       # 基础复活费用
    level_factor: 10     # 等级影响因子
    max_cost: 1000       # 最大复活费用
```

##### ******** game/upgrade.yml - 装备升品和玉佩浣灵配置

```yaml
# 基于需求文档4.12升品配置
upgrade:
  # 装备升品配置
  equipment:
    # 1-3品升品配置(基于数据库item.sql)
    level_1_3:
      materials:
        ITEM_015: "level * 2"  # 所需灵石数量公式
        ITEM_005: "level * 100"      # 所需银两公式
      success_rate: 0.8            # 成功率
      attribute_increase: 0.1      # 属性提升比例

    # 4-6品升品配置
    level_4_6:
      materials:
        ITEM_015: "level * 3"  # 灵石
        ITEM_034: "level * 1"  # 精炼石
        ITEM_005: "level * 200"      # 银两
      success_rate: 0.6
      attribute_increase: 0.15

    # 7-9品升品配置
    level_7_9:
      materials:
        ITEM_015: "level * 5"  # 灵石
        ITEM_034: "level * 2"  # 精炼石
        ITEM_025: "level * 1"  # 稀有水晶
        ITEM_005: "level * 500"      # 银两
      success_rate: 0.4
      attribute_increase: 0.2

    # 10品以上升品配置
    level_10_plus:
      materials:
        ITEM_015: "level * 10"  # 灵石
        ITEM_034: "level * 5"   # 精炼石
        ITEM_025: "level * 3"   # 稀有水晶
        ITEM_035: "1"           # 神性精华
        ITEM_008: "level * 10"  # 金币
      success_rate: 0.2
      attribute_increase: 0.25

  # 玉佩浣灵配置
  jade_refine:
    # 1-3品浣灵配置(基于数据库item.sql)
    level_1_3:
      materials:
        ITEM_033: "level * 1"      # 月光石
        ITEM_005: "level * 50"     # 银两
      success_rate: 0.9
      attribute_range:
        min: 1.0    # 最小属性倍率
        max: 1.3    # 最大属性倍率

    # 4-6品浣灵配置
    level_4_6:
      materials:
        ITEM_033: "level * 2"  # 月光石
        ITEM_036: "level * 1"  # 星辰石
        ITEM_005: "level * 100"     # 银两
      success_rate: 0.7
      attribute_range:
        min: 1.1
        max: 1.5

    # 7-9品浣灵配置
    level_7_9:
      materials:
        ITEM_033: "level * 3"  # 月光石
        ITEM_036: "level * 2"  # 星辰石
        ITEM_037: "level * 1"  # 太阳石
        ITEM_005: "level * 200"     # 银两
      success_rate: 0.5
      attribute_range:
        min: 1.2
        max: 1.8

    # 10品以上浣灵配置
    level_10_plus:
      materials:
        ITEM_033: "level * 5"  # 月光石
        ITEM_036: "level * 3"  # 星辰石
        ITEM_037: "level * 2"  # 太阳石
        ITEM_038: "1"          # 神玉
        ITEM_008: "level * 5"  # 金币
      success_rate: 0.3
      attribute_range:
        min: 1.5
        max: 2.5
```

##### *******1 suit.yml - 装备套装例外加成属性配置

TODO 补充

##### *******2 game/trade.yml - 交易配置

```yaml
# 基于需求文档4.13交易配置
trade:
  # 交易税收配置
  tax:
    rate: 0.1           # 10%税收
    min_amount: 10      # 最小税收金额
    currency: "silver"  # 税收货币类型

  # 交易限制配置
  limits:
    max_items: 10       # 单次交易最大物品数量
    timeout: 300        # 交易超时时间(秒)
    min_level: 5        # 最小交易等级

  # 安全机制配置
  security:
    confirm_time: 10    # 确认时间(秒)
    cancel_penalty: 50  # 取消交易惩罚(银两)
    blacklist_time: 3600 # 恶意取消交易黑名单时间(秒)
```

##### *******3 world/collect.yml - 地图采集配置

```yaml
# 基于需求文档4.14采集配置
collect:
  # 钓鱼配置
  fishing:
    locations:
      - map: "sudi"  # 使用地图ID
        success_rate: 0.7
        items:
          - item_no: "ITEM_014"  # 使用数据库道具编号
            rate: 0.6
            min: 1
            max: 3
          - item_no: "ITEM_021"  # 稀有鱼类
            rate: 0.1
            min: 1
            max: 1
      - map: "huagang"  # 使用地图ID
        success_rate: 0.5
        items:
          - item_no: "ITEM_016"  # 使用数据库道具编号
            rate: 0.4
            min: 1
            max: 2
          - item_no: "ITEM_022"  # 金鱼
            rate: 0.05
            min: 1
            max: 1
    time_cost: 3        # 采集耗时(秒)
    exp_reward: 10      # 经验奖励

  # 采桑配置
  mulberry:
    locations:
      - map: "duanqiao"  # 使用地图ID
        success_rate: 0.8
        items:
          - item_no: "ITEM_012"  # 使用数据库道具编号
            rate: 0.8
            min: 2
            max: 5
          - item_no: "ITEM_023"  # 蚕宝宝
            rate: 0.1
            min: 1
            max: 1
    time_cost: 2
    exp_reward: 8

  # 采茶配置
  tea:
    locations:
      - map: "duanqiao"  # 使用地图ID
        success_rate: 0.6
        items:
          - item_no: "ITEM_013"  # 使用数据库道具编号
            rate: 0.7
            min: 1
            max: 3
          - item_no: "ITEM_024"  # 优质茶叶
            rate: 0.2
            min: 1
            max: 1
    time_cost: 3
    exp_reward: 12

  # 挖矿配置
  mining:
    locations:
      - map: "leifeng"  # 使用地图ID
        success_rate: 0.3
        items:
          - item_no: "ITEM_015"  # 使用数据库道具编号
            rate: 0.5
            min: 1
            max: 2
          - item_no: "ITEM_025"  # 稀有水晶
            rate: 0.1
            min: 1
            max: 1
    time_cost: 5
    exp_reward: 20

  # 采集汇总配置
  summary:
    time_window: 30     # 汇总时间窗口(秒)
    min_players: 2      # 最小汇总玩家数
    message_template: "在过去30秒内，{player_count}位少侠在{location}进行了{action}，共获得了{items}"
```

##### *******4 world/treasure.yml - 宝箱道具配置

```yaml
# 基于需求文档4.15宝箱配置
treasure:
  # 神奇盒子配置(基于数据库item.sql)
  magic_box:
    item_no: "ITEM_026"  # 使用数据库道具编号
    cost: 100           # 开启消耗(金币)
    rewards:
      - group: "common"
        rate: 0.6       # 60%概率
        items:
          - item_no: "ITEM_001"  # 回血丹
            count: 5
            rate: 0.4
          - item_no: "ITEM_002"  # 回蓝丹
            count: 5
            rate: 0.4
          - item_no: "ITEM_005"  # 银两
            min: 100
            max: 500
            rate: 0.2

      - group: "rare"
        rate: 0.3       # 30%概率
        items:
          - item_no: "ITEM_015"  # 灵石
            count: 3
            rate: 0.5
          - item_no: "ITEM_027"  # 玉佩
            count: 1
            rate: 0.3
          - item_no: "ITEM_025"  # 稀有水晶
            count: 1
            rate: 0.2

      - group: "epic"
        rate: 0.1       # 10%概率
        items:
          - item_no: "ITEM_028"  # 神兵
            count: 1
            rate: 0.3
          - item_no: "ITEM_029"  # 传说护甲
            count: 1
            rate: 0.3
          - item_no: "ITEM_008"  # 金币
            min: 10
            max: 50
            rate: 0.4

  # 宝藏箱配置
  treasure_chest:
    item_no: "ITEM_030"  # 使用数据库道具编号
    cost: 0             # 免费开启
    rewards:
      - group: "equipment"
        rate: 0.7
        items:
          - item_no: "ITEM_031"  # 银剑
            count: 1
            rate: 0.3
          - item_no: "ITEM_032"  # 皮甲
            count: 1
            rate: 0.4
          - item_no: "ITEM_027"  # 玉佩
            count: 1
            rate: 0.3

      - group: "materials"
        rate: 0.3
        items:
          - item_no: "ITEM_015"  # 灵石
            count: 5
            rate: 0.6
          - item_no: "ITEM_033"  # 月光石
            count: 2
            rate: 0.4
```

### 3.2 配置文件关联关系

配置文件之间的关联关系如下：

1. **maps.yml** ↔ **map_refresh.yml**: 地图中刷怪关联
2. **maps.yml** ↔ **npcs.yml**: 地图中的NPC列表关联NPC配置
3. **maps.yml** ↔ **collect.yml**: 地图中的采集区域关联采集配置
4. **monster数据库表** ↔ **drops.yml**: 怪物编号关联掉落配置
5. **npcs.yml** ↔ **shops.yml**: NPC关联商店配置
6. **items数据库表** ↔ **upgrade.yml**: 道具类型关联升品配置
7. **skill数据库表** ↔ **battle.yml**: 技能威力关联战斗配置
8. **item数据库表** ↔ **treasure.yml**: 宝箱奖励关联道具配置
9. **newbie.yml** ↔ **item数据库表**: 新手装备关联道具配置
10. **levels.yml** ↔ **battle.yml**: 等级属性关联战斗计算

### 3.3 配置验证规则

1. **引用完整性验证**
   
   - 确保所有item_no引用在item表中存在
   - 确保所有monster_no引用在monster表中存在
   - 确保所有skill_no引用在skills表中存在
   - 确保所有npc_no引用在npcs.yml中存在
   - 确保地图间连接关系的双向一致性

2. **数值合理性验证**
   
   - 概率值必须在0-1之间
   - 数量、等级、属性值必须为正整数
   - 坐标值必须为整数
   - 时间值必须为正整数

3. **逻辑一致性验证**
   
   - 等级限制不能超过游戏最大等级
   - 职业限制值必须在有效范围内(0-3)
   - 装备子类型与类型必须匹配
   - 技能类型与职业限制必须对应

4. **格式正确性验证**
   
   - YAML格式语法正确
   - 必需字段完整性检查
   - 字段类型正确性检查
   - 嵌套结构完整性检查

5. **业务规则验证**
   
   - 新手保护等级不能超过最大等级
   
   - 升品材料需求必须合理
   
   - 商店价格必须为正数
   
   - 任务条件必须可完成
     
     ```
     
     ```

### 3.2配置加载机制

1. 启动加载流程
   
   - 系统启动时按优先级加载配置文件
   - 配置文件加载失败时系统启动失败
   - 配置数据校验失败时系统启动失败

2. 缓存机制
   
   - 所有配置加载到内存中

3. 配置校验
   
   - 格式校验：YAML格式正确性
   - 数据校验：必填字段、数据类型、取值范围
   - 关联校验：配置间依赖关系

4. 依赖管理
   
   - 显式声明配置间依赖关系
   - 按依赖顺序加载配置
   - 依赖配置变更时联动更新

## 第五章 核心功能模块设计

### 5.1 命令引擎设计

1. 命令解析机制
   
   1. 命令格式
      
      - 游戏模式命令："!"开头（如：!游戏模式、!退出游戏模式）
      - 标准命令：操作词 + 操作对象 [@目标]
      - 示例：
        - 查看状态 @张三
        - 前往雷峰塔
        - 攻击 白蛇
   
   2. 命令分类
      
      - 查看类：状态、地图、装备、背包、排名、任务
      - 移动类：前往+地点/坐标/目标
      - 战斗类：攻击、施放技能
      - 物品类：使用、装备、存取
      - 交易类：买卖、交易邀请
      - 挂机类：挖矿、钓鱼、采集
      - 任务类：接收任务，提交任务，取消任务
      - 管理类：创建区服等

2. 命令处理机制
   
   1. 命令预处理
      
      - 去除多余空格
      - 分割命令组件（操作词、对象、目标）
      - 识别命令类型
   
   2. 命令验证
      
      - 格式验证
      - 权限验证
      - 状态验证（如：死亡状态、战斗状态）
   
   3. 命令执行
      
      - 调用对应业务模块
      - 更新游戏状态
      - 生成反馈消息

3. 消息反馈机制
   
   1. 标准文本格式
      
      ```
      ╭──────────╮
       [消息类型]
       [主要内容]
       [额外信息]
      ╰──────────╯
      ```
   
   2. 完整消息模板
      
      - 移动消息：
        
        ```
        🏃‍♂️ @玩家 到达[地点]
        🗺️ 环境：[环境描述]
        💠 可选操作：
        1. 采集/打怪
        2. 查看环境
        3. 进入副本（如果有）
        ```
      
      - 战斗消息：
        
        ```
        ⚔️ 战斗播报 ⚔️
        @玩家 → 目标
        伤害：[数值] | 剩余：[血量]
        ```
      
      - 击杀消息：
        
        ```
        系统：☠️ 击杀播报！
        @玩家 击杀 [怪物]
        ╭──────────╮
          经验 +100
          金币 +500
          获得：
          🎽 [装备名]
          💎 [材料]×2
        ╰──────────╯
        ```
      
      - PVP击杀消息：
        
        ```
        系统：⚔️ PVP击杀！
        @玩家A 击杀 @玩家B
        ╭──────────╮
          经验 +200
          江湖恶名 +50
          掠夺：
          🎽 [装备]
          💰 金币×1000
        ╰──────────╯
        ```
      
      - 采集汇总消息：
        
        ```
        系统：📦 30秒内采集汇总 📦
        @玩家A：雨花石×2 | 天蚕丝×1
        @玩家B：雨花石×1
        @玩家C：获得：天外陨石×1 🌟稀有!
        ```
      
      - 任务消息：
        
        ```
        📋 任务更新
        ✅ 已完成：[任务名称]
        🎁 奖励：经验+500，金币+1000
        📝 新任务：[新任务名称]
        ```
   
   3. Emoji规范
      
      - 🏃‍♂️ 移动相关
      - ⚔️ 战斗相关
      - 📦 物品相关
      - 💠 操作选项
      - 🌟 稀有/特殊
      - ☠️ 击杀相关
      - 💰 金币/财物
      - 🎽 装备相关
      - ❌ 错误提示
      - ✅ 成功提示

4. 防刷控制机制
   
   1. 频率限制配置
      
      ```yaml
      # command.yml 命令别名配置文件
      commands:
        attack:
          aliases: ["攻击", "打", "杀", "砍"]
          rate_limit:
            seconds: 5      # 时间窗口
            max_count: 1    # 最大次数
            message: "少侠功夫了得，手脚太快了，请停下来休息下喝杯龙井茶 🍵"
      
        collect:
          aliases: ["采集", "采桑", "采茶"]
          rate_limit:
            seconds: 3
            max_count: 1
            message: "采集需要耐心，请稍作休息 🌿"
      ```
   
   2. 内存统计机制
      
      ```json
      {
        "user_id": "用户ID",
        "command_stats": {
          "attack": {
            "window_start": "时间窗口开始时间戳",
            "count": "当前窗口内执行次数",
            "last_execute": "最后执行时间"
          },
          "collect": {
            "window_start": "时间窗口开始时间戳",
            "count": "当前窗口内执行次数",
            "last_execute": "最后执行时间"
          }
        }
      }
      ```
   
   3. 限流判断逻辑
      
      ```java
      public boolean checkRateLimit(String userId, String command) {
          CommandConfig config = getCommandConfig(command);
          if (config.getRateLimit() == null) {
              return true; // 无限制
          }
      
          UserCommandStats stats = getUserStats(userId);
          CommandStats cmdStats = stats.getCommandStats(command);
      
          long now = System.currentTimeMillis();
          long windowStart = cmdStats.getWindowStart();
          int maxCount = config.getRateLimit().getMaxCount();
          int seconds = config.getRateLimit().getSeconds();
      
          // 检查是否需要重置时间窗口
          if (now - windowStart > seconds * 1000) {
              cmdStats.reset(now);
              return true;
          }
      
          // 检查是否超过限制
          if (cmdStats.getCount() >= maxCount) {
              return false; // 超过限制
          }
      
          return true;
      }
      ```

5. 错误处理
   
   1. 错误类型
      
      - 命令格式错误
      - 权限不足
      - 状态限制
      - 目标无效
      - 频率限制
   
   2. 错误提示格式
      
      ```
      ❌ 错误：[错误描述]
      💡 提示：[操作建议]
      ```

6. 命令处理流程
   
   ```
   1. 接收命令文本
   2. 预处理（分割、识别）
   3. 验证（格式、权限、状态）
   4. 执行命令
   5. 更新状态
   6. 返回反馈
   ```

### 4.2 游戏核心模块设计

#### 4.2.1 地图系统

1. 地图状态管理
   
   1. 地图数据结构
      
      ```
      {
        "map_id": "地图ID",
        "name": "地图名称",
        "description": "地图描述",
        "monsters": [怪物列表],
        "collect_areas": [采集区域],
        "fishing_areas": [钓鱼区域]
      }
      ```
   
   2. 场景管理
      
      - 场景文字描述
      - 场景状态记录
      - 场景切换处理
   
   3. 区域管理
      
      - 采集区域标记
      - 钓鱼区域标记
      - 区域文字描述

2. 地图元素管理
   
   1. 怪物管理
      
      - 根据配置生成怪物列表
      - 怪物刷新机制
      - 怪物状态维护
      - 掉落物生成
   
   2. 采集区域管理
      
      - 采集结果随机生成
      - 采集冷却控制
   
   3. 钓鱼区域管理
      
      - 钓鱼结果随机生成
      - 钓鱼冷却控制

3. 地图消息管理
   
   1. 状态展示
      
      - 地图描述消息
      - 场景描述消息
      - 怪物信息消息
      - 区域信息消息
   
   2. 数据管理
      
      - 地图数据存储
      - 怪物数据存储
      - 玩家位置记录
   
   3. 消息推送
      
      - 场景变化消息
      - 怪物状态消息
      - 采集结果消息
      - 钓鱼结果消息

#### 4.2.2 角色系统

1. 角色属性管理
   
   1. 基础属性
      
      - 等级经验
      - 生命法力
      - 攻击防御
      - 战斗属性
   
   2. 状态属性
      
      - 角色状态
      - 增益效果
      - 减益效果
   
   3. 属性计算
      
      - 基础属性计算
      - 装备属性加成
      - 技能属性加成
      - 状态效果计算

2. 角色行为管理
   
   1. 移动行为
      
      - 地图切换
      - 场景切换
      - 位置记录
   
   2. 战斗行为
      
      - 攻击指令处理
      - 技能指令处理
      - 战斗结果计算
   
   3. 交互行为
      
      - 物品使用
      - 装备操作
      - NPC交互
      - 玩家交易

3. 角色数据管理
   
   1. 数据存储
      
      - 基础信息存储
      - 状态信息存储
      - 物品数据存储
      - 任务数据存储
   
   2. 状态管理
      
      - 角色状态维护
      - 实时状态更新
      - 状态消息推送
   
   3. 消息管理
      
      - 状态查询消息
      - 属性变更消息
      - 物品清单消息
      - 位置信息消息

#### 4.2.3 战斗系统

1. 战斗机制设计
   
   ```
   1. 战斗触发
      - 距离检查：按职业不同攻击距离
        - 剑客：近战攻击距离5米
        - 仙师：远程法术攻击距离15米
        - 圣僧：中距离佛法攻击距离8米
      - 状态检查：死亡/禁锢等状态无法发起战斗
      - 冷却检查：普通攻击5秒，技能攻击按技能配置
   
   2. 攻击方式
      - 普通攻击：基础伤害，5秒冷却
      - 技能攻击：技能伤害，10秒冷却，消耗法力值
   ```

2. 伤害计算
   
   ```
   1. 攻击力计算
      - 基础攻击：物理/法术/佛法
      - 装备加成：武器/防具属性
      - 技能加成：技能倍率
      - 状态加成：buff效果
   
   2. 防御力计算
      - 基础防御：物防/法防/佛防
      - 装备防御：防具属性
      - 技能防御：防御技能
      - 状态防御：buff效果
   
   3. 反伤计算
      - 基础反伤：装备提供
      - 技能反伤：技能效果
      - 最终反伤 = min(反伤系数 * 受到伤害, 最大反伤值)
   ```

3. 死亡处理
   
   ```
   1. 怪物死亡
      - 掉落物品：按配置概率，多人攻击各自独立结算掉落
      - 经验结算：等级差计算，每个参与者独立获得经验
      - 刷新控制：地图怪物数量维护
   
   2. 玩家死亡
      - 随机掉落：身上/背包随机掉落，概率可配置
      - 状态更新：死亡状态标记
      - 记录死亡：时间/地点/原因
   ```

4. 数据结构
   
   ```json
   1. 战斗状态
   {
     "battle_id": "战斗ID",
     "attacker": {
       "user_id": "攻击者ID",
       "attack_type": "攻击类型",
       "skill_id": "技能ID",
       "position": {"x": 0, "y": 0}
     },
     "defender": {
       "target_id": "目标ID",
       "target_type": "目标类型",
       "position": {"x": 0, "y": 0}
     },
     "status": "战斗状态",
     "last_attack_time": "上次攻击时间"
   }
   
   2. 伤害结果
   {
     "damage": "最终伤害",
     "is_critical": "是否暴击",
     "real_damage": "实际伤害",
     "reflect_damage": "反伤伤害",
     "kill": "是否击杀",
     "drop_items": ["掉落物品列表"]
   }
   ```

5. 实现接口
   
   ```java
   // 发起攻击
   Result attack(String userId, String targetId, String skillId);
   
   // 计算伤害
   DamageResult calculateDamage(BattleContext context);
   
   // 处理死亡
   void handleDeath(String targetId, DamageResult result);
   
   // 获取战斗状态
   BattleStatus getBattleStatus(String userId);
   ```

#### 4.2.4 物品系统

1. 物品管理
   
   1. 物品分类
      
      - 装备类：武器、防具、饰品
      - 消耗品：药品、材料
      - 任务物品
      - 特殊物品
   
   2. 物品属性
      
      - 基础属性
      - 特殊效果
      - 使用限制
      - 交易限制
   
   3. 物品操作
      
      - 获得物品
      - 使用物品
      - 丢弃物品
      - 交易物品

2. 物品存储管理
   
   1. 物品列表管理
      
      - 身上物品列表
      - 背包物品列表
      - 仓库物品列表
   
   2. 装备管理
      
      - 装备穿戴记录
      - 装备属性计算
      - 套装效果计算
   
   3. 物品流转
      
      - 任务奖励获取
      - 怪物掉落获取
      - 交易流转（玩家之间、NPC之间）
      - 使用消耗
      - 系统回收

3. 装备系统
   
   1. 装备强化
      
      - 升品机制
      - 材料消耗
      - 成功率计算
      - 失败处理
   
   2. 玉佩浣灵
      
      - 浣灵机制
      - 材料消耗
      - 属性提升
   
   3. 套装效果
      
      - 套装识别
      - 套装属性
      - 效果叠加

### 4.3 消息系统设计

#### 4.3.1 消息队列设计

1. 消息分类
   
   1. 系统消息
      
      - 系统公告
      - 错误提示
      - 状态变更通知
   
   2. 游戏消息
      
      - 战斗消息
      - 物品消息
      - 状态消息
      - 场景消息
   
   3. 交互消息
      
      - 指令响应
      - 查询结果
      - 操作反馈

2. 消息队列管理
   
   1. 队列分级
      
      - 高优先级队列：系统消息、错误提示
      - 中优先级队列：战斗消息、状态变更
      - 低优先级队列：场景描述、查询结果
   
   2. 消息调度
      
      - 优先级调度
      - 批量处理
      - 消息合并
      - 延迟发送
   
   3. 队列监控
      
      - 队列长度监控
      - 处理延迟监控
      - 错误率监控
      - 性能监控

#### 4.3.2 消息处理机制

1. 消息生成
   
   1. 消息格式
      
      ```
      {
        "type": "消息类型",
        "target": "目标用户",
        "content": "消息内容",
        "priority": "优先级",
        "timestamp": "时间戳"
      }
      ```
   
   2. 内容生成
      
      - 模板填充
      - 变量替换
      - 格式美化
      - 长度控制
   
   3. 消息装饰
      
      - 边框装饰
      - 分隔符
      - 重要标记
      - 分类标记

2. 消息处理
   
   1. 消息过滤
      
      - 重复消息过滤
      - 频率限制
      - 内容过滤
      - 优先级过滤
   
   2. 消息合并
      
      - 相同类型合并
      - 相同目标合并
      - 时间窗口合并
      - 内容智能合并
   
   3. 消息转换
      
      - 格式转换
      - 编码转换
      - 长度截断
      - 特殊字符处理

3. 消息存储
   
   1. 临时存储
      
      - 内存队列
      - 本地缓存
      - 消息缓冲
   
   2. 持久化存储
      
      - 重要消息存储
      - 系统消息存储
      - 错误消息存储
   
   3. 存储策略
      
      - 过期清理
      - 容量控制
      - 备份策略
      - 恢复机制

#### 4.3.3 消息推送机制

1. 推送策略
   
   1. 实时推送
      
      - 重要消息
      - 战斗消息
      - 错误提示
      - 状态变更
   
   2. 批量推送
      
      - 场景描述
      - 物品信息
      - 查询结果
      - 系统公告
   
   3. 延迟推送
      
      - 非重要消息
      - 统计信息
      - 定时通知
      - 周期消息

2. 推送优化
   
   1. 消息合并
      
      - 合并规则
      - 合并阈值
      - 合并优先级
      - 合并模板
   
   2. 流量控制
      
      - 频率限制
      - 批量大小
      - 队列容量
      - 超时处理
   
   3. 失败处理
      
      - 重试机制
      - 降级策略
      - 错误恢复
      - 异常通知

3. 推送监控
   
   1. 性能监控
      
      - 推送延迟
      - 队列长度
      - 处理速率
      - 资源占用
   
   2. 质量监控
      
      - 推送成功率
      - 消息丢失率
      - 重试次数
      - 异常统计
   
   3. 告警机制
      
      - 阈值告警
      - 异常告警
      - 容量告警
      - 性能告警

## 第六章 具体实现方案

### 6.1 关键玩法实现

#### 5.1.1 新手引导系统

1. 初始化流程
   
   ```
   1. 检查用户是否已创建角色
   2. 如未创建，执行初始化
      - 读取新手配置文件
      - 创建角色基础数据
      - 分配初始物品和金币
      - 设置新手保护属性
   3. 如已创建，跳过初始化
   ```

2. 数据结构
   
   1. 新手配置(新手初始化配置)
      
      ```json
      {
        "init_items": [
          {
            "item_id": "物品ID",
            "count": "数量"
          }
        ],
        "init_gold": "初始金币数",
        "protect_attrs": {
          "protect_level": 10,  // 保护等级，达到此等级取消保护
          "bonus_defense": "额外防御"  // 新手保护期间的额外防御加成
        },
        "guide_text": "新手引导文案"
      }
      ```
   
   2. 角色初始数据
      
      ```json
      {
        "user_id": "用户ID",
        "role_data": {
          "status": "活跃",
          "map_id": "初始地图",
          "position": {
            "x": 0,
            "y": 0
          },
          "level": 1,
          "exp": 0,
          "gold": "初始金币",
          "silver": 0
        }
      }
      ```

3. 实现接口
   
   1. 角色创建
      
      ```java
      Result createRole(String userId, String roleType);
      ```
   
   2. 新手引导
      
      ```java
      String getGuideText(String userId);
      ```
   
   3. 保护属性
      
      ```java
      boolean checkProtectStatus(String userId);
      ```

#### 5.1.2 地图行走系统

1. 移动机制设计
   
   1. 移动类型
      
      ```
      1. 地点移动：前往+地点名
         - 检查地点是否存在
         - 计算移动距离和时间
         - 执行移动
      
      2. 精准移动：前往+坐标
         - 验证坐标合法性
         - 计算移动距离和时间
         - 执行移动
      
      3. 模糊移动：前往+地图目标编号
         - 查找目标位置
         - 计算目标10米范围内随机位置
         - 执行移动
      ```
   
   2. 移动速度
      
      ```
      1. 普通行走
         - 基础速度：1米/秒
         - 受地形影响：
           平地：100%
           山地：70%
           水域：50%
      
      2. 御剑飞行（特殊状态）
         - 基础速度：2米/秒
         - 不受地形影响
         - 需要消耗法力值
      ```

2. 距离计算
   
   1. 坐标系统
      
      ```json
      {
        "position": {
          "x": 0,          // X坐标
          "y": 0,          // Y坐标
          "map_id": "m1",  // 地图ID
          "area_id": "a1"  // 区域ID
        }
      }
      ```
   
   2. 距离计算
      
      ```java
      // 曼哈顿距离计算
      distance = |x1 - x2| + |y1 - y2|
      
      // 移动时间计算（秒）
      moveTime = distance / (baseSpeed * terrainFactor)
      ```

3. 移动处理
   
   1. 移动验证
      
      ```
      1. 状态检查
         - 是否死亡
         - 是否在战斗
         - 是否被禁锢
      
      2. 目标检查
         - 目标地点是否存在
         - 目标坐标是否合法
         - 是否有权限进入
      
      3. 距离检查
         - 计算移动距离
         - 检查体力是否足够
      ```
   
   2. 移动执行
      
      ```
      1. 更新状态
         - 设置移动状态
         - 记录起点终点
         - 计算到达时间
      
      2. 移动完成
         - 更新最终位置
         - 清除移动状态
         - 返回地图描述
      ```

4. 地图描述
   
   ```
   1. 基础描述
      - 地点名称
      - 地图描述（包含场景描述）
      - 当前状态（在场人物、当前怪物、可采集物等）
   
   2. 命令提示
      - 可执行的命令提示（如：可以在此地点钓鱼、采集、战斗等）
   ```

5. 实现接口
   
   1. 移动指令
      
      ```java
      // 地点移动
      Result moveToLocation(String userId, String location);
      
      // 坐标移动
      Result moveToPosition(String userId, int x, int y);
      
      // 相对移动
      Result moveToTarget(String userId, String target, String direction);
      ```
   
   2. 状态查询
      
      ```java
      // 获取当前位置
      Position getCurrentPosition(String userId);
      
      // 获取地图描述
      MapDescription getMapDescription(String userId);
      ```

#### 5.1.3 战斗系统

1. 战斗机制设计
   
   ```
   1. 战斗触发
      - 距离检查：按职业不同攻击距离
        - 剑客：近战攻击距离5米
        - 仙师：远程法术攻击距离15米
        - 圣僧：中距离佛法攻击距离8米
      - 状态检查：死亡/禁锢等状态无法发起战斗
      - 冷却检查：普通攻击5秒，技能攻击按技能配置
   
   2. 攻击方式
      - 普通攻击：基础伤害，5秒冷却
      - 技能攻击：技能伤害，10秒冷却，消耗法力值
   ```

2. 伤害计算
   
   ```
   1. 攻击力计算
      - 基础攻击：物理/法术/佛法
      - 装备加成：武器/防具属性
      - 技能加成：技能倍率
      - 状态加成：buff效果
   
   2. 防御力计算
      - 基础防御：物防/法防/佛防
      - 装备防御：防具属性
      - 技能防御：防御技能
      - 状态防御：buff效果
   
   3. 反伤计算
      - 基础反伤：装备提供
      - 技能反伤：技能效果
      - 最终反伤 = min(反伤系数 * 受到伤害, 最大反伤值)
   ```

3. 死亡处理
   
   ```
   1. 怪物死亡
      - 掉落物品：按配置概率
      - 经验结算：等级差计算
      - 刷新控制：地图怪物数量维护
   
   2. 玩家死亡
      - 随机掉落：身上/背包随机掉落，概率可配置
      - 状态更新：死亡状态标记
      - 记录死亡：时间/地点/原因
   ```

4. 数据结构
   
   ```json
   1. 战斗状态
   {
     "battle_id": "战斗ID",
     "attacker": {
       "user_id": "攻击者ID",
       "attack_type": "攻击类型",
       "skill_no": "技能编号",
       "position": {"x": 0, "y": 0}
     },
     "defender": {
       "target_id": "目标ID",
       "target_type": "目标类型",
       "position": {"x": 0, "y": 0}
     },
     "status": "战斗状态",
     "last_attack_time": "上次攻击时间"
   }
   
   2. 伤害结果
   {
     "damage": "最终伤害",
     "is_critical": "是否暴击",
     "real_damage": "实际伤害",
     "reflect_damage": "反伤伤害",
     "kill": "是否击杀",
     "drop_items": ["掉落物品列表"]
   }
   ```

5. 实现接口
   
   ```java
   // 发起攻击
   Result attack(String userId, String targetId, String skillId);
   
   // 计算伤害
   DamageResult calculateDamage(BattleContext context);
   
   // 处理死亡
   void handleDeath(String targetId, DamageResult result);
   
   // 获取战斗状态
   BattleStatus getBattleStatus(String userId);
   ```

#### 5.1.4 交易系统

1. 交易类型设计
   
   ```
   1. NPC交易
      - 固定价格买卖
      - 商品列表配置
      - 无需确认机制
   
   2. 玩家交易
      - 邀请确认机制
      - 背包物品交易
      - 需双方同意
   ```

2. 交易流程
   
   ```
   1. NPC交易流程
      - 检查与NPC距离（10米内）
      - 查询NPC商品列表
      - 验证物品和价格
      - 执行交易
      - 更新双方物品
   
   2. 玩家交易流程
      - 买家查看卖家背包物品信息
      - 买家发起交易邀请
      - 等待卖家确认
      - 卖家确认交易
      - 执行交易
   ```

3. 数据结构
   
   ```json
   1. NPC商品
   {
     "npc_id": "NPC编号",
     "items": [
       {
         "item_no": "物品编号",
         "price": "价格",
         "currency": "货币类型(金币/银两)",
         "limit": "限购数量"
       }
     ]
   }
   
   2. 交易会话
   {
     "trade_id": "交易ID",
     "initiator": {
       "user_id": "发起者ID",
       "asset_id": "售卖者资产ID"
     },
     "target": {
       "user_id": "目标用户ID",
       "asset_id": "购买者获得后的资产ID"
     },
     "count": "数量",
     "currency": "货币类型",
     "currency_count": "货币数量",
     "status": "交易状态",
     "create_time": "创建时间",
     "expire_time": "过期时间"
   }
   ```

4. 交易验证
   
   ```
   1. 基础验证
      - 距离检查（10米内）
      - 等级限制
      - 物品是否存在
      - 物品是否可交易
   
   2. 条件验证
      - 金币/银两是否足够
      - 背包空间是否足够
      - 限购数量检查
   ```

5. 实现接口
   
   ```java
   // NPC交易
   Result queryNPCItems(String userId, String npcId);
   Result buyFromNPC(String userId, String npcId, String itemNo, int count);
   Result sellToNPC(String userId, String npcId, String itemNo, int count);
   
   // 玩家交易
   Result initiateTrade(String userId, String targetId, Long assetId);
   Result acceptTrade(String tradeId);
   Result rejectTrade(String tradeId);
   Result confirmTrade(String tradeId);
   
   // 交易状态
   TradeStatus getTradeStatus(String tradeId);
   ```

#### 5.1.5 装备系统

1. 装备分类设计
   
   ```
   1. 装备类型
      - 武器：主要提供攻击属性
      - 头冠：主要提供防御属性
      - 玉佩：主要提供灵力属性
      - 衣服：提供攻击和防御属性
      - 护手：提供攻击和防御属性
      - 护膝：提供攻击和防御属性
      - 裤子：提供攻击和防御属性
      - 鞋子：提供攻击和防御属性
   
   2. 装备品质
      - 品数：1~12品
      - 每品属性加成：
        1~3品：0.5%
        4~6品：1%
        7~9品：2%
        10品以上：3%
   ```

2. 装备属性
   
   ```
   1. 基础属性
      - 物理攻击/防御
      - 法术攻击/防御
      - 佛法攻击/防御
      - 血气/法力加成
      - 反伤/内力
   
   2. 品质属性
      - 基础属性 * (1 + 品质加成)
   
   3. 套装属性
      - 同套装装备数量达到要求触发
      - 可叠加多个套装效果
   ```

3. 升品机制
   
   ```
   1. 1~3品
      - 成功率：80%
      - 消耗：
        金币 = 1000 * (品数 * 品数)
        天蚕丝 = 品数 * 品数
   
   2. 4~6品
      - 成功率：60%
      - 消耗：
        金币 = 10000 * (品数 * 品数)
        天蚕丝 = 10 * (品数 * 品数)
        雨花石 = 品数 * 品数
   
   3. 7~9品
      - 成功率：40%
      - 消耗：
        银两 = 5 * 品数
        天蚕丝 = 50 * (品数 * 品数)
        雨花石 = 10 * (品数 * 品数)
   
   4. 10品以上
      - 成功率：20%
      - 消耗：
        银两 = 10 * (品数 * 2)
        天蚕丝 = 100 * (品数 * 品数)
        雨花石 = 50 * (品数 * 品数)
        天外陨石 = 品数
   ```

4. 浣灵机制
   
   ```
   1. 1~3品
      - 消耗：
        金币 = 1000 * (品数 * 品数)
        日月同辉 = 品数 * 2
      - 洗练范围：1.001~1.01
   
   2. 4~6品
      - 消耗：
        金币 = 10000 * (品数 * 品数)
        日月同辉 = 品数 * 3
      - 洗练范围：1.001~1.05
   
   3. 7~9品
      - 消耗：
        银两 = 5 * 品数
        日月同辉 = 品数 * 4
      - 洗练范围：1.001~1.2
   
   4. 10品以上
      - 消耗：
        银两 = 10 * (品数 * 2)
        日月同辉 = 品数 * 5
      - 洗练范围：1.001~1.3
   ```

5. 数据结构
   
   ```json
   1. 装备基础信息
   {
     "item_no": "道具编号",
     "type": 0,  // 装备类型
     "sub_type": "装备子类型(武器/头冠等)",
     "name": "装备名称",
     "level": "等级要求",
     "attributes": {
       "phy_atk": "物理攻击",
       "phy_def": "物理防御",
       "mag_atk": "法术攻击",
       "mag_def": "法术防御",
       "bud_atk": "佛法攻击",
       "bud_def": "佛法防御",
       "hp": "血气",
       "mp": "法力",
       "reflect": "反伤",
       "inner": "内力"
     },
     "set_no": "套装编号"
   }
   
   2. 装备实例
   {
     "asset_id": "资产ID",
     "user_id": "用户ID",
     "item_no": "道具编号",
     "count": 1,  // 装备数量固定为1
     "degree": "品级",  // 对应升品等级
     "plus": "灵力值",  // 对应浣灵值
     "create_time": "创建时间"
   }
   ```

6. 实现接口
   
   ```java
   // 装备操作
   Result equipItem(String userId, Long assetId);
   Result unequipItem(String userId, Long assetId);
   
   // 升品
   Result upgradeQuality(String userId, Long assetId);
   
   // 浣灵
   Result purifySpirit(String userId, Long assetId);
   
   // 查询
   AssetDetail getAssetDetail(Long assetId);
   //type代表资产位置，(1:身上 2:背包 3:仓库)，1获得的就是装备
   List<AssetDetail> getAssetItems(String userId, int type);
   List<SetEffect> getActiveSetEffects(String userId);
   ```

#### 5.1.6 挂机系统设计

1. 挂机机制设计
   
   ```
   1. 挂机类型
      - 挂机打怪：自动攻击附近怪物，获得经验和掉落
      - 挂机采集：自动采集(钓鱼/采桑/采茶/挖矿)，获得材料
      - 挂机收益：为手动操作的20%，可配置调整
   
   2. 挂机限制
      - 最大挂机时长：3小时，可配置
      - 挂机结算间隔：打怪30分钟，采集10分钟，可配置
      - 挂机风险：有概率遇到怪物攻击导致死亡
   ```

2. 挂机状态管理(内存)
   
   ```json
   {
     "user_id": "用户ID",
     "hang_type": "挂机类型(combat/collect)",
     "hang_subtype": "挂机子类型(fishing/mining/monster)",
     "start_time": "开始时间",
     "last_settle_time": "上次结算时间",
     "position": {"x": 0, "y": 0, "map_id": "地图ID"},
     "skill_no": "使用技能编号(打怪时)",
     "total_time": "累计挂机时间",
     "status": "挂机状态(active/paused/stopped)"
   }
   ```

3. 挂机状态管理详细设计
   
   ```
   1. 状态维护
      - 挂机状态存储在内存Map<String, HangingStatus>中，key为user_id
      - 定时任务每分钟检查挂机状态，处理结算和风险事件
      - 玩家下线时保留挂机状态，上线时恢复
      - 服务重启时挂机状态丢失，需重新开始
   
   2. 状态转换
      - 开始挂机：active状态，记录开始时间和位置
      - 暂停挂机：paused状态，保留累计时间
      - 结算收益：计算收益后更新last_settle_time
      - 遇到风险：转为combat状态，暂停挂机
      - 达到时限：自动停止，转为stopped状态
   ```

4. 挂机收益计算详细机制
   
   ```
   1. 打怪收益计算
      - 基础经验 = 怪物等级 * 等级系数 * 挂机时间(分钟)
      - 实际经验 = 基础经验 * 挂机收益比例(默认20%)
      - 掉落计算：每10分钟按正常概率 * 挂机收益比例计算一次
      - 金币收益 = 怪物掉落金币 * 挂机收益比例
   
   2. 采集收益计算
      - 基础产量 = 采集点基础产量 * 挂机时间(分钟) / 采集间隔
      - 实际产量 = 基础产量 * 挂机收益比例(默认20%)
      - 稀有物品：概率降低到正常的50%
      - 材料种类：按配置的概率分布随机生成
   ```

5. 挂机风险机制详细设计
   
   ```
   1. 风险事件类型
      - 怪物袭击：每小时1-5%概率，可配置
      - 恶劣天气：影响采集效率，降低20%收益
      - 装备损耗：长时间挂机装备耐久度下降
      - 体力消耗：超过2小时后收益递减
   
   2. 风险处理机制
      - 怪物袭击：进入战斗状态，根据玩家属性计算胜负
      - 战斗失败：死亡，掉落装备，挂机中断
      - 战斗胜利：获得额外经验，继续挂机
      - 风险通知：通过消息队列通知玩家风险事件
   
   3. 风险配置参数
      - 风险概率：可按地图、挂机类型配置
      - 风险强度：影响战斗难度和损失程度
      - 风险冷却：同一玩家风险事件间隔时间
   ```

6. 挂机系统实现接口
   
   ```java
   // 挂机控制接口
   Result startHanging(String userId, String hangType, String subType, String skillNo);
   Result stopHanging(String userId);
   Result pauseHanging(String userId);
   Result resumeHanging(String userId);
   
   // 挂机状态查询
   HangingStatus getHangingStatus(String userId);
   List<HangingStatus> getAllHangingUsers();
   
   // 挂机结算接口
   void settleHangingReward(String userId);
   void batchSettleHangingRewards();
   
   // 挂机风险处理
   void processHangingRisk(String userId, String riskType);
   void handleHangingCombat(String userId, String monsterId);
   ```

#### 5.1.7 新手保护机制设计

1. 新手保护期定义
   
   ```
   1. 保护期条件
      - 角色等级 <= 10级
      - 创建时间 <= 7天
      - 满足任一条件即享受新手保护
   
   2. 保护期属性加成
      - 物理防御 +50%
      - 魔法防御 +50%
      - 佛法防御 +50%
      - 血量上限 +30%
      - 死亡掉落概率 -80%
   ```

2. 新手保护状态管理
   
   ```json
   {
     "user_id": "用户ID",
     "newbie_protection": {
       "is_protected": true,
       "protection_type": "level", // level/time
       "start_time": "保护开始时间",
       "end_condition": {
         "max_level": 10,
         "max_days": 7
       },
       "bonus_attributes": {
         "defense_w_percent": 50,
         "defense_m_percent": 50,
         "defense_f_percent": 50,
         "hp_percent": 30,
         "drop_reduce_percent": 80
       }
     }
   }
   ```

3. 保护期检查机制
   
   ```java
   public void checkNewbieProtection(String userId) {
       UserCharacter character = getUserCharacter(userId);
       NewbieProtection protection = getNewbieProtection(userId);
   
       if (!protection.isProtected()) {
           return; // 已经不在保护期
       }
   
       boolean shouldRemove = false;
   
       // 检查等级条件
       if (character.getLevel() > protection.getEndCondition().getMaxLevel()) {
           shouldRemove = true;
       }
   
       // 检查时间条件
       long days = (System.currentTimeMillis() - protection.getStartTime()) / (24 * 60 * 60 * 1000);
       if (days > protection.getEndCondition().getMaxDays()) {
           shouldRemove = true;
       }
   
       if (shouldRemove) {
           removeNewbieProtection(userId);
           notifyProtectionEnd(userId);
       }
   }
   ```

4. 属性计算集成
   
   ```java
   public PlayerAttributes calculatePlayerAttributes(String userId) {
       PlayerAttributes baseAttributes = getBaseAttributes(userId);
   
       // 应用新手保护加成
       NewbieProtection protection = getNewbieProtection(userId);
       if (protection != null && protection.isProtected()) {
           applyNewbieBonus(baseAttributes, protection.getBonusAttributes());
       }
   
       // 应用其他加成（装备、状态效果等）
       applyEquipmentBonus(baseAttributes, userId);
       applyStatusEffects(baseAttributes, userId);
   
       return baseAttributes;
   }
   ```

5. 保护期结束通知
   
   ```
   🛡️ 新手保护期结束
   ╭──────────╮
     恭喜少侠！
     你已经成长为真正的江湖侠客
     新手保护期已结束
   
     💪 属性变化：
     物防、法防、佛防 -50%
     血量上限 -30%
   
     ⚠️ 注意：死亡掉落概率恢复正常
   ╰──────────╯
   ```

6. 实现接口
   
   ```java
   // 新手保护管理接口
   void initNewbieProtection(String userId);
   boolean isUnderProtection(String userId);
   void checkAndUpdateProtection(String userId);
   void removeNewbieProtection(String userId);
   
   // 属性加成接口
   void applyNewbieBonus(PlayerAttributes attributes, BonusAttributes bonus);
   ```

#### 5.1.8 日常任务系统设计

1. 任务类型定义
   
   ```yaml
   # quest.yml 任务配置
   daily_quests:
     - quest_no: "DAILY_KILL_MONSTER"
       name: "降妖任务"
       description: "击杀指定地图的10个怪物"
       type: "kill"
       target:
         monster_type: "normal"
         count: 10
         map_id: "MAP_SUDI"
       rewards:
         exp: 500
         gold: 1000
         items:
           - item_no: "ITEM_HERB"
             count: 5
   
     - quest_no: "DAILY_COLLECT"
       name: "采集任务"
       description: "采集10份茶叶或桑叶"
       type: "collect"
       target:
         item_types: ["tea", "mulberry"]
         count: 10
       rewards:
         exp: 300
         gold: 500
   ```

2. 任务状态管理(内存)
   
   ```json
   {
     "user_id": "用户ID",
     "daily_quests": {
       "refresh_date": "2025-01-06",
       "quests": [
         {
           "quest_no": "DAILY_KILL_MONSTER",
           "status": "accepted", // not_accepted/accepted/completed/submitted
           "progress": {
             "current": 3,
             "target": 10,
             "details": {
               "killed_monsters": ["MON_WILLOW", "MON_FROG", "MON_WILLOW"]
             }
           },
           "accept_time": "接受时间戳",
           "complete_time": "完成时间戳"
         }
       ]
     }
   }
   ```

3. 任务进度追踪
   
   ```java
   public void updateQuestProgress(String userId, String eventType, Map<String, Object> eventData) {
       List<DailyQuest> activeQuests = getUserActiveQuests(userId);
   
       for (DailyQuest quest : activeQuests) {
           if (quest.getStatus() != QuestStatus.ACCEPTED) {
               continue;
           }
   
           switch (quest.getType()) {
               case "kill":
                   if ("monster_killed".equals(eventType)) {
                       updateKillProgress(quest, eventData);
                   }
                   break;
               case "collect":
                   if ("item_collected".equals(eventType)) {
                       updateCollectProgress(quest, eventData);
                   }
                   break;
           }
   
           // 检查任务是否完成
           if (isQuestCompleted(quest)) {
               quest.setStatus(QuestStatus.COMPLETED);
               notifyQuestCompleted(userId, quest);
           }
       }
   }
   ```

4. 任务接取和提交
   
   ```java
   // 接受任务
   public Result acceptQuest(String userId, String questNo) {
       DailyQuest quest = getDailyQuest(questNo);
       if (quest == null) {
           return Result.error("任务不存在");
       }
   
       UserQuestProgress progress = getUserQuestProgress(userId);
       if (progress.hasQuest(questNo)) {
           return Result.error("任务已接受");
       }
   
       progress.acceptQuest(quest);
       return Result.success("任务接受成功");
   }
   
   // 提交任务
   public Result submitQuest(String userId, String questNo) {
       UserQuestProgress progress = getUserQuestProgress(userId);
       DailyQuest quest = progress.getQuest(questNo);
   
       if (quest == null || quest.getStatus() != QuestStatus.COMPLETED) {
           return Result.error("任务未完成");
       }
   
       // 发放奖励
       giveQuestRewards(userId, quest.getRewards());
       quest.setStatus(QuestStatus.SUBMITTED);
   
       return Result.success("任务提交成功，获得奖励");
   }
   ```

5. 任务显示格式
   
   ```
   📋 每日任务
   ╭──────────────────╮
   1. ✅ 降妖任务 (已完成)
      击杀怪物 10/10
      奖励：经验+500，金币+1000
   
   2. 🔄 采集任务 (进行中)
      采集材料 3/10
      奖励：经验+300，金币+500
   
   3. ❌ 西湖巡游 (未接受)
      前往三个指定景点
      奖励：经验+200，银两+1
   ╰──────────────────╯
   
   💡 发送"接受任务 3"接受任务
   💡 发送"提交任务 1"提交完成的任务
   ```

6. 任务重置机制
   
   ```java
   @Scheduled(cron = "0 0 0 * * ?") // 每日0点执行
   public void resetDailyQuests() {
       String today = LocalDate.now().toString();
   
       // 获取所有玩家
       List<String> allUsers = getAllActiveUsers();
   
       for (String userId : allUsers) {
           UserQuestProgress progress = getUserQuestProgress(userId);
   
           // 检查是否需要重置
           if (!today.equals(progress.getRefreshDate())) {
               resetUserDailyQuests(userId, today);
           }
       }
   }
   ```

7. 实现接口
   
   ```java
   // 任务管理接口
   Result acceptQuest(String userId, String questNo);
   Result submitQuest(String userId, String questNo);
   Result cancelQuest(String userId, String questNo);
   List<DailyQuest> getUserQuests(String userId);
   
   // 进度更新接口
   void updateQuestProgress(String userId, String eventType, Map<String, Object> eventData);
   boolean isQuestCompleted(DailyQuest quest);
   
   // 奖励发放接口
   void giveQuestRewards(String userId, QuestRewards rewards);
   ```

#### 5.1.9 怪物系统设计

##### 怪物编号规则

- 所有怪物都有唯一的编号，格式为"MON_XXX"
- 编号规则：
  - MON_：固定前缀，表示怪物
  - XXX：怪物特征的英文单词，如WILLOW(柳树)、GHOST(水鬼)等
- 编号用途：
  - 作为怪物的唯一标识
  - 在配置文件中引用
  - 在数据库中存储
  - 在代码中使用

##### 怪物分类

- 普通怪物：普通难度，掉落普通物品
- 精英怪物：较高难度，掉落稀有物品
- BOSS怪物：最高难度，掉落珍贵物品

##### 怪物属性

- 基础属性：等级、血量、法力值
- 攻击属性：物理攻击、法术攻击、佛法攻击
- 防御属性：物理防御、法术防御、佛法防御
- 其他属性：经验值、描述信息

##### 怪物刷新机制

- 按地图分区刷新
- 每个地图有固定的怪物类型
- 设置最小和最大刷新数量
- 定时检查补充怪物数量

##### 怪物掉落设计

- 按怪物等级和类型设置掉落
- 支持多种物品类型掉落
- 每种物品设置掉落概率
- 特殊怪物有特殊掉落

#### 5.1.8 环境扫描与编号机制设计

1. 环境扫描机制
   
   ```
   1. 扫描触发条件
      - 玩家进入新地图时自动扫描
      - 玩家执行"查看地图"、"查看环境"、"扫描"命令时
      - 地图状态发生变化时（怪物刷新、玩家进出）
   
   2. 扫描范围和对象
      - 当前地图内的所有可交互目标
      - 怪物：按距离排序，显示血量状态
      - NPC：显示位置和功能提示
      - 玩家：显示等级和状态
      - 可采集对象：显示类型和数量
   ```

2. 临时编号分配机制
   
   ```json
   {
     "user_id": "用户ID",
     "area_id": "区服ID",
     "map_id": "地图ID",
     "scan_time": "扫描时间戳",
     "objects": [
       {
         "temp_id": 1,
         "object_type": "monster",
         "object_id": "MON_WILLOW_001",
         "name": "柳树精",
         "distance": 10,
         "hp_percent": 100,
         "level": 5
       },
       {
         "temp_id": 2,
         "object_type": "player",
         "object_id": "user123",
         "name": "侠客小A",
         "distance": 2,
         "level": 8,
         "status": "normal"
       },
       {
         "temp_id": 3,
         "object_type": "npc",
         "object_id": "NPC_SONG",
         "name": "宋五嫂",
         "distance": 100,
         "function": "商店"
       }
     ]
   }
   ```

3. 距离计算机制
   
   ```
   1. 距离计算公式
      - 欧几里得距离：sqrt((x1-x2)² + (y1-y2)²)
      - 曼哈顿距离：|x1-x2| + |y1-y2|（用于移动时间计算）
   
   2. 距离显示规则
      - 0-5米：近距离，绿色显示
      - 6-20米：中距离，黄色显示
      - 21米以上：远距离，红色显示
   
   3. 交互距离限制
      - 攻击距离：按职业不同（剑客5米、仙师15米、圣僧8米）
      - 对话距离：10米以内
      - 交易距离：5米以内
   ```

4. 环境扫描反馈格式
   
   ```
   🗺️ 你来到了[苏堤]，柳条轻拂，碧波荡漾。
   周围有：
   1. [怪物] 柳树精 Lv.5 (距离10米) ❤️100%
   2. [怪物] 柳树精 Lv.5 (距离30米) ❤️80%
   3. [NPC] 宋五嫂 (距离100米) 🏪商店
   4. [玩家] 侠客小A Lv.8 (距离2米) ⚔️战斗中
   5. [采集] 桑叶 (距离15米) 📦可采集
   
   💡 使用编号进行交互，如：攻击 1、前往 3、查看背包 @4
   ```

5. 编号有效期管理
   
   ```
   1. 有效期规则
      - 编号有效期：5分钟
      - 地图变化时重新分配编号
      - 玩家移动超过50米时重新扫描
   
   2. 编号冲突处理
      - 同一玩家在同一地图的编号唯一
      - 对象消失时编号失效，不重复使用
      - 新对象出现时分配新的编号
   ```

6. 实现接口
   
   ```java
   // 环境扫描接口
   ScanResult scanEnvironment(String userId, String mapId);
   
   // 编号解析接口
   GameObjectInfo resolveObjectByTempId(String userId, int tempId);
   
   // 距离计算接口
   double calculateDistance(Position pos1, Position pos2);
   
   // 编号管理接口
   void refreshTempIds(String userId, String mapId);
   void clearExpiredTempIds();
   ```

### 5.2 内存状态管理系统

#### 5.2.1 内存数据结构设计

1. 地图实例状态(内存)
   
   ```json
   {
     "map_id": "地图ID",
     "area_id": "区服ID",
     "players": [
       {
         "user_id": "用户ID",
         "position": {"x": 0, "y": 0},
         "status": "玩家状态",
         "last_update": "最后更新时间"
       }
     ],
     "monsters": [
       {
         "instance_id": "怪物实例ID",
         "monster_no": "怪物编号",
         "position": {"x": 0, "y": 0},
         "hp": "当前血量",
         "max_hp": "最大血量",
         "status": "怪物状态",
         "attackers": ["攻击者列表"],
         "last_attack_time": "最后被攻击时间"
       }
     ],
     "objects": [
       {
         "object_id": "对象编号",
         "object_type": "对象类型(npc/item)",
         "position": {"x": 0, "y": 0},
         "status": "对象状态"
       }
     ]
   }
   ```

2. 技能冷却状态管理(内存)
   
   ```json
   {
     "user_id": "用户ID",
     "cooldowns": {
       "attack": "普通攻击冷却结束时间戳",
       "skill_no_1": "技能1冷却结束时间戳",
       "skill_no_2": "技能2冷却结束时间戳"
     },
     "last_attack_time": "最后攻击时间",
     "last_skill_time": "最后使用技能时间"
   }
   ```
   
   技能冷却管理机制：
   
   ```
   1. 冷却检查
      - 使用技能前检查当前时间是否超过冷却结束时间
      - 普通攻击冷却5秒，技能冷却按配置(15-30秒)
      - 冷却中的技能使用返回剩余冷却时间
   
   2. 冷却更新
      - 技能使用成功后更新冷却结束时间
      - 冷却时间 = 当前时间 + 技能配置冷却时间
      - 存储在内存Map<String, SkillCooldown>中
   
   3. 冷却清理
      - 定时任务每分钟清理过期的冷却记录
      - 玩家下线30分钟后清理冷却数据
      - 服务重启时冷却数据丢失，重新开始
   ```

3. 状态效果管理详细设计(内存)
   
   ```json
   {
     "user_id": "用户ID",
     "effects": [
       {
         "effect_id": "效果唯一ID",
         "effect_type": "效果类型(buff/debuff)",
         "skill_no": "来源技能编号",
         "caster_id": "施法者ID",
         "start_time": "开始时间戳",
         "duration": "持续时间(秒)",
         "end_time": "结束时间戳",
         "attributes": {
           "phy_def": "+100",
           "reflect": "30%",
           "speed": "+50%"
         },
         "stack_count": "叠加层数",
         "max_stack": "最大叠加层数"
       }
     ]
   }
   ```
   
   状态效果管理机制：
   
   ```
   1. 效果添加
      - 技能命中目标时添加对应状态效果
      - 检查是否可叠加，不可叠加则覆盖旧效果
      - 可叠加效果增加stack_count，最大不超过max_stack
      - 存储在内存Map<String, List<StatusEffect>>中
   
   2. 效果计算
      - 计算角色属性时遍历所有生效的状态效果
      - 按效果类型累加属性加成
      - 叠加效果按层数计算加成值
      - 相同属性的多个效果可累加
   
   3. 效果清理
      - 定时任务每秒检查效果是否过期
      - 过期效果自动移除并触发属性重算
      - 玩家死亡时清除所有debuff效果
      - 服务重启时状态效果丢失
   
   4. 特殊效果处理
      - 镜面令：反弹30%伤害，持续30秒
      - 金刚护体：提升防御，持续时间可配置
      - 御剑术：移动速度提升，持续1分钟
      - 珠钗令：破防效果，降低目标防御
   ```
   
   技能冷却和状态效果实现接口：
   
   ```java
   // 技能冷却管理接口
   boolean isSkillInCooldown(String userId, String skillNo);
   long getSkillCooldownRemaining(String userId, String skillNo);
   void setSkillCooldown(String userId, String skillNo, int cooldownSeconds);
   void clearSkillCooldown(String userId, String skillNo);
   
   // 状态效果管理接口
   void addStatusEffect(String userId, StatusEffect effect);
   void removeStatusEffect(String userId, String effectId);
   List<StatusEffect> getActiveEffects(String userId);
   void clearAllEffects(String userId, String effectType);
   
   // 属性计算接口
   PlayerAttributes calculatePlayerAttributes(String userId);
   void refreshPlayerAttributes(String userId);
   
   // 定时清理接口
   void cleanExpiredCooldowns();
   void cleanExpiredEffects();
   ```

4. 交易会话状态管理详细设计(内存)
   
   ```json
   {
     "trade_id": "交易唯一ID",
     "initiator": {
       "user_id": "发起者ID",
       "user_name": "发起者名称",
       "asset_id": "出售资产ID",
       "position": {"x": 0, "y": 0, "map_id": "地图ID"}
     },
     "target": {
       "user_id": "目标用户ID",
       "user_name": "目标用户名称",
       "position": {"x": 0, "y": 0, "map_id": "地图ID"}
     },
     "item_info": {
       "item_no": "物品编号",
       "item_name": "物品名称",
       "count": "数量",
       "price": "价格",
       "currency": "货币类型(gold/silver)",
       "bag_index": "背包序号"
     },
     "status": "交易状态",
     "create_time": "创建时间戳",
     "expire_time": "过期时间戳(5分钟后)",
     "last_update": "最后更新时间"
   }
   ```
   
   交易状态管理机制：
   
   ```
   1. 交易状态流转
      - pending: 等待目标用户确认
      - agreed: 目标用户同意，等待最终确认
      - completed: 交易完成
      - cancelled: 交易取消
      - expired: 交易过期
   
   2. 交易验证
      - 距离验证：双方距离不超过10米
      - 物品验证：物品是否存在，是否可交易
      - 资金验证：买方金币/银两是否足够
      - 背包验证：买方背包是否有空位
   
   3. 交易执行
      - 锁定双方相关资源(物品、金币)
      - 原子性操作：物品转移和金币扣除
      - 更新双方背包和资产数据
      - 记录交易日志
   
   4. 交易清理
      - 定时任务每分钟清理过期交易
      - 交易完成或取消后立即清理
      - 玩家下线时取消所有相关交易
      - 存储在内存Map<String, TradeSession>中
   
   5. 交易通知
      - 交易邀请通知目标用户
      - 交易状态变更通知双方
      - 交易完成通知和结果展示
   ```
   
   交易系统实现接口：
   
   ```java
   // 交易会话管理接口
   Result initiateTrade(String buyerId, String sellerId, Long assetId, int price, String currency);
   Result acceptTrade(String tradeId, String userId);
   Result rejectTrade(String tradeId, String userId);
   Result cancelTrade(String tradeId, String userId);
   
   // 交易状态查询接口
   TradeSession getTradeSession(String tradeId);
   List<TradeSession> getUserTrades(String userId);
   List<TradeSession> getPendingTrades(String userId);
   
   // 交易执行接口
   Result executeTrade(String tradeId);
   void cleanExpiredTrades();
   
   // 交易验证接口
   boolean validateTradeDistance(String buyerId, String sellerId);
   boolean validateTradeResources(String tradeId);
   ```

5. 组队系统状态管理(内存) - 二期开发
   
   ```json
   {
     "team_id": "队伍唯一ID",
     "leader": {
       "user_id": "队长ID",
       "user_name": "队长名称"
     },
     "members": [
       {
         "user_id": "队员ID",
         "user_name": "队员名称",
         "level": "等级",
         "profession": "职业",
         "position": {"x": 0, "y": 0, "map_id": "地图ID"},
         "status": "队员状态(online/offline/busy)",
         "join_time": "加入时间"
       }
     ],
     "team_config": {
       "max_members": "最大队员数(3-5人)",
       "exp_share": "经验分享模式(average/contribution)",
       "loot_mode": "掉落分配模式(leader/roll/dkp)",
       "auto_accept": "自动接受申请"
     },
     "team_status": "队伍状态(active/disbanded)",
     "create_time": "创建时间",
     "last_activity": "最后活动时间"
   }
   ```
   
   组队管理机制：
   
   ```
   1. 队伍创建和解散
      - 队长创建队伍，自动成为队长
      - 队长可以解散队伍
      - 队长离线超过30分钟自动转移队长
      - 所有队员离队时自动解散队伍
   
   2. 队员管理
      - 队长可以邀请玩家入队
      - 队长可以踢出队员
      - 队员可以主动离队
      - 队员上限3-5人可配置
   
   3. 队伍功能
      - 共享经验：队伍内击杀怪物经验按贡献或平均分配
      - 协作打BOSS：队伍内可以协同攻击同一目标
      - 队伍聊天：队伍内部聊天频道
      - 位置共享：队员位置信息共享
   
   4. 掉落分配
      - 队长分配：队长决定物品归属
      - 随机分配：系统随机分配给队员
      - 贡献分配：按伤害贡献分配
   
   5. 队伍存储
      - 存储在内存Map<String, TeamInfo>中
      - 队员关系存储在Map<String, String>中(user_id -> team_id)
      - 队伍解散时清理相关数据
      - 服务重启时队伍数据丢失，需重新组队
   ```
   
   组队系统实现接口：
   
   ```java
   // 队伍管理接口
   Result createTeam(String leaderId, String teamName);
   Result disbandTeam(String teamId, String leaderId);
   Result transferLeader(String teamId, String currentLeader, String newLeader);
   
   // 队员管理接口
   Result inviteToTeam(String teamId, String leaderId, String targetUserId);
   Result acceptTeamInvite(String teamId, String userId);
   Result rejectTeamInvite(String teamId, String userId);
   Result leaveTeam(String teamId, String userId);
   Result kickFromTeam(String teamId, String leaderId, String targetUserId);
   
   // 队伍查询接口
   TeamInfo getTeamInfo(String teamId);
   TeamInfo getUserTeam(String userId);
   List<TeamMember> getTeamMembers(String teamId);
   
   // 队伍功能接口
   void shareExperience(String teamId, int totalExp);
   void distributeLoot(String teamId, List<ItemDrop> drops);
   Result sendTeamMessage(String teamId, String senderId, String message);
   
   // 队伍状态管理
   void updateMemberStatus(String userId, String status);
   void cleanInactiveTeams();
   ```

#### 5.2.2 内存数据同步机制

1. 数据同步策略
   
   ```
   1. 玩家位置同步
      - 实时更新内存中的位置
      - 每5分钟同步到数据库user_character表
      - 玩家下线时立即同步
   
   2. 怪物状态同步
      - 怪物血量变化实时更新内存
      - 怪物死亡时从内存移除
      - 定时刷新机制补充怪物
   
   3. 挂机状态同步
      - 挂机状态实时维护在内存
      - 挂机收益定时结算并同步到数据库
      - 玩家下线时保留挂机状态
   
   4. 技能冷却同步
      - 冷却状态仅存储在内存
      - 不需要持久化到数据库
      - 服务重启时冷却重置
   
   5. 状态效果同步
      - 效果到期自动从内存移除
      - 不需要持久化到数据库
      - 死亡时清除相关效果
   
   6. 交易会话同步
      - 交易状态仅存储在内存
      - 交易完成后记录到操作日志
      - 过期交易自动清理
   
   7. 组队信息同步
      - 队伍状态仅存储在内存
      - 不需要持久化到数据库
      - 服务重启时队伍解散
   ```

2. 数据恢复机制
   
   ```
   1. 服务重启恢复
      - 玩家数据从数据库加载
      - 地图怪物按配置重新生成
      - 临时状态(冷却/效果/交易/组队)丢失，重新开始
      - 挂机状态丢失，玩家需重新开始挂机
   
   2. 异常恢复
      - 定时检查数据一致性
      - 发现异常时重新加载
      - 关键操作记录日志便于追溯
      - 内存数据异常时从数据库重新加载
   
   3. 数据清理机制
      - 定时清理过期的内存数据
      - 清理离线玩家的临时状态
      - 清理无效的交易会话和队伍信息
   ```

### 5.3 性能优化方案

#### 5.2.1 缓存机制设计

1. 静态数据缓存
   
   ```
   1. 缓存范围
      - 道具表基础数据
      - 技能表基础数据
      - 怪物表基础数据
      - 游戏命令字符配置
      - 等级/称号-升级经验配置
      - NPC商店配置
      - 地图及NPC位置配置
      - 地图刷怪配置
      - 怪物掉落配置
      - 钓鱼采集配置
      - 攻击效果配置
      - 装备升品材料配置
      - 玉佩浣灵材料配置
      - 套装属性加成配置
      - 神奇盒子开出道具配置
      - 新手初始化配置
   
   2. 缓存策略
      - 启动时全量加载到内存
      - 数据库表记录或者配置文件修改时触发更新
      - 系统重启时重新加载
   
   3. 缓存结构
      {
        "item": {
          "item_no_1": {
            "base_info": "基础信息",
            "attributes": "属性数据"
          },
          "item_no_2": {...}
        },
        "config": {
          "command": {...},  // 命令配置
          "level": {...},    // 等级配置
          "shop": {...},     // 商店配置
          "map": {...}       // 地图配置
        }
      }
   ```

2. 动态数据缓存
   
   ```
   1. 用户数据缓存（可配置缓存时间）
      - 角色基础信息
      - 背包数据
      - 装备数据
      - 状态数据
   
   2. 地图数据缓存
      - 地图玩家列表(实时)
      - 地图怪物列表(实时)
      - 地图状态数据(可配置)
   
   3. 排行榜缓存
      - 等级排行(可配置)
      - 战力排行(可配置)
      - 财富排行(可配置)
   ```

3. 缓存更新机制
   
   ```
   1. 被动更新
      - 数据变更时触发
      - 缓存失效时加载
      - 定时任务检查
   
   2. 主动更新
      - 运营后台触发
      - 系统重启触发
   
   3. 更新策略
      - 增量更新优先
      - 按需加载
      - 异步更新
   ```

#### 5.2.2 状态计算优化

1. 角色属性计算
   
   ```
   1. 计算时机
      - 登录时计算
      - 装备变更时（脱穿/升品/浣灵）
      - 状态变更时（死亡/复活）
      - 技能施放/被技能命中时
      - 使用药物时
      - 人物升级时
      - 每5分钟重算一次（确保数据一致性）
   
   2. 计算策略
      - 基础属性缓存（等级对应的基础属性）
      - 装备属性叠加（所有装备的属性和套装效果）
      - 状态效果叠加（BUFF/DEBUFF效果）
      - 技能效果叠加（持续性技能效果）
      - 药物效果叠加（临时属性加成）
   
   3. 优化方案
      - 增量计算为主（只计算变化的部分）
      - 全量计算为辅（定时进行数据校验）
      - 异步计算队列（非实时计算放入队列）
      - 结果缓存1分钟（减少重复计算）
      - 批量更新（多个属性变化一起计算）
   ```

2. 战斗状态计算
   
   ```
   1. 实时计算（必须实时）
      - 攻击判定（距离/状态/冷却）
      - 伤害计算（攻击/防御/暴击/反伤）
      - 即时状态更新（血量/法力/状态）
      - 死亡判定（是否致死/死亡处理）
   
   2. 延迟计算（可以延迟）
      - 经验结算（击杀经验/等级提升）
      - 掉落生成（物品掉落/拾取处理）
      - 场景更新（怪物刷新/物品清理）
      - 排行统计（战力/财富/等级排行）
   
   3. 并发控制
      - 乐观锁控制（版本号控制）
      - 状态队列化（按角色ID分队列）
      - 批量处理（同一角色的多个状态）
      - 防重复提交（请求去重）
   ```

3. 地图状态计算
   
   ```
   1. 定时计算(1秒)
      - 玩家位置（移动后的新位置）
      - 怪物位置（巡逻/追击位置）
      - 场景状态（可采集物/可钓鱼点）
      - 掉落物品（更新到背包）
   
   2. 计算优化
      - 区域划分（将地图分为多个区域）
      - 就近计算（只计算玩家附近区域）
      - 批量更新（同一区域的状态一起更新）
      - 异步处理（非关键状态异步更新）
   
   3. 数据同步
      - 增量同步（只同步变化数据）
      - 定时全量（每5分钟全量同步）
      - 按需加载（进入新区域时加载）
      - 延迟持久化（非关键数据延迟保存）
   ```

4. 数据一致性保证
   
   ```
   1. 版本控制
      - 角色状态版本号
      - 地图状态版本号
      - 装备状态版本号
      - 版本号不一致时重新计算
   
   2. 数据校验
      - 定时全量校验（每5分钟）
      - 重要节点校验（战斗前/后）
      - 异常恢复机制（回滚到上一状态）
      - 日志记录（重要状态变化记录）
   
   3. 并发处理
      - 角色状态锁（保证同一角色操作串行）
      - 地图区域锁（保证同一区域操作串行）
      - 装备操作锁（保证装备操作串行）
      - 死锁预防（锁超时机制）
   ```

### 5.4 日志系统设计

#### 5.4.1 日志分类

1. 操作日志
   
   ```
   1. 关键操作记录
      - 角色创建/删除
      - 装备升品/浣灵
      - 玩家交易
      - 充值记录
      - 管理员操作
   
   2. 战斗日志
      - PVP战斗记录
      - BOSS击杀记录
      - 死亡记录
   
   3. 经济日志
      - 金币/银两变动
      - 物品获得/消失
      - NPC交易记录
   ```

2. 日志格式
   
   ```
   时间戳|区服ID|用户ID|操作类型|操作详情|结果|IP地址
   
   示例：
   2025-01-06 10:30:15|AREA_001|user123|TRADE|买@user456背包1格物品1000银两|SUCCESS|***********
   2025-01-06 10:31:20|AREA_001|user123|UPGRADE|升品武器青锋剑从3品到4品|FAILED|***********
   ```

3. 日志存储
   
   ```
   1. 文件存储
      - 按日期分割日志文件
      - 日志文件路径：logs/game_YYYYMMDD.log
      - 保留30天日志文件
   
   2. 日志轮转
      - 单文件最大100MB
      - 超过大小自动分割
      - 压缩历史日志文件
   ```

#### 5.4.2 日志分析

1. 实时监控
   
   ```
   1. 异常行为监控
      - 短时间大量操作
      - 异常数值变动
      - 频繁失败操作
   
   2. 系统性能监控
      - 命令处理延迟
      - 错误率统计
      - 并发用户数
   ```

2. 数据统计
   
   ```
   1. 运营数据
      - 日活跃用户数
      - 用户行为分析
      - 功能使用率
   
   2. 经济数据
      - 金币/银两流通
      - 物品产出消耗
      - 交易活跃度
   ```

## 第七章 运营和防作弊

### 7.1 运营数据统计

#### 6.1.1 数据收集

1. 核心指标
   
   ```
   1. 用户数据
      - 在线人数统计
      - 注册人数统计
      - 日活跃用户数
      - 次日留存率
      - 7日留存率
      - 人均游戏时长
   
   2. 游戏数据
      - 职业选择分布
      - 等级段位分布
      - 装备品质分布
      - 银两分布统计
      - 主要玩法使用率(PVP/PVE/交易)
   
   3. 变现数据
      - 银两购买次数/金额
      - 神奇盒子开启次数/类型
   ```

#### 6.1.2 数据分析

1. 游戏健康度分析
   
   ```
   1. 活跃度
      - 日活跃趋势
      - 留存率变化
      - 游戏时长分布
   
   2. 游戏平衡性
      - 职业胜率对比
      - 等级提升速度
      - 装备获取难度
   ```

2. 变现效果分析
   
   ```
   1. 付费转化
      - 首次付费时机
      - 付费金额分布
      - 付费频次分析
   ```

### 6.2 防作弊系统

#### 6.2.1 作弊行为识别

1. 异常行为监控
   
   ```
   1. 打怪异常
      - 短时间内击杀大量怪物
      - 远距离攻击(超出10米)
      - 瞬间移动式打怪
   
   2. 经济异常
      - 短时间获得大量金币
      - 交易价格严重偏离市场
      - 频繁小额交易
   
   3. 升级异常
      - 短时间内快速升级
      - 经验获取来源异常
   ```

2. 异常判定标准
   
   ```
   1. 时间维度
      - 单位时间内操作次数
      - 操作间隔时间
      - 在线时间分布
   
   2. 空间维度
      - 移动距离/速度
      - 攻击距离范围
      - 地图分布范围
   
   3. 数值维度
      - 金币获取速度
      - 经验获取速度
      - 装备获取概率
   ```

#### 6.2.2 防作弊措施

1. 实时防护
   
   ```
   1. 操作校验
      - 移动距离检查
      - 攻击距离检查
      - 技能冷却检查
      - 技能限流控制
      - 状态合法性检查
   
   2. 数据校验
      - 经验获取上限
      - 金币获取上限
      - 装备掉落概率
      - 交易价格区间
   ```

2. 事后追溯
   
   ```
   1. 行为日志
      - 记录关键操作
      - 记录异常数据
      - 记录处罚历史
   
   2. 数据分析
      - 定期分析异常
      - 关联账号分析
      - 交易链路分析
   ```

#### 6.2.3 惩罚机制

1. 处罚等级
   
   ```
   1. 警告
      - 首次异常行为
      - 轻微违规操作
   
   2. 限制
      - 限制交易功能
      - 限制PVP功能
      - 限制打怪收益
   
   3. 封禁
      - 临时封禁(1-7天)
      - 永久封禁
   ```

2. 申诉机制
   
   ```
   1. 申诉渠道
      - 群内申诉
      - 私信申诉
   
   2. 处理流程
      - 收集申诉信息
      - 核实违规记录
      - 确认处理结果
   ```

## 