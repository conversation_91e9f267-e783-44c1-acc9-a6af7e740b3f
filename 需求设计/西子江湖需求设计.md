# 西子江湖需求设计文档

## 文档更新记录

| 版本   | 日期         | 更新内容                              | 更新人  |
| ---- | ---------- | --------------------------------- | ---- |
| v1.1 | 2025-01-06 | 优化命令系统设计，增加错误处理机制，明确攻击距离，完善挂机系统配置 | AI助手 |
| v1.0 | 2024-XX-XX | 初始版本                              | 原作者  |

## 第一章 游戏概述

### 1.1 游戏背景

西子江湖是一款基于社交聊天APP的文字RPG游戏。游戏以杭州西湖的历史文化为背景，融合了白蛇传、雷峰塔等传统故事元素，让玩家在充满江湖气息的西湖世界中体验升级、战斗、社交等丰富玩法。

### 1.2 游戏特色

1. 无界面交互
   
   - 纯文字命令式玩法
   - 基于社交APP群聊实现
   - 通过文字描述和emoji表情增强游戏体验
   - 部分场景使用ASCII字符绘制界面效果

2. 西湖文化融入
   
   - 融入西湖历史传说
   - 景点作为游戏场景
   - 历史人物作为NPC
   - 传统故事融入剧情

3. 社交化设计
   
   - 群聊即游戏区服
   - 支持多人实时互动
   - 玩家之间可交易
   - 预留社交玩法扩展

### 1.3 核心玩法

1. 基础玩法
   
   - 通过文字命令进行游戏操作
   - 支持地图探索、打怪升级
   - 装备收集与强化
   - 技能学习与使用

2. 战斗系统
   
   - 三大职业相互克制
   - 支持PVE和PVP
   - 技能战斗
   - 装备属性加成

3. 经济系统
   
   - 金币：基础货币
   - 银两：高级货币（1银两=10000金币）
   - 支持交易系统

## 第二章 游戏设定

### 2.1 世界观

游戏设定群聊即游戏区服，一个群即一个世界（区服），可以在群里执行命令进行交互和游戏机器人代表系统发出反馈信息；

由于多个人在同个群里玩，消息会非常多，为了屏蔽过多的消息，可以加机器人为好玩，和机器人直接对话玩游戏，但是采用“!模式模式”命令，必须加入区服别名，这样指定在哪个区分（世界）。

区服别名由游戏群建立时，管理员指定

### 2.2 角色系统

#### 职业设计

- 剑客（男性）
  
  - 定位：物理输出型职业
  
  - 特点：物理攻击高、物理防御高
  
  - 弱点：法防低、佛防低
  
  - 适合：喜欢近战的玩家

- 仙师（女性）
  
  - 定位：法术输出型职业
  - 特点：法术攻击高、法术防御高
  - 弱点：物防低、佛防低
  - 适合：喜欢远程法术的玩家

- 圣僧（男性）
  
  - 定位：佛法输出型职业
  - 特点：佛法攻击高、佛法防御高
  - 弱点：物防低、法防低
  - 适合：喜欢佛法攻击的玩家

#### 等级称号

| 等级段位 | 级别范围   | 称号   |
| ---- | ------ | ---- |
| 第一阶段 | 1-9级   | 初出茅庐 |
| 第二阶段 | 10-29级 | 江湖新手 |
| 第三阶段 | 30-59级 | 江湖有名 |
| 第四阶段 | 60-99级 | 武林高手 |
| 第五阶段 | 100级以上 | 西湖神话 |

#### 属性系统

- 基础属性
  
  - 等级：决定基础属性值，不同职业属性有偏向性（三职业相互克制）
  - 经验值：升级所需经验
  - 金币：基础货币，拥有的货币，一般来源于打怪或者npc交易获得
  - 银两：高级货币，最初来源只能充值获得，可以玩家交易间接获得

- 战斗属性
  
  - 攻击：物攻/法攻/佛攻
  - 防御：物防/法防/佛防
  - 灵力：增加攻击百分比
  - 反伤：受攻击时反弹伤害

- 资源属性
  
  - 血气：生命值
  - 法气：法力值
  - 内力：影响回血速度

#### 技能系统

对应的职业，只能通过技能书学习（见道具系统说明）相应的技能，只能学习本职业相关的技能

- 剑客技能
  
  - 破军剑法：物攻（主攻）
  - 镜面令：反弹伤害30%（辅助），有 效时间30秒
  - 御剑术：极速行走（特色），有效时间1分钟
  - 精忠剑法：极高伤害，二期实现

- 仙师技能
  
  - 玄雷术：法攻（主攻）
  - 珠钗令：破防（辅助）
  - 回法术：回法气（特色）
  - 白蛇法相：极高伤害，二期实现

- 圣僧技能
  
  - 金刚掌：佛攻（主攻）
  - 金刚护体：提升防御（辅助）
  - 大悲咒：加血气（特色）
  - 遁形：二期开放，隐身，有效时间5分钟
  - 往生咒：二期开放，有概率秒杀对方，二期开

技能冷却时间：

- 主攻技能：15秒
- 辅助技能：20秒
- 特色技能：30秒

技能法气消耗：

- 主攻技能：20点
- 辅助技能：30点
- 特色技能：50点

威力计算影响：

- 人物等级

- 技能威力

- 人攻击属性

- 受攻防御属性

- 技能威力系数（见技能威力配置）

### 2.3 背包和仓库

- 背包
  
  初始化背包系统有50格，除装备外，其他物品都可以合并为一格，并显示数量，虚拟物品（主要是金币和银两，见道具系统）不占背包格子，只有背包里的道具才可以直接使用或者交易
  
  背包50格，有道具的都占1格，并且有序号，可以查看其他玩家里面背包信息，以及批定查看背包里面某一格道具信息，方便玩家间交易

- 仓库
  
  背包里的道具可以放到仓库，也可以从仓库里面取出来放到背包里面

### 2.4 道具系统

#### 道具基本属性定义

- 每种道具都有唯一的物品ID

- 类型（装备、药品、技能书、材料、虚拟物、宝箱）

- 子类型：根据类型拆分子类型

- 属性：不同道具有不同属性，可选

- 等级要求：满足等级才能使用

#### 装备（type=0）

- 基础属性
  
  - 三防三攻
  
  - 血气/法气
  
  - 反伤/内力

- 随机属性，随机生成1~2条属性，包括基础属性里面的范围和暴击属性

- 职业角色属性，不能跨角色装备使用

- 装备品质
  
  - 1-3品：基础装备
  
  - 4-6品：进阶装备
  
  - 7-9品：高级装备
  
  - 10品以上：顶级装备

- 部位属性
  
  | 位置  | 主要属性 | 说明       |
  | --- | ---- | -------- |
  | 武器  | 攻击   | 提供主要攻击加成 |
  | 头冠  | 防御   | 提供主要防御加成 |
  | 玉佩  | 灵力   | 提供灵力加成   |
  | 衣服  | 攻防   | 提供攻击防御加成 |
  | 护手  | 攻防   | 提供攻击防御加成 |
  | 护膝  | 攻防   | 提供攻击防御加成 |
  | 裤子  | 攻防   | 提供攻击防御加成 |
  | 鞋子  | 攻防   | 提供攻击防御加成 |

装备详细设计见《装备需求设计.md》

#### 药品(type=1)

| 名称   | 稀有度 | 功能     |
| ---- | --- | ------ |
| 回气丹  | 普通  | 加血气100 |
| 回法丹  | 普通  | 加法气100 |
| 龙井仙露 | 稀有  | 瞬间满血   |
| 灵芝仙草 | 极稀有 | 起死回生   |

#### 技能书(type=2)

| 物品名  | 角色  | 等级要求   | 作用           |
| ---- | --- | ------ | ------------ |
| 御剑术  | 剑客  | 1~10级  | 极速行走         |
| 珠钗令  | 仙师  | 1~10级  | 破防           |
| 大悲咒  | 圣僧  | 1~10级  | 加血气          |
| 破军剑法 | 剑客  | 10~59级 | 物攻           |
| 玄雷术  | 仙师  | 10~59级 | 法攻           |
| 金刚掌  | 圣僧  | 10~59级 | 佛攻           |
| 金刚护体 | 圣僧  | 10~59级 | 提升防御         |
| 镜面令  | 剑客  | 60~99级 | 反弹伤害50%      |
| 精忠剑法 | 剑客  | 60~99级 | 超高物伤害        |
| 回法术  | 仙师  | 60~99级 | 加血气          |
| 白蛇法相 | 仙师  | 60~99级 | 超高法伤害        |
| 遁形   | 圣僧  | 60~99级 | 隐身，地图上消失30分钟 |
| 往生咒  | 圣僧  | 60~99级 | 有概率秒杀对方      |

#### 材料(type=3)

| 名称   | 稀有度 | 来源     | 作用             |
| ---- | --- | ------ | -------------- |
| 天蚕丝  | 普通  | NPC桑叶换 | 升级防具           |
| 雨花石  | 普通  | 保俶山挖矿  | 升级武器           |
| 天外陨石 | 稀有  | 开箱子    | 升级装备           |
| 日月同辉 | 极稀有 | 开箱子    | 升级装备           |
| 升级护符 | 极稀有 | 开箱子    | 防升级失败          |
| 桑叶   | 普通  | 梅家坞    |                |
| 茶叶   | 普通  | 龙井村    |                |
| 鱼类   | 普通  | 钓鱼     | 白条/鲫鱼/鲤鱼/鳜鱼/鳗鱼 |

#### 虚拟物(type=8)

- 经验

- 金币

- 银两

#### 神奇宝箱(type=9)

| 子类别    | 稀有度 | 来源      |
| ------ | --- | ------- |
| 宝箱[初级] | 普通  | 金币兑换    |
| 宝箱[中级] | 普通  | 1银两兑换   |
| 宝箱[高级] | 稀有  | 5银两兑换   |
| 宝箱[稀有] | 极稀有 | 100银两兑换 |

### 2.5 怪物系统

| 怪物名      | 推荐等级  | 血量   | 功击特性 | 防御特性 | 掉落推荐配置    | 暴率  |
| -------- | ----- | ---- | ---- | ---- | --------- | --- |
| 柳树精      | 1~10  | 100  | 物攻   | 物防   | 游湖套装      | 普通  |
| 青蛙精      | 1~10  | 100  | 法攻   | 法防   | 湖光套装      | 普通  |
| 水鬼       | 1~10  | 100  | 物攻   | 物防   | 游湖套装      | 普通  |
| 游魂       | 1~10  | 100  | 佛攻   | 佛防   | 印月套装      | 普通  |
| 孤狼       | 10~30 | 200  | 物攻   | 物防   | 游湖套装      | 普通  |
| 花妖       | 10~30 | 200  | 法攻   | 法防   | 湖光套装      | 普通  |
| 游僧       | 10~30 | 200  | 佛攻   | 佛防   | 印月套装      | 普通  |
| 法海(boss) | 60~80 | 800  | 三系攻击 | 三防   | 岳王/灵隐/白蛇套 | 低   |
| 金刚力士     | 60    | 400  | 物攻   | 物防   | 岳王套装      | 极低  |
| 乌龟精      | 60    | 400  | 佛攻   | 佛防   | 灵隐套装      | 极低  |
| 螃蟹精      | 70    | 500  | 法攻   | 法防   | 白蛇套装      | 极低  |
| 黑鱼精      | 80    | 600  | 三系攻击 | 三防   | 岳王/灵隐/白蛇套 | 低   |
| 蛟龙(boss) | 100   | 1200 | 三系攻击 | 三防   | 岳王/灵隐/白蛇套 | 高   |
|          |       |      |      |      |           |     |

具体怪物刷新见地图系统配置章节、怪物掉落配置见游戏配置章节

### 2.6 地图系统

#### 地图类型

- 基础地图
  
  - 武林主城：交易NPC、出生地
  - 郊外地图：
    - 苏堤：钓鱼、柳树精、青蛙精
    - 断桥：水鬼、钓鱼
    - 孤山：游魂、孤狼（初出茅庐）
    - 太子湾：花妖、游僧（初出茅庐）
    - 保俶山：采矿
    - 梅家坞：采桑
    - 龙井村：采茶（江湖新手）

- 副本地图
  
  - 雷峰塔底：法海(boss)、金刚力士、乌龟精、螃蟹精（江湖新手 武林高手）
  - 湖心亭：螃蟹精、黑鱼精、蛟龙(boss)（西湖神话）
  - 九溪十八涧：二期开发
  - 十里锒铛：豹子、二期开发

#### 地图怪物

由游戏配置里面指定地图和刷怪配置确定刷怪策略：

- 30分钟检查一次，少于30%则补充怪物数量，随机分布地图

- 每只怪物实例都有具体唯一标识编号，方便后续组队打怪识别以及怪物状态（血量、是否死亡消失，哪些玩家在打）

#### NPC商店

| NPC | 位置   | 功能       |
| --- | ---- | -------- |
| 钱镠  | 武林主城 | 买卖天蚕丝、桑叶 |
| 宋五嫂 | 断桥   | 买卖鱼类     |
| 岳飞  | 武林主城 | 买卖装备     |
| 丁仁  | 武林主城 | 买卖技能书    |
| 许仙  | 断桥   | 买卖药品     |
| 济公  | 武林主城 | 出售神奇盒子   |

人物与NPC距离10米内可以和NPC对话，了解商店可以买什么，可以买什么，使用NPC命令交互；

具体NPC描述、交易商品名和价格、所在地图等配置，由游戏配置章节指定配置

#### 地图对象编号

查看地图时，返回即时所在地图的对象和编号列表：

- 编号

- 对象名

- 对象属性

例如：

```
🗺️ 你来到了[苏堤]，柳条轻拂，碧波荡漾。
周围有：
1. [怪物] 柳树精 (距离10米)
2. [怪物] 柳树精 (距离30米)
3. [NPC] 宋五嫂 (距离100米)
4. [玩家] 侠客小A (距离2米)
```

这样玩家可以通过编号精确交互：

```
玩家可以使用这个临时编号来执行精确操作，省去输入完整名称的麻烦。

 - 攻击 1：攻击编号为1的柳树精。

 - 前往 3 或 询问 3：移动到宋五嫂附近并触发对话。

 - 查看背包 @4：查看玩家“侠客小A”的背包
```

### 2.7 命令系统

#### 命令规范

- 命令格式
  
  - 强命令："!" 或者“！”开头
  - 操作词 + 操作对象
  - @玩家 相关操作

- 命令模式
  
  - !游戏模式：进入游戏状态
  - !退出游戏模式：退出游戏状态
  - 游戏模式下无需使用"!"或者“！”

#### 命令容错

因为是文字输入命令游戏，玩家记不信这么多命令，可能打出相似性比较强的命令，这里通过命令别名来支持

- 命令别名
  
  通过配置一系列的命令别名，比如“攻击”差多的，包括“打”、“杀”、“砍”、“hit” 等方式，都可以确定为“攻击”输入。

- 常用命令简化建议
  
  | 完整命令              | 简化别名       | 说明       |
  | ----------------- | ---------- | -------- |
  | 查看状态              | 状态、st      | 查看角色状态   |
  | 查看地图              | 地图、map     | 查看当前地图   |
  | 查看背包              | 背包、bb      | 查看背包内容   |
  | 查看装备              | 装备、zb      | 查看装备信息   |
  | 前往苏堤              | 去苏堤        | 移动到指定地点  |
  | 攻击柳树精             | 打1         | 攻击编号1的目标 |
  | 买@玩家 背包序号 货币数量 银两 | 买1 1 1000银 | 简化交易命令   |

命令操作对象，游戏命令里面操作玩家对象，有些是带AT对象，传入是具体玩家id，有些是直接角色名，这两者都要识别出玩家，比如 "攻击@凌封" 和“攻击凌封” 这里对象是一样的。

#### 错误处理机制

- 统一错误反馈格式
  
  ```
  ❌ 操作失败：[具体原因]
  💡 建议：[可能的解决方案]
  ```

- 常见错误类型
  
  | 错误类型  | 错误提示       | 建议操作            |
  | ----- | ---------- | --------------- |
  | 距离过远  | 距离过远，无法攻击  | 发送"前往 目标编号"靠近目标 |
  | 命令不存在 | 未知命令       | 发送"!帮助"查看可用命令   |
  | 参数错误  | 参数格式错误     | 显示正确的命令格式示例     |
  | 资源不足  | 金币不足，无法购买  | 显示当前金币和所需金币     |
  | 冷却中   | 技能冷却中，还需X秒 | 显示剩余冷却时间        |
  | 目标不存在 | 找不到指定目标    | 发送"查看地图"刷新目标列表  |

#### 基础命令

1. 查看命令（查看XX）
   
   | 命令类型 | 命令示例       | 说明                                               |
   | ---- | ---------- | ------------------------------------------------ |
   | 状态查看 | 查看状态       | 显示角色当前行动、状态、位置、死亡信息                              |
   | 地图查看 | 查看地图       | 显示地图、人物分布、boss分布，返回列表，并有编号，方便交互直接使用编号            |
   | 怪物查看 | 查看法海       | 显示法海的介绍和属性                                       |
   | 装备查看 | 查看装备       | 显示装备属性和装备情况，不@玩家，默认查看自己的                         |
   | 背包查看 | 查看背包@玩家 序号 | 显示背包内容，如果带上“@玩家” 则显示玩家背包，加上序号，则显示具体玩家背包里面某一格的道信息 |
   | 排名查看 | 查看排名       | 显示战力、财富、恶人(杀人数量)、降妖排行(杀怪数量)                      |

2. 移动命令（前往XX）
   
   | 命令类型 | 命令示例            | 说明                   |
   | ---- | --------------- | -------------------- |
   | 地点移动 | 前往+地点名          | 移动到指定地点              |
   | 精准移动 | 前往+坐标           | 移动到指定坐标              |
   | 模糊移动 | 前往+人物/怪物/地图目标编号 | 移动到目标10米范围内上下左右随机一位置 |

3. 挂机命令（挂机XX）
   
   | 命令类型 | 命令示例    | 说明               |
   | ---- | ------- | ---------------- |
   | 挖矿   | 挂机挖矿    | 挂机挖矿，每10分钟计算一次结果 |
   | 钓鱼   | 挂机钓鱼    | 钓鱼，每10分钟计算一次结果   |
   | 采集   | 挂机采桑    | 采桑，每10分钟计算一次结果   |
   | 打怪   | 挂机打怪 技能 | 挂机自动打怪，技能可选      |

4. 兑现命令
   
   | 命令类型 | 命令示例 | 说明           |
   | ---- | ---- | ------------ |
   | 兑现   | 兑现20 | 按配置比例兑换银两为金币 |

#### 交互命令

1. 战斗命令
   
   | 命令类型 | 命令示例     | 说明                              |
   | ---- | -------- | ------------------------------- |
   | 普通攻击 | 攻击+目标    | 对目标发起普通攻击，目标可以玩家，也可以是怪物，也可以地图编号 |
   | 技能攻击 | 施+技能名+目标 | 对目标使用技能，目标可以玩家，也可以是怪物，也可以地图编号   |
   | 复活命令 | 复活       | 死亡后复活，复活后回到武林主城                 |

2. 采集玩法
   
   | 命令类型 | 命令示例 | 说明                |
   | ---- | ---- | ----------------- |
   | 采集   | 钓鱼   | 在可钓鱼的地图，随机钓起配置的物品 |
   | 采集   | 采桑   | 在可采桑的地图，随机采到配置的物品 |
   | 采集   | 采茶   | 在可采茶的地图，随机采到配置的物品 |
   | 采集   | 挖矿   | 在可挖矿的地图，随机挖出配置的物品 |

3. 人物操作
   
   | 命令类型 | 命令示例    | 说明          |
   | ---- | ------- | ----------- |
   | 人物操作 | 创建剑客    | 创建对应职业名称的角色 |
   | 使用物品 | 用+物品名   | 使用消耗品，比如药品  |
   | 装备物品 | 装备+物品名  | 穿上装备        |
   | 存取物品 | 存/取+物品名 | 从仓库里拿出来或者放入 |

4. NPC交易
   
   | 命令类型  | 命令示例         | 说明                  |
   | ----- | ------------ | ------------------- |
   | NPC交易 | 买/卖+物品名 NPC名 | 与NPC交易相应的物品         |
   | NPC交易 | 询问+NPC名      | NPC自我介绍，回复卖xxx,买xxx |

5. 玩家交易
   
   | 命令类型  | 命令示例                 | 说明              |
   | ----- | -------------------- | --------------- |
   | 玩家交易  | 买@玩家 背包序号 货币数量 银两/金币 | 请求买对方背包里对应序号的道具 |
   | NPC交易 | 卖@玩家 同意              | 同意买方请求，卖出道具     |

6. 装备操作
   
   | 命令类型 | 命令示例   | 说明           |
   | ---- | ------ | ------------ |
   | 装备升级 | 升品+道具名 | 对身上对应的道具进行升品 |
   | 装备升级 | 浣灵     | 只对身上的玉佩进行浣灵  |

#### 帮助命令

游戏帮助说明：

- !帮助
  
  - 显示游戏背景📖
    
    以杭州西湖的历史文化为背景，融合白蛇传、雷峰塔等传统故事元素的文字RPG游戏。
  
  - 显示游戏特色🎯
    
    - 纯文字交互，无需图形界面
    
    - 三大职业相互克制的战斗系统
    
    - 丰富的西湖文化地图探索
    
    - 社交化群聊游戏体验
    
    - 装备强化与技能学习
    
    - PVP与PVE双重战斗模式
  
  - 开始游戏🚀
    
    - 发送 !游戏模式 进入游戏
    
    - 发送 !职业介绍 了解职业信息
    
    - 发送 !新手指南 查看详细教程

- !职业介绍
  
  - 介绍三职业的背景和角色特点

- !新手指南
  
  🎯 指南目录：
  1️⃣ 角色创建
  2️⃣ 基础命令
  3️⃣ 战斗系统
  4️⃣ 装备系统
  5️⃣ 地图探索
  6️⃣ 社交功能
  7️⃣ 进阶技巧
  
  💡 发送 !新手指南 数字 查看具体章节
  例如：!新手指南 1 查看角色创建指南

#### 管理命令

- 创建区服：建服 + 服唯一别名

#### 系统反馈

- 文本反馈
  
  - 使用标准文本框
  - 文本框使用ASCII字符绘制边框
  - 合理使用emoji表情增强体验
  - 保持反馈信息简洁明了

- 消息模板
  
  - 移动消息：
    
    ```
    🏃‍♂️ @玩家 到达[地点]
    🗺️ 环境：[环境描述]
    💠 可选操作：
    1. 采集/打怪
    2. 查看环境 [图片]
    3. 进入副本（如果有）
    ```
  
  - 战斗消息：
    
    ```
    ⚔️ 战斗播报 ⚔️
    @玩家 → 目标
    伤害：[数值] | 剩余：[血量]
    ```
  
  - 击杀消息：
    
    ```
    系统：☠️ 击杀播报！
    @玩家 击杀 [怪物]
    ╭──────────╮
      经验 +100
      金币 +500
      获得：
      🎽 [装备名]
      💎 [材料]×2
    ╰──────────╯
    ```
  
  - PVP击杀消息：
    
    ```
    系统：⚔️ PVP击杀！
    @玩家A 击杀 @玩家B
    ╭──────────╮
      经验 +200
      江湖恶名 +50
      掠夺：
      🎽 [装备]
      💰 金币×1000
    ╰──────────╯
    ```
  
  - 采集消息：
    
    ```
    系统：📦 30秒内采集汇总 📦
    @玩家A：雨花石×2 | 天蚕丝×1
    @玩家B：雨花石×1
    @玩家C：获得：天外陨石×1 🌟稀有!
    ```

- Emoji规范
  
  - 🏃‍♂️ 移动相关
  - ⚔️ 战斗相关
  - 📦 物品相关
  - 💠 操作选项
  - 🌟 稀有/特殊
  - ☠️ 击杀相关
  - 💰 金币/财物
  - 🎽 装备相关

- 特殊反馈
  
  - 重要事件使用GIF动图
  - 地图查看生成实时位置图（3-5分钟缓存，实际根据体验和服务器能力调整），前期文字形式反馈
  - 状态查看可显示角色立绘
  - ASCII字符绘制简单界面

#### 防刷控制

    命令别名配置里面配置对应的命令每(X)秒内，只能操作(Y)次，如果超出了，提示“少侠功夫了得，手脚太快了，请停下来休息下喝杯龙井茶 🍵”。

## 第三章 游戏玩法

### 3.1 帮助系统

主要介绍游戏背景、游戏玩法等内容，由特定命令响应和反馈，具体见命令系统中的帮助命令内容

### 3.2 新手初始化

由创建职业角色后，初始化，初始化内容包括：

- 初始化背包
  
  - 初级装备武器

- 初始化金币

- 新手保护属性
  
  - 保护期三防都比较高

- 人物状态数据

- 给出下一步游戏玩法引导
  
  具体初始化数据由游戏配置化-新手初始化配置指定

### 3.3 地图行走

由命令系统里面的移动命令指定操作，计算目标地址和当前配置距离，决定到达目标时间长

有两种行路方式：

- 普通行路，由速度属性影响

- 御剑飞行，剑客特有的技能

引入“环境扫描”与“目标编号”机制，解决让玩家顺畅地“移动靠近”并“精确选择”一个目标（怪物或玩家）进行互动。。

- **环境扫描与列表化：**
  
  - 当玩家进入一个新地图或执行 `查看地图` / `查看环境` 命令时，系统不仅描述环境，还会将当前地图内的所有可交互目标（怪物、NPC、其他玩家）以列表形式展示，并自动为其分配一个临时编号。
  
  - **示例反馈：**
    
    ```
    🗺️ 你来到了[苏堤]，柳条轻拂，碧波荡漾。
    周围有：
    1. [怪物] 柳树精 (距离10米)
    2. [怪物] 柳树精 (距离30米)
    3. [NPC] 宋五嫂 (距离100米)
    4. [玩家] 侠客小A (距离2米)
    ```

- **通过编号精确交互：**
  
  - 玩家可以使用这个临时编号来执行精确操作，省去输入完整名称的麻烦。
  
  - `攻击 1`：攻击编号为1的柳树精。
  
  - `前往 3` 或 `询问 3`：移动到宋五嫂附近并触发对话。
  
  - `查看背包 @4`：查看玩家“侠客小A”的背包。

- **默认目标与状态锁定：**
  
  - 为了简化操作，可以设置默认目标规则。例如，单独发送 `攻击` 命令，会默认攻击列表中最近的怪物。
  
  - 一旦玩家攻击了某个目标（如 `攻击 1`），系统就将玩家置于与该目标的“交战”状态。后续的 `攻击` 或 `施放技能` 命令都会自动指向该目标，直到战斗结束或玩家使用 `撤退` 命令

### 3.4 钓鱼/采集

由指定地图固定位置配置可钓鱼/采集，具体配置见游戏配置化-地图采集和概率配置

可以用户主动发送钓鱼命令/采集命令，或者挂机采集/钓鱼，挂机概率会降低到原来的20%，最多挂机3小时。

如果钓鱼/采集到 稀有物品，需要发送区服系统消息，让玩家收到信息

### 3.5 抽奖系统

 到NPC处购买神奇宝箱，也可能做任务等其他玩法获得宝箱

如果背包有宝箱，使用物品命令，开启宝箱，根据初级~~稀有指定概率开出相关道具

具体道具配置由游戏配置化-宝箱道具配置

如果开启获得稀有以上的道具，需要发送区服系统消息，让玩家收到信息

### 3.6 角色属性加成

#### 属性加成组成部分

- 基础属性
  
  - 职业
  
  - 等级

- 装备属性

- 系统属性
  
  - 如新手保护期

#### 属性更新事件

- 被技能功击/使用

- 装备脱装

- 使用药物

- 人物升级

- 升级/浣灵装备

### 3.7 互动

#### 打怪

- 玩家在某个地图探索发现最近的怪，或者前往指定地图坐标的怪，使用攻击命令攻打最近的怪物（法师可以远距离攻击）；

- 连续发送命令进行攻击，也可以施放技能进行攻击；

- 怪物死亡掉落道具，如果一个人打，由一个人获得，如果多个人打，各自独立结算掉落，每个参与攻击的玩家都有独立的掉落概率；

#### 打架

- 条件1：非安全区（武林主城安全区）

- 条件2：同一个地图，距离不超过100米；

使用功击命令/技能 攻击玩家，收到被攻击的玩家也会收到提醒

#### 攻击锁定

- 为了简化操作，可以设置默认目标规则。例如，单独发送 `攻击` 命令，会默认攻击列表中最近或血量最低的怪物。

- 一旦玩家攻击了某个目标（如 `攻击 1`），系统就将玩家置于与该目标的“交战”状态。后续的 `攻击` 或 `施放技能` 命令都会自动指向该目标，直到战斗结束或玩家使用 `撤退` 命令。

#### 攻击效果计算

攻击效果由下面几部分组成，也是计算最复杂的部分：

- 距离判断
  
  - 攻击距离要求：
    
    - 剑客：近战攻击距离5米
    - 仙师：远程法术攻击距离15米
    - 圣僧：中距离佛法攻击距离8米
  
  - 超出攻击距离时：
    
    - 提示距离过远
    
    - 提示@前往 xxx 附近

- 攻击方式
  
  - 普通攻击
    
    - 冷却时间5秒
  
  - 技能伤害
    
    - 防御破坏x秒
    
    - 技能冷却时间10秒

- 发起方攻击力
  
  - 人物发起
  
  - 怪物发起：由人物攻击触发

- 受攻方防御力
  
  - 人物防属性
  
  - 怪物防属性

- 受攻方反伤
  
  - 只有人物才有

- 死亡触发
  
  - 死记掉落
    
    - 怪物死亡：见怪物掉落配置
    
    - 人物死亡：身上/背包随机掉，概率比较低
  
  - 人物死亡更新
    
    - 人物属性
    
    - 死亡原因：时间/地图和坐标/使用什么技能
  
  - 消息推送

人物死亡后还在地图，仍能使用查看命令，不能使用互动命令，复活后在武林主城，血气法气恢复一半；

#### 交易

- 与NPC交易
  
  在限定距离内，采用NPC命令进行交易，包括卖/买交易，如果条件不能满足，提示交易失败原因。
  
  玩家卖道具，与NPC交易完成后，材料销毁，获得对应价值虚拟货币；
  
  玩家买道具，与NPC交易完成后，生在新道具实例归属玩家，扣除玩家背包对应虚拟货币

- 与人物交易
  
  由买家发起命令请求，卖家通过命令确认交易，交易成功后，卖家获得对应货币，买家失去对应货币，道具所属用户，更新为买家；

### 3.8 角色升级

玩家攻击怪物，或者杀死玩家时，会获得经验，累积到一定经验时，会提升等级，提升完后，累积经验清零。

具体等级和所需经验等配置，见游戏配置化-升级属性配置

人物升级后，需要有系统提示

### 3.9 装备升级

#### 装备升品

| 品级    | 提升效果     | 材料需求                                                  | 成功概率 |
| ----- | -------- | ----------------------------------------------------- | ---- |
| 1-3品  | 0.5%属性提升 | 金币1000*(品数* 品数)、天蚕丝*(品数*品数)                           | 100% |
| 4-6品  | 1%属性提升   | 金币10000*(品数* 品数)、天蚕丝10*(品数* 品数)、雨花石*(品数*品数)           | 80%  |
| 7-9品  | 2%属性提升   | 银两5*(品数)、天蚕丝50*(品数* 品数)、雨花石10*(品数*品数)                 | 60%  |
| 10品以上 | 3%属性提升   | 银两10*(品数* 2)、天蚕丝100*(品数* 品数)、雨花石50*(品数* 品数)、天外陨石*(品数) | 50%  |

#### 玉佩浣灵

| 品级    | 浣灵范围       | 材料需求                         |
| ----- | ---------- | ---------------------------- |
| 1-3品  | 1.001-1.01 | 金币1000*(品数* 品数)、日月同辉*(品数*2)  |
| 4-6品  | 1.001-1.05 | 金币10000*(品数* 品数)、日月同辉*(品数*3) |
| 7-9品  | 1.001-1.2  | 银两5*(品数)、日月同辉*(品数*4)         |
| 10品以上 | 1.001-1.3  | 银两10*(品数* 2)、日月同辉*(品数*5)     |

具体装备升级所需材料由游戏配置化-装备升级材料配置决定

### 3.8 挂机

为了方便玩家离线后仍有收益，玩家可以挂机打怪、采集，具体由挂机相关命令执行进入挂机状态。

#### 挂机机制

- 挂机打怪：结算时间可配置（默认30分钟），收益为手动操作的20%
- 挂机采集：结算时间可配置（默认10分钟），收益为手动操作的20%
- 挂机时长：最多3小时，可通过系统配置调整
- 挂机风险：有概率遇到怪物攻击导致死亡，死亡掉装备概率可配置（建议设置较低概率）

#### 挂机配置参数

- 挂机打怪结算间隔：可配置（建议10-30分钟）
- 挂机采集结算间隔：可配置（建议5-15分钟）
- 挂机收益比例：可配置（建议10%-30%）
- 挂机死亡概率：可配置（建议1%-5%）
- 挂机死亡掉装备概率：可配置（建议0.1%-1%）

### 3.9 任务

- **任务类型：**
  
  - **主线任务：** 以“白蛇传”、“岳飞传”等西湖故事为蓝本，设计成系列任务。它将引导新手玩家熟悉世界、地图和核心玩法，并作为解锁高级功能（如特定副本）的前置条件。
  
  - **日常任务 (每日)：** 提供稳定的金币、经验和基础材料来源，鼓励玩家每日上线。可设计一个NPC（如武林主城的“说书人”）来发布。
    
    - `降妖任务`：击杀指定地图的10个怪物。
    
    - `采集任务`：采集10份茶叶或桑叶。
    
    - `西湖巡游`：前往三个指定的西湖景点。
    
    - `悬赏任务`：击杀一个小型Boss。
  
  - **周常任务 (每周)：** 针对高级玩家或团队，提供更有挑战性的目标和更丰厚的奖励（如高级材料、银两、稀有装备）。
    
    - `挑战法海`：通关“雷峰塔底”副本1次。
    
    - `降服蛟龙`：在团队中成功击杀“湖心亭”的蛟龙Boss。

- **任务接取与追踪：**
  
  - 玩家可以通过 `询问 [NPC名]` 来查看和 `接受 [任务名]` 来接取任务。
  
  - `查看状态` 命令的反馈中应加入“当前任务”一栏，方便玩家随时查看任务目标和进度。

### 3.10 排行榜

### 3.12 留存玩法

- 每日活动
  
  - 每日游玩西湖
  - 拜访苏小小
  - 在线时长奖励

- 挂机系统
  
  - 降低采集概率（人工10%）
  - 降低打怪收益（人工10%）
  - 固定时间范围内遇boss，不小心会被boss杀害（如果等级和防御低）

## 第四章 游戏配置化

为了方便调优游戏玩法和平衡性，以及优化游戏的访问性能，需要一个系统化的配置，来设定游戏的一些参数，这些配置是游戏启动后，全局生效，加载到内存系统的。

### 4.1 命令别名配置

列表配置，单个列表包含：

- 命令英文名

- 命令别名列表，支持多个

- 命令功能介绍

- 命令限流配置（可选）
  
  - xxx秒
  
  - xxx次

### 4.2 新手初始化配置

- 保护等级（保护期结束等级）

- 保护期属性增加
  
  - 三防增加100
  
  - 攻击增加10

- 初始化金币数量

- 初始化职业武器
  
  - 剑客：装备英文ID
  
  - 仙师：装备英文ID
  
  - 圣僧：装备英文ID

### 4.3 角色升级属性配置

- 1级角色基础属性：

| 属性   | 剑客  | 仙师  | 圣僧  |
| ---- | --- | --- | --- |
| 血量   | 20  | 10  | 15  |
| 法气   | 10  | 20  | 15  |
| 法攻   | 0   | 10  | 0   |
| 物攻   | 10  | 0   | 0   |
| 佛攻   | 0   | 0   | 10  |
| 物防   | 10  | 0   | 0   |
| 法防   | 0   | 10  | 0   |
| 佛防   | 0   | 0   | 10  |
| 升级经验 | 100 | 100 | 100 |

- 升级属性配置
  
  - 1~10级
    
    - 血量法气每级增加属性增加20%
    
    - 其他属性每级增加8%
  
  - 11~20级
    
    - 血量法气每级增加属性增加15%
    
    - 其他属性每级增加4%
  
  - 21~40级
    
    - 血量法气每级增加属性增加10%
    
    - 其他属性每级增加2%
  
  - 40~60级
    
    - 血量法气每级增加属性增加5%
    
    - 其他属性每级增加1.5%
  
  - 61级以上
    
    - 
    
    - 血量法气每级增加属性增加3%
    
    - 其他属性每级增加1%

- 升级所需经验配置：
  
  按照以下公式计算：下级经验 = (上级经验 * 1.035) + (当前等级 * 800)

- 等级称号配置：
  
  | 等级段位 | 级别范围   | 称号   |
  | ---- | ------ | ---- |
  | 第一阶段 | 1-9级   | 初出茅庐 |
  | 第二阶段 | 10-29级 | 江湖新手 |
  | 第三阶段 | 30-59级 | 江湖有名 |
  | 第四阶段 | 60-99级 | 武林高手 |
  | 第五阶段 | 100级以上 | 西湖神话 |

### 4.4 装备升级材料配置

- 装备升品：
  
  - 品级范围
  
  - 提升效果比例
  
  - 成功概率
  
  - 材料列表
    
    - 材料名
    
    - 数量：支持变量配置（如品数）
  
  示例：
  
  | 品级    | 提升效果     | 材料需求                                                  | 成功概率 |
  | ----- | -------- | ----------------------------------------------------- | ---- |
  | 1-3品  | 0.5%属性提升 | 金币1000*(品数* 品数)、天蚕丝*(品数*品数)                           | 100% |
  | 4-6品  | 1%属性提升   | 金币10000*(品数* 品数)、天蚕丝10*(品数* 品数)、雨花石*(品数*品数)           | 80%  |
  | 7-9品  | 2%属性提升   | 银两5*(品数)、天蚕丝50*(品数* 品数)、雨花石10*(品数*品数)                 | 60%  |
  | 10品以上 | 3%属性提升   | 银两10*(品数* 2)、天蚕丝100*(品数* 品数)、雨花石50*(品数* 品数)、天外陨石*(品数) | 50%  |

- 玉佩浣灵：
  
  - 品级范围
  
  - 浣灵范围
  
  - 所需材料
  
  示例：
  
  | 品级    | 浣灵范围       | 材料需求                         |
  | ----- | ---------- | ---------------------------- |
  | 1-3品  | 1.001-1.01 | 金币1000*(品数* 品数)、日月同辉*(品数*2)  |
  | 4-6品  | 1.001-1.05 | 金币10000*(品数* 品数)、日月同辉*(品数*3) |
  | 7-9品  | 1.001-1.2  | 银两5*(品数)、日月同辉*(品数*4)         |
  | 10品以上 | 1.001-1.3  | 银两10*(品数* 2)、日月同辉*(品数*5)     |

### 4.5 怪物掉落配置

列表配置，单个列表包含：

- 怪物ID

- 掉落物品列表
  
  - 物品ID
  
  - 概率

### 4.6 地图基础信息配置

列表配置，单个列表包含：

- 地图ID

- 地图名

- 地图介绍

- 地图图片地图（后续查看地图可以返回图片）

- 可进入地图ID列表

- 可去往地图ID列表

- 地图X坐标范围

- 地图Y坐标范围

### 4.7 地图刷怪配置

列表配置，单个列表包含：

- 地图ID
  
  - 怪物ID
  
  - 怪物数量
  
  - boss怪ID

### 4.8 地图采集配置

列表配置，单个列表包含

- 地图ID
  
  - 可采集X坐标范围
  
  - 可采集Y坐标范围
  
  - 采集类型
  
  - 采集相关的物品列表
    
    - 物品ID
    
    - 概率

### 4.9 NPC商店配置

列表配置，单个列表包含：

- NPC ID

- NPC 名

- 所在地图ID

- 地图坐标（x,y）

- NPC介绍

- 可卖物品列表：
  
  - 物品ID
  
  - 物品价格
  
  - 货币种类(银两/金币)
  
  - 税率

- 可买物品列表：
  
  - 物品ID
  
  - 物品价格
  
  - 货币种类(银两/金币)

### 4.10 宝箱道具配置

列表配置，单个列表包含：

- 宝箱道具ID

- 开启道具列表：
  
  - 道具ID
  
  - 道具数量
  
  - 概率

## 第六章 运营系统

### 6.1 变现系统

1. 充值内容
   
   - 银两只能充值流入市场
   - 神奇盒子：只能银两购买
   - 特殊道具：只能银两购买

2. 游戏机制
   
   - PVP掉落机制
   - 装备升级消耗
   - 技能书获取

### 6.2 社交系统（后续开发）

1. 基础社交
   
   - 结婚系统
   - 组队系统（二期开发）
     - 组队命令：组队邀请@玩家、同意组队、退出队伍
     - 队伍上限：3-5人
     - 队伍功能：共享经验、协作打BOSS、队伍聊天
     - 队长权限：踢出队员、解散队伍、分配掉落
   - 交易系统

2. 进阶社交
   
   - 帮派系统
   - 师徒系统
   - 好友系统