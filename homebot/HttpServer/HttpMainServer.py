from fastapi import FastAPI, HTTPException, Request
from typing import Dict, Any
import uvicorn
import json
from PushServer.GamePushServer import GamePushServer
from OutPut.outPut import op
import Config.ConfigServer as Cs

class HttpMainServer:
    def __init__(self, game_push_server: GamePushServer):
        """
        HTTP服务主入口
        统一管理所有HTTP接口

        Args:
            game_push_server: GamePushServer实例
        """
        self.app = FastAPI(title="HomeBot HTTP API")
        self.game_push_server = game_push_server
        configData = Cs.returnConfigData()
        self.port = configData['HttpServer']['port']
        # 注册所有路由
        self._register_routes()

    def _register_routes(self):
        """注册所有API路由"""
        
        # 注册游戏推送相关路由
        self._register_game_routes()
        
        # 注册系统相关路由
        self._register_system_routes()
        
        # TODO: 后续可以注册更多路由
        # self._register_xxx_routes()

    def _register_game_routes(self):
        """注册游戏相关路由"""
        
        @self.app.post("/api/v1/push/game")
        async def push_game_message(request: Request):
            """
            处理游戏推送消息
            
            请求体格式：
            {
                "msg_type": "text/image/card/...",  # 消息类型
                "room_id": "群ID",
                "content": "消息内容",
                "at_users": ["wxid1", "wxid2"],  # 需要@的用户ID列表
                "extra": {}  # 额外数据
            }
            """
            try:
                data = await request.json()
                
                # 验证必要字段
                if not data.get("room_id"):
                    raise HTTPException(status_code=400, detail="缺少必要字段: room_id")
                
                # 调用推送服务处理消息
                result = self.game_push_server.push_game_message(data)
                
                if result:
                    return {"success": True, "message": "消息推送成功"}
                else:
                    return {"success": False, "message": "消息推送失败"}
                    
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="无效的JSON格式")
            except Exception as e:
                op(f"[!] 处理推送请求异常: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))

    def _register_system_routes(self):
        """注册系统相关路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查接口"""
            return {"status": "ok"}
            
        @self.app.get("/version")
        async def version():
            """版本信息接口"""
            return {
                "version": "1.0.0",
                "name": "HomeBot HTTP API"
            }

    def run(self):
        """
        启动HTTP服务
        
        Args:
            host: 监听地址
            port: 监听端口
        """
        uvicorn.run(self.app, host="0.0.0.0", port=self.port)