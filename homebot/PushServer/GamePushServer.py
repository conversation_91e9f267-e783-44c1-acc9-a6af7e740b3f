from typing import Dict, Any, Optional
from OutPut.outPut import op
from wcferry import Wcf

class GamePushServer:
    def __init__(self, wcf: Wcf):
        """
        游戏推送服务器
        处理游戏系统的异步推送消息
        
        Args:
            wcf: WCF实例，用于发送微信消息
        """
        self.wcf = wcf

    def push_game_message(self, data: Dict[str, Any]) -> bool:
        """
        处理游戏推送消息
        
        Args:
            data: 推送消息数据，格式如下：
            {
                "msg_type": "text/image/card/...",  # 消息类型
                "room_id": "群ID",
                "content": "消息内容",
                "at_users": ["wxid1", "wxid2"],  # 需要@的用户ID列表
                "extra": {}  # 额外数据
            }
        
        Returns:
            bool: 处理结果
        """
        try:
            msg_type = data.get("msg_type", "text")
            room_id = data.get("room_id")
            content = data.get("content", "")
            at_users = data.get("at_users", [])
            extra = data.get("extra", {})

            if not room_id:
                op("[!] 推送消息缺少群ID")
                return False

            # 根据消息类型处理
            if msg_type == "text":
                return self._send_text_message(room_id, content, at_users)
            elif msg_type == "image":
                return self._send_image_message(room_id, content)
            elif msg_type == "card":
                return self._send_card_message(room_id, extra)
            else:
                op(f"[!] 不支持的消息类型: {msg_type}")
                return False

        except Exception as e:
            op(f"[!] 推送消息处理异常: {str(e)}")
            return False

    def _send_text_message(self, room_id: str, content: str, at_users: list) -> bool:
        """
        发送文本消息
        """
        try:
            if at_users:
                # 如果有@用户，需要在消息前面加上@标记
                at_text = " ".join([f"@{self.wcf.get_alias_in_chatroom(wxId, room_id)}" for wxId in at_users])
                content = f"{at_text} {content}"
                at_users_str = ",".join(at_users)
                self.wcf.send_text(msg=content, receiver=room_id, aters=at_users_str)
            else:
                self.wcf.send_text(msg=content, receiver=room_id)
            return True
        except Exception as e:
            op(f"[!] 发送文本消息失败: {str(e)}")
            return False

    def _send_image_message(self, room_id: str, image_path: str) -> bool:
        """
        发送图片消息
        """
        try:
            self.wcf.send_image(path=image_path, receiver=room_id)
            return True
        except Exception as e:
            op(f"[!] 发送图片消息失败: {str(e)}")
            return False

    def _send_card_message(self, room_id: str, card_data: Dict[str, Any]) -> bool:
        """
        发送卡片消息
        """
        try:
            name = card_data.get("name", "")
            account = card_data.get("account", "")
            title = card_data.get("title", "")
            digest = card_data.get("digest", "")
            url = card_data.get("url", "")
            thumb_url = card_data.get("thumb_url", "")

            self.wcf.send_rich_text(name=name, account=account, title=title, digest=digest, url=url, thumburl=thumb_url, receiver=room_id)
            return True
        except Exception as e:
            op(f"[!] 发送卡片消息失败: {str(e)}")
            return False 