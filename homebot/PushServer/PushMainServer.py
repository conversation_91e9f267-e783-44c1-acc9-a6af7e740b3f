from ApiServer.ApiMainServer import ApiMainServer
from DbServer.DbMainServer import DbMainServer
import FileCache.FileCacheServer as Fcs
import Config.ConfigServer as Cs
from OutPut.outPut import *
import schedule
from wcferry import Wcf
from .GamePushServer import GamePushServer
import datetime
import random

class PushMainServer:
    def __init__(self, wcf: Wcf):
        """
        推送主服务器
        初始化各个推送服务

        Args:
            wcf: WCF实例
        """
        self.stopFlag = True
        self.wcf = wcf
        self.Ams = ApiMainServer()
        self.Dms = DbMainServer()
        configData = Cs.returnConfigData()

        # 初始化游戏推送服务
        self.game_push_server = GamePushServer(wcf)

    def pushFishing(self, ):
        op(f'[*]: 检查是否在推送时间范围内...')
        current_time = datetime.datetime.now().time()
        start_time = datetime.time(9, 0)  # 早上9点
        end_time = datetime.time(23, 40)  # 晚上24点
        ##op(f'[*]: 时间相关参数 {start_time} -{current_time}- {end_time}')
        if start_time <= current_time <= end_time:
            self.wcf.send_text(msg='抛竿', receiver='47442567074@chatroom')
            time.sleep(random.randint(15, 30))
            self.wcf.send_text(msg='一键卖鱼', receiver='47442567074@chatroom')

    def pushRace(self, ):
        current_time = datetime.datetime.now().time()
        start_time = datetime.time(9, 0)  # 早上9点
        end_time = datetime.time(23, 45)  # 晚上24点
        if start_time <= current_time <= end_time:
            self.wcf.send_text(msg='钓鱼比赛', receiver='47442567074@chatroom')
            time.sleep(15)
            self.wcf.send_text(msg='钓鱼比赛', receiver='47442567074@chatroom')
            time.sleep(2)
            self.wcf.send_text(msg='参加比赛', receiver='47442567074@chatroom')

    def clearCacheFile(self, ):
        """
        定时缓存文件清空
        :return:
        """
        op(f'[*]: 定时缓存文件清空中... ...')
        Fcs.clearCacheFolder()
        op(f'[+]: 定时缓存文件清空成功！！！')

    def clearRoomTableData(self, ):
        """
        定时清除群聊消息库
        :return:
        """
        op(f'[*]: 群聊消息库清空中... ...')
        self.Dms.clearRoomMsgTableData()
        op(f'[+]: 群聊消息库清空成功！！！')

    def stopPushServer(self, ):
        """
        停止定时推送
        :param flag:
        :return:
        """
        self.stopFlag = False

    def run(self):
        """
        启动推送服务
        """
        try:
            # 设置定时任务
            schedule.every().day.at('03:00').do(self.clearCacheFile)
            schedule.every().weeks.monday.at("00:00").do(self.clearRoomTableData)
            schedule.every(2).minutes.do(self.pushFishing)
            schedule.every(29).minutes.do(self.pushRace)
            op(f'[+]: 已开启定时推送服务！！！')
            
            # 运行定时任务
            #while self.stopFlag:
            while True:
                schedule.run_pending()
                
        except Exception as e:
            op(f"[!] 启动推送服务失败: {str(e)}")