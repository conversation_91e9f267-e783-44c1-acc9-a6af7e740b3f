import DbServer.DbDomServer as Dds
import Config.ConfigServer as Cs
from OutPut.outPut import op


class DbRoomServer:
    def __init__(self):
        pass

    def addSuperRoom(self, roomId, roomName):
        """
        新增超服群聊
        :param roomName:
        :param roomId:
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        try:
            if not self.searchSuperRoom(roomId):
                cursor.execute('INSERT INTO superRoom VALUES (?, ?)', (roomId, roomName))
                conn.commit()
                Dds.closeDb(conn, cursor)
                return True
            return False
        except Exception as e:
            op(f'[-]: 新增超服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return False

    def delSuperRoom(self, roomId):
        """
        删除超服群聊
        :param roomId:
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        try:
            cursor.execute('DELETE FROM superRoom WHERE roomId=?', (roomId,))
            conn.commit()
            Dds.closeDb(conn, cursor)
            return True
        except Exception as e:
            op(f'[-]: 删除超服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return False

    def searchSuperRoom(self, roomId):
        """
        查询超服群聊
        :param roomId:
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        try:
            cursor.execute('SELECT roomName FROM superRoom WHERE roomId=?', (roomId,))
            result = cursor.fetchone()
            Dds.closeDb(conn, cursor)
            if result:
                return True
            return False
        except Exception as e:
            op(f'[-]: 查询超服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return False

    def showSuperRoom(self, ):
        """
        查看所有超服群聊
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        dataDict = dict()
        try:
            cursor.execute('SELECT roomId, roomName FROM superRoom')
            result = cursor.fetchall()
            Dds.closeDb(conn, cursor)
            if result:
                for res in result:
                    dataDict[res[0]] = res[1]
            return dataDict
        except Exception as e:
            op(f'[-]: 查看所有超服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return dataDict

    def addCloseRoom(self, roomId, roomName):
        """
        新增关服群聊
        :param roomName:
        :param roomId:
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        try:
            if not self.searchCloseRoom(roomId):
                cursor.execute('INSERT INTO closeRoom VALUES (?, ?)', (roomId, roomName))
                conn.commit()
                Dds.closeDb(conn, cursor)
                return True
            return False
        except Exception as e:
            op(f'[-]: 新增关服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return False

    def delCloseRoom(self, roomId):
        """
        删除关服群聊
        :param roomId:
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        try:
            cursor.execute('DELETE FROM closeRoom WHERE roomId=?', (roomId,))
            conn.commit()
            Dds.closeDb(conn, cursor)
            return True
        except Exception as e:
            op(f'[-]: 删除关服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return False

    def searchCloseRoom(self, roomId):
        """
        查询关服群聊
        :param roomId:
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        try:
            cursor.execute('SELECT roomName FROM closeRoom WHERE roomId=?', (roomId,))
            result = cursor.fetchone()
            Dds.closeDb(conn, cursor)
            if result:
                return result
            else:
                return False
        except Exception as e:
            op(f'[-]: 查询关服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return False

    def showCloseRoom(self, ):
        """
        查看所有关服群聊
        :return:
        """
        conn, cursor = Dds.openDb(Cs.returnRoomDbPath())
        dataDict = dict()
        try:
            cursor.execute('SELECT roomId, roomName FROM closeRoom')
            result = cursor.fetchall()
            Dds.closeDb(conn, cursor)
            if result:
                for res in result:
                    dataDict[res[0]] = res[1]
            return dataDict
        except Exception as e:
            op(f'[-]: 查看所有关服群聊出现错误, 错误信息: {e}')
            Dds.closeDb(conn, cursor)
            return dataDict


