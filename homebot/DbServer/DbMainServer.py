from DbServer.DbRoomMsgServer import DbRoomMsgServer
from DbServer.DbUserServer import Db<PERSON>serServer
from DbServer.DbRoomServer import DbRoomServer
from DbServer.DbInitServer import <PERSON>bInitServer
from DbServer.DbGhServer import DbGhServer
import Config.ConfigServer as Cs


class DbMainServer:
    def __init__(self):
        self.Dus = DbUserServer()
        self.Drs = DbRoomServer()
        self.Dis = DbInitServer()
        self.Dgs = DbGhServer()
        self.Dms = DbRoomMsgServer()
        self.configData = Cs.returnConfigData()

    def searchRoomMsgTable(self, tableName):
        """
        查询群聊数据表
        :param tableName:
        :return:
        """
        return self.Dms.searchRoomTable(tableName)

    def addRoomTable(self, tableName):
        """
        增加群聊数据表
        :param tableName:
        :return:
        """
        return self.Dms.addRoomTable(tableName)

    def addRoomContent(self, tableName, msgType, wxId, wxName, msgId, Content):
        """
        增加游戏对话内容
        :param tableName:
        :param msgType:
        :param wxId:
        :param wxName:
        :param msgId:
        :param Content:
        :return:
        """
        if not self.Dms.searchRoomTable(tableName):
            self.Dms.addRoomTable(tableName)
        return self.Dms.addRoomContent(tableName, msgType, wxId, wxName, msgId, Content)

    def showRoomContent(self, tableName):
        """
        查看群聊所有对话内容
        :param tableName:
        :return:
        """
        return self.Dms.showRoomContent(tableName)

    def showRoomCount(self, tableName):
        """
        查看当日群聊聊天总数和人数
        :param tableName:
        :return:
        """
        return self.Dms.showRoomCount(tableName)

    def searchRoomContent(self, tableName, msgId):
        """
        查找群聊某一对话内容, 根据MsdId查找
        :param tableName:
        :param msgId:
        :return:
        """
        return self.Dms.searchRoomContent(tableName, msgId)

    def roomMsgRanking(self, tableName):
        """
        当日群聊消息排行榜
        :param tableName:
        :return:
        """
        return self.Dms.roomMsgRanking(tableName)

    def roomMsgRowingList(self, tableName):
        """
        群聊划水榜（所有消息）
        :param tableName:
        :return:
        """
        return self.Dms.roomMsgRowingList(tableName)

    def roomMsgTypeRanking(self, tableName):
        """
        当日群聊消息类型排行榜
        :param tableName:
        :return:
        """
        return self.Dms.roomMsgTypeRanking(tableName)

    def clearRoomMsgTableData(self, ):
        """
        清除群聊消息所有表的数据
        :return:
        """
        return self.Dms.clearRoomMsgTableData()

    def addAdmin(self, wxId, roomId):
        """
        添加GM管理员
        :param wxId:
        :param roomId:
        :return:
        """
        return self.Dus.addAdmin(wxId, roomId)

    def delAdmin(self, wxId, roomId):
        """
        删除GM管理员
        :param wxId:
        :param roomId:
        :return:
        """
        return self.Dus.delAdmin(wxId, roomId)

    def searchAdmin(self, wxId, roomId):
        """
        搜索GM管理员
        :param wxId:
        :param roomId:
        :return:
        """
        return self.Dus.searchAdmin(wxId, roomId)


    def addUserOnline(self, wxId, roomId):
        """
        添加游戏模式用户
        :param wxId:
        :param roomId:
        :return:
        """
        return self.Dus.addUserOnline(wxId, roomId)

    def delUserOnline(self, wxId, roomId):
        """
        删除游戏模式用户
        :param wxId:
        :param roomId:
        :return:
        """
        return self.Dus.delUserOnline(wxId, roomId)

    def searchUserOnline(self, wxId, roomId):
        """
        搜索用户是否在线模式
        :param wxId:
        :param roomId:
        :return:
        """
        return self.Dus.searchUserOnline(wxId, roomId)

    def addSuperRoom(self, roomId, roomName):
        """
        添加超服群聊
        :param roomName:
        :param roomId:
        :return:
        """
        self.Drs.addSuperRoom(roomId, roomName)

    def delSuperRoom(self, roomId):
        """
        移出超服群聊
        :param roomId:
        :return:
        """
        return self.Drs.delSuperRoom(roomId=roomId)

    def showSuperRoom(self, ):
        """
        查看所有超服群聊
        :return:
        """
        return self.Drs.showSuperRoom()

    def searchSuperRoom(self, roomId):
        """
        搜索超服群聊
        :return:
        """
        return self.Drs.searchSuperRoom(roomId)

    def addCloseRoom(self, roomId, roomName):
        """
        添加关服群聊
        :param roomName:
        :param roomId:
        :return:
        """
        self.Drs.addCloseRoom(roomId, roomName)

    def delCloseRoom(self, roomId):
        """
        移出关服群聊
        :param roomId:
        :return:
        """
        return self.Drs.delCloseRoom(roomId=roomId)

    def showCloseRoom(self, ):
        """
        查看所有关服群聊
        :return:
        """
        return self.Drs.showCloseRoom()

    def searchCloseRoom(self, roomId):
        """
        搜索关服群聊
        :return:
        """
        return self.Drs.searchCloseRoom(roomId)

    def addBlackGh(self, ghId, ghName):
        """
        添加黑名单公众号
        :return:
        """
        return self.Dgs.addBlackGh(ghId=ghId, ghName=ghName)

    def delBlackGh(self, ghId):
        """
        删除黑名单公众号
        :param ghId:
        :return:
        """
        return self.Dgs.delBlackGh(ghId, )

    def showBlackGh(self, ):
        """
        查看黑名单群聊
        :return:
        """
        return self.Dgs.showBlackGh()


if __name__ == '__main__':
    Ds = DbMainServer()
    # print(Ds.clearSign())
    # print(Ds.searchPoint('sender', 'roomid'))
    print(Ds.showRoomContent('123456@chatroom'))