from BotServer.BotFunction.AdministratorFunction import AdministratorFunction
from BotServer.BotFunction.RoomMsgFunction import RoomMsgFunction
from BotServer.BotFunction.AdminFunction import AdminFunction
from BotServer.BotFunction.GameFunction import GameFunction
from BotServer.BotFunction.JudgeFuncion import *
from DbServer.DbMainServer import DbMainServer
import Config.ConfigServer as Cs
from threading import Thread
import re


class RoomMsgHandle:
    def __init__(self, wcf):
        """
        超级管理员功能 所有功能+管理员操作
        管理员功能 积分功能+娱乐功能
        超服群聊功能 积分功能免费
        关服群聊功能 所有功能无法使用 管理员以及超管除外
        普通群聊功能 所有功能正常使用
        :param wcf:
        """
        self.wcf = wcf
        self.Dms = DbMainServer()
        self.Gf = GameFunction(self.wcf)
        self.Af = AdminFunction(self.wcf)
        self.Asf = AdministratorFunction(self.wcf)
        self.Rmf = RoomMsgFunction(self.wcf)
        configData = Cs.returnConfigData()
        self.Administrators = configData['Administrators']
        self.joinRoomMsg = configData['customMsg']['joinRoomMsg']
        self.joinRoomCardData = configData['customMsg']['JoinRoomCard']
        self.appointJoinRoomMsgs = configData['customMsg']['appointJsonRoomMsgs']

    def mainHandle(self, msg):
        roomId = msg.roomid
        sender = msg.sender
        # print('消息类型-----------------------------', msg.type)
        # 超服群聊功能，相当于测试服，功能限制少
        if judgeSuperRoom(roomId):
            # 超管功能以及管理功能
            self.AdminFunction(msg)
            # 游戏功能
            Thread(target=self.Gf.mainHandle, args=(msg,)).start()
            # 入群欢迎
            Thread(target=self.JoinRoomWelcome, args=(msg,)).start()
            # 推送群聊和超服群聊才可以使用群聊总结功能&撤回消息检测功能&发言排行榜功能&定时推送总结
            Thread(target=self.Rmf.mainHandle, args=(msg,)).start()
        # 关服群聊功能，只有管理员可以试玩游戏
        elif judgeCloseRoom(roomId):
            # 超管功能以及管理功能
            self.AdminFunction(msg)
            # 超管和管理才能使用游戏功能
            if sender in self.Administrators or judgeAdmin(sender, roomId):
                Thread(target=self.Asf.mainHandle, args=(msg,)).start()
                Thread(target=self.Af.mainHandle, args=(msg,)).start()
                # 游戏功能
                Thread(target=self.Gf.mainHandle, args=(msg,)).start()
        # 普通游戏开服的群聊功能
        else:
            # 超管功能以及管理功能
            self.AdminFunction(msg)
            # 入群欢迎
            Thread(target=self.JoinRoomWelcome, args=(msg,)).start()
            # 游戏功能
            Thread(target=self.Gf.mainHandle, args=(msg,)).start()

    def RoomMsgFunction(self, msg):
        """
        群聊消息服务
        :param msg:
        :return:
        """


    def JoinRoomWelcome(self, msg):
        """
        进群欢迎
        :param msg:
        :return:
        """
        try:
            ret = 1
            appoint = 1
            content = msg.content.strip()
            wx_names = None
            if '二维码' in content:
                wx_names = re.search(r'"(?P<wx_names>.*?)"通过扫描', content)
            elif '邀请' in content:
                wx_names = re.search(r'邀请"(?P<wx_names>.*?)"加入了', content)
            if wx_names:
                wx_names = wx_names.group('wx_names')
                if '、' in wx_names:
                    wx_names = wx_names.split('、')
                else:
                    wx_names = [wx_names]
            for wx_name in wx_names:
                for roomIds, data in self.joinRoomCardData.items():
                    roomIdLists = roomIds.split(',')
                    for roomId in roomIdLists:
                        if msg.roomid == roomId:
                            name = data.get('name')
                            account = data.get('account')
                            title = data.get('title').format(wx_name)
                            digest = data.get('digest')
                            url = data.get('url')
                            thumbUrl = data.get('thumbUrl')
                            self.wcf.send_rich_text(name, account, title, digest, url, thumbUrl, roomId)
                            ret = 0

                if ret:
                    for id, msgs in self.appointJoinRoomMsgs.items():
                        if id == msg.roomid:
                            self.wcf.send_text(msg=f'@{wx_name} ' + msgs.replace("\\n", "\n"), receiver=msg.roomid)
                            appoint = 0
                if appoint and ret:
                    joinRoomMsg = f'@{wx_name} ' + self.joinRoomMsg.replace("\\n", "\n")
                    self.wcf.send_text(msg=joinRoomMsg, receiver=msg.roomid)
        except Exception as e:
            pass

    def AdminFunction(self, msg):
        """
        超级管理员以及管理员功能
        :param msg:
        :return:
        """
        # 超级管理员功能
        if msg.sender in self.Administrators:
            self.Asf.mainHandle(msg)
        # 管理员功能 超管也可以调用
        if judgeAdmin(msg.sender, msg.roomid) or msg.sender in self.Administrators:
            self.Af.mainHandle(msg)


if __name__ == '__main__':
    configData = Cs.returnConfigData()
    appointJoinRoomMsgs = configData['customMsg']['appointJsonRoomMsgs']
    for ids, msgs in appointJoinRoomMsgs.items():
        print(ids)
