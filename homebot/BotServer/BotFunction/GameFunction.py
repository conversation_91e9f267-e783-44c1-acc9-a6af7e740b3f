from BotServer.BotFunction.InterfaceFunction import *
from ApiServer.ApiMainServer import ApiMainServer
from BotServer.BotFunction.JudgeFuncion import *
import Config.ConfigServer as Cs
from DbServer.DbMainServer import DbMainServer

class GameFunction:
    def __init__(self, wcf):
        """
        娱乐功能
        :param wcf:
        """
        self.wcf = wcf
        self.Ams = ApiMainServer()
        self.Dms = DbMainServer()
        configData = Cs.returnConfigData()

        self.helpKeyWords = configData['functionKeyWord']['helpMenu']

        self.musicWords = configData['functionKeyWord']['musicWords']
        # 自定义回复关键词字典
        self.customKeyWords = configData['customKeyWord']


    def mainHandle(self, message):
        content = message.content.strip()
        roomId = message.roomid
        senderWxId = message.sender
        senderHead = getUserPicUrl(self.wcf, senderWxId)
        senderName = self.wcf.get_alias_in_chatroom(senderWxId, roomId)
        msgType = message.type
        atUsers, noAtMsg = getAtData(self.wcf, message)

        if msgType == 1:
            # 点歌
            if judgeSplitAllEqualWord(content, self.musicWords):
                musicName = content.split(' ')[-1]
                musicHexData = self.Ams.getMusic(musicName)
                if not musicHexData:
                    self.wcf.send_text(f'@{senderName} 点歌接口出现错误, 请稍后再试 ~~~', receiver=roomId, aters=senderWxId)
                    return
                data = self.wcf.query_sql('MSG0.db', "SELECT * FROM MSG where type = 49  limit 1")
                self.wcf.query_sql('MSG0.db',
                                   f"UPDATE MSG SET  CompressContent = x'{musicHexData}', BytesExtra=x'',type=49,SubType=3,IsSender=0,TalkerId=2 WHERE MsgSvrID={data[0]['MsgSvrID']}")
                self.wcf.forward_msg(data[0]["MsgSvrID"], roomId)
            #使用游戏命令，进行游戏玩法
            elif content.startswith('!'):
                #用户
                if not judgeUserOnline(senderWxId, roomId) and judgeInWord(content, '游戏模式'):
                    self.onlineHandle(senderWxId, senderName, roomId)
                if judgeUserOnline(senderWxId, roomId) and judgeInWord(content, '退出游戏模式'):
                    self.offlineHandle(senderWxId, senderName, roomId)
                #调用游戏
                self.Ams.game(message, senderName, senderHead, atUsers)
            #进入游戏模式，无须使用!命令
            elif judgeUserOnline(senderWxId, roomId):
                if judgeUserOnline(senderWxId, roomId) and judgeInWord(content, '退出游戏模式'):
                    self.offlineHandle(senderWxId, senderName, roomId)
                #调用游戏
                self.Ams.game(message, senderName, senderHead, atUsers)
            # 自定义回复
            elif judgeEqualListWord(content, self.customKeyWords.keys()):
                for keyWord in self.customKeyWords.keys():
                    if judgeEqualWord(content, keyWord):
                        replyMsgLists = self.customKeyWords.get(keyWord)
                        for replyMsg in replyMsgLists:
                            self.wcf.send_text(replyMsg, receiver=roomId)
            # 帮助菜单
            elif judgeEqualListWord(content, self.helpKeyWords):
                helpMsg = '[爱心]=== 游戏帮助 ===[爱心]\n'
                helpMsg += '【一、游戏介绍】\n1.1、游戏背景\n1.2、游戏功能\n1.3、游戏角色\n1.4、游戏装备\n1.5、游戏资产\n1.6、游戏怪物\n1.7、游戏攻击\n\n'
                helpMsg += '【二、游戏命令】\n2.1、操作规则\n2.2、常规操作\n2.3、路径操作\n2.4、攻击操作\n2.5、交易操作\n2.6、整理操作\n2.7、挂机操作\n\n'
                helpMsg += '[爱心]=== 游戏帮助 ===[爱心]\n'
                self.wcf.send_text(f'@{senderName}\n{helpMsg}', receiver=roomId, aters=senderWxId)
        elif msgType == 49: # 视频号解析
            return

    def onlineHandle(self, senderWxId, senderName,roomId):
        self.Dms.addUserOnline(senderWxId, roomId)
        op(f'[*]: 用户进入游戏模式, 用户ID: {senderWxId}')
        self.wcf.send_text(f'@{senderName} 你进入游戏模式, 游戏操作无须再增加“!”操作符', receiver=roomId,aters=senderWxId)
    def offlineHandle(self, senderWxId, senderName,roomId):
        self.Dms.delUserOnline(senderWxId, roomId)
        op(f'[*]: 用户退出游戏模式, 用户ID: {senderWxId}')
        self.wcf.send_text(f'@{senderName} 你退出游戏模式，如须玩游戏，命令前面需增加操作符“!”，或者重新进入游戏模式', receiver=roomId, aters=senderWxId)