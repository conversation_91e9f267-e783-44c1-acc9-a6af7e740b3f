from BotServer.BotFunction.InterfaceFunction import *
from BotServer.BotFunction.JudgeFuncion import *
from DbServer.DbMainServer import DbMainServer
import Config.ConfigServer as Cs


class AdminFunction:
    def __init__(self, wcf):
        """
        管理功能
        :param wcf:
        """
        self.wcf = wcf
        self.Dms = DbMainServer()
        configData = Cs.returnConfigData()
        self.addCloseRoomKeyWords = configData['adminFunctionWord']['addCloseRoomWord']
        self.delCloseRoomKeyWords = configData['adminFunctionWord']['delCloseRoomWord']
        self.addSuperRoomKeyWords = configData['adminFunctionWord']['addSuperRoomWord']
        self.delSuperRoomKeyWords = configData['adminFunctionWord']['delSuperRoomWord']
        self.addBlackGhKeyWords = configData['adminFunctionWord']['addBlackGhWord']
        self.delBlackGhKeyWords = configData['adminFunctionWord']['delBlackGhWord']
        self.delUserKeyWords = configData['adminFunctionWord']['delUserWord']

    def mainHandle(self, message):
        content = message.content.strip()
        sender = message.sender
        roomId = message.roomid
        msgType = message.type
        roomName = getIdName(self.wcf, roomId)
        senderName = self.wcf.get_alias_in_chatroom(sender, roomId)
        atUserLists, noAtMsg = getAtData(self.wcf, message)
        if msgType == 1:
            # 添加超服群聊
            if judgeEqualListWord(content, self.addSuperRoomKeyWords):
                if self.Dms.addSuperRoom(roomId, roomName):
                    self.wcf.send_text(f'@{senderName} 添加超服群聊成功 !!!', receiver=roomId, aters=sender)
                    return
                self.wcf.send_text(f'@{senderName} 此群已在超服中', receiver=roomId, aters=sender)
            # 移出超服群聊
            elif judgeEqualListWord(content, self.delSuperRoomKeyWords):
                if self.Dms.delSuperRoom(roomId):
                    self.wcf.send_text(f'@{senderName} 移出超服群聊成功 !!!', receiver=roomId, aters=sender)
                    return
                self.wcf.send_text(f'@{senderName} 此群已移出超服 !!!', receiver=roomId, aters=sender)
            # 添加关服群聊
            elif judgeEqualListWord(content, self.addCloseRoomKeyWords):
                if self.Dms.addCloseRoom(roomId, roomName):
                    self.wcf.send_text(f'@{senderName} 此群已关闭游戏服务 !!!', receiver=roomId, aters=sender)
                    return
                self.wcf.send_text(f'@{senderName} 此群已在关服中 !!!', receiver=roomId, aters=sender)
            # 开启游戏服务群聊
            elif judgeEqualListWord(content, self.delCloseRoomKeyWords):
                if self.Dms.delCloseRoom(roomId):
                    self.wcf.send_text(f'@{senderName} 开启游戏服务 !!!', receiver=roomId, aters=sender)
                    return
                self.wcf.send_text(f'@{senderName} 此群已开启游戏服务 !!!', receiver=roomId, aters=sender)
            # 踢人
            elif judgeEqualListWord(noAtMsg, self.delUserKeyWords):
                for atUser in atUserLists:
                    atWxId = atUser.wxId
                    atName = atUser.name
                    if self.wcf.del_chatroom_members(roomId, atWxId):
                        self.wcf.send_text(
                            f'@{atName} 基于你的表现, 给你移出游戏群的奖励',
                            receiver=roomId)
                    else:
                        self.wcf.send_text(
                            f'@{senderName} [{atName}] 移出群聊失败',
                            receiver=roomId, aters=sender)
            # # 添加黑名单公众号 阉割
            # elif judgeEqualListWord(content, self.addBlackGhKeyWords):
            #     pass
            # # 移出黑名单公众号
            # elif judgeEqualListWord(content, self.delBlackGhKeyWords):
            #     pass

