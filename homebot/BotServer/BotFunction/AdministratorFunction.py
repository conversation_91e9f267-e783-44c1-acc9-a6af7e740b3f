from BotServer.BotFunction.InterfaceFunction import *
from BotServer.BotFunction.JudgeFuncion import *
from DbServer.DbMainServer import DbMainServer
import Config.ConfigServer as Cs


class AdministratorFunction:
    def __init__(self, wcf):
        """
        超管功能
        :param wcf:
        """
        self.wcf = wcf
        self.Dms = DbMainServer()
        configData = Cs.returnConfigData()
        self.addAdminKeyWords = configData['adminFunctionWord']['addAdminWord']
        self.delAdminKeyWords = configData['adminFunctionWord']['delAdminWord']

    def mainHandle(self, message):
        sender = message.sender
        roomId = message.roomid
        senderName = self.wcf.get_alias_in_chatroom(sender, roomId)
        msgType = message.type
        atUserLists, noAtMsg = getAtData(self.wcf, message)
        if msgType == 1:
            # 添加管理员
            if judgeEqualListWord(noAtMsg, self.addAdminKeyWords):
                if atUserLists:
                    for atUser in atUserLists:
                        atWxId = atUser.wxId
                        atName = atUser.name
                        if self.Dms.addAdmin(atWxId, roomId):
                            self.wcf.send_text(
                                f'@{senderName}\n管理员 [{atName}] 添加成功', receiver=roomId, aters=sender)
                        else:
                            self.wcf.send_text(
                                f'@{senderName}\n群成员 [{atName}] 已是管理员', receiver=roomId, aters=sender)
            # 删除管理员
            elif judgeEqualListWord(noAtMsg, self.delAdminKeyWords):
                if atUserLists:
                    for atUser in atUserLists:
                        atWxId = atUser.wxId
                        atName = atUser.name
                        if self.Dms.delAdmin(atWxId, roomId):
                            self.wcf.send_text(
                                f'@{senderName}\n管理员 [{atName}] 删除成功', receiver=roomId, aters=sender)
                        else:
                            self.wcf.send_text(
                                f'@{senderName}\n群成员 [{atName}] 已不是管理员', receiver=roomId, aters=sender)

