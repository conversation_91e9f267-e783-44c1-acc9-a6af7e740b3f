# HomeBot架构设计文档

## 一、系统核心组件

```mermaid
graph TB
    WX[微信客户端] --> WCF[WCF接口]
    WCF --> MS[MainServer]
    
    subgraph BotServer[机器人服务器]
        MS --> RMH[RoomMsgHandle<br/>群消息处理]
        MS --> FMH[FriendMsgHandle<br/>好友消息处理]
        
        RMH --> GF[GameFunction<br/>游戏功能]
        RMH --> AF[AdminFunction<br/>管理功能]
        RMH --> ASF[AdministratorFunction<br/>超管功能]
    end
    
    subgraph ApiServer[API服务器]
        GF --> GAS[GameApiServer<br/>游戏API]
    end
    
    subgraph HttpServer[HTTP服务器]
        HMS[HttpMainServer] --> GR[游戏路由]
        HMS --> SR[系统路由]
        GR --> GPS[GamePushServer]
    end
    
    subgraph SupportServer[支持服务]
        PS[PushServer<br/>推送服务]
        DBS[DbServer<br/>数据库服务]
        FC[FileCache<br/>文件缓存]
    end
    
    MS --> HMS
    GAS --> DBS
    GAS --> FC
    MS --> PS
```

## 二、消息流转路径

### 1. 基础消息流转

```mermaid
sequenceDiagram
    participant WX as 微信客户端
    participant WCF as WCF接口
    participant MS as MainServer
    participant RMH as RoomMsgHandle
    participant GF as GameFunction
    participant GAS as GameApiServer
    
    WX->>WCF: 发送消息
    WCF->>MS: 消息接收
    MS->>RMH: 群消息分发
    RMH->>GF: 游戏消息处理
    GF->>GAS: 游戏命令处理
    GAS-->>GF: 返回结果
    GF-->>WX: 响应消息
```

### 2. HTTP服务流程

```mermaid
sequenceDiagram
    participant GS as 游戏系统
    participant HMS as HttpMainServer
    participant GPS as GamePushServer
    participant WX as 微信客户端
    
    GS->>HMS: POST /api/v1/push/game
    HMS->>GPS: push_game_message()
    GPS->>WX: 发送消息(文本/图片/卡片)
    WX-->>GPS: 发送结果
    GPS-->>HMS: 处理结果
    HMS-->>GS: HTTP响应
```

## 三、功能模块说明

### 1. 核心组件功能

- **MainServer (主服务器)**
  - 系统初始化
  - 消息接收分发
  - 服务协调管理
  - HTTP服务管理

- **BotServer (机器人服务器)**
  - MsgHandleServer
    - RoomMsgHandle: 群消息处理
    - FriendMsgHandle: 好友消息处理
  - BotFunction
    - GameFunction: 游戏功能
    - AdminFunction: 管理功能
    - AdministratorFunction: 超管功能

- **HttpServer (HTTP服务器)**
  - HttpMainServer: HTTP服务主入口
  - 统一管理所有HTTP接口
  - 模块化的路由注册
  - 系统级接口支持

- **ApiServer (API服务器)**
  - GameApiServer: 游戏API处理

### 2. 支持服务功能

- **PushServer (推送服务器)**
  - GamePushServer: 游戏消息推送
  - 定时任务管理
  - 系统消息推送

- **DbServer (数据库服务器)**
  - 数据持久化
  - 数据查询服务

- **FileCache (文件缓存)**
  - 临时文件存储
  - 缓存管理

## 四、消息类型说明

### 1. 基础消息类型
- 普通文本消息
- 系统消息(入群等)

### 2. 功能消息类型
- 游戏命令消息 (!开头)
- 管理命令消息
- 超管命令消息

### 3. HTTP接口消息类型
- 游戏推送消息
  - 文本消息
  - 图片消息
  - 卡片消息
- 系统消息
  - 健康检查
  - 版本信息

## 五、架构特点

1. **模块化设计**
   - 清晰的功能划分
   - 独立的服务模块
   - 易于扩展和维护

2. **多层消息处理**
   - 消息分类处理
   - 权限分级控制
   - 灵活的处理机制

3. **服务解耦**
   - 核心服务独立
   - HTTP服务集中管理
   - 支持服务分离
   - 便于横向扩展

4. **安全性设计**
   - 权限严格控制
   - 分级功能访问
   - 管理功能隔离

5. **可扩展性**
   - 统一的HTTP服务入口
   - 模块化的路由注册
   - 标准的接口规范
   - 预留扩展接口 