HttpServer:
  port: 9090

## 超级管理员配置
Administrators:
  - 'fengin'

## 系统配置
systemConfig:
  # 版权信息
  systemCopyright: '西湖美产品小组'
  # 自动接收转账
  acceptMoneyLock: 1
  # 自动同意好友开关
  acceptFriendLock: 1
  # 好友消息转发给管理员开关
  msgForwardAdmin: 1

## 进群关键词配置
roomKeyWord:
  AI全书:
    - '21221660076@chatroom'
  进游戏群:
    - '***********@chatroom'
    - '***********@chatroom'
  运营交流群:
    - '***********@chatroom'
    - '***********@chatroom'

## 自定义关键词回复
customKeyWord:
  # 设置关键词
  '关键词1':
    - '回复内容1'
    - '回复内容2'
  '关键词2':
    - '回复内容1'
    - '回复内容2'

## 自定义各种消息 \n换行
customMsg:
  # 加好友后回复
  # acceptFriendMsg: '客官你好，我是西湖哇塞NPC， 私聊我即可进行智能对话哦[爱心]~~~ ~~~'
  acceptFriendMsg: '你好朋友，我是AI全书助理，欢迎加入我们一起学习，进学习群回复我：AI全书'
  # 默认进群欢迎词配置 换行用\n 支持微信表情 默认使用文本 卡片自己配置
  #joinRoomMsg: '欢迎新进服的侠客，新的征途即将开始[爱心]~~~ ~~~'
  joinRoomMsg: '欢迎新同学加入，学习介绍可以看群公告！'
  # 指定群聊进群欢迎词 触发了指定群聊 则不会触发默认进群欢迎
  appointJsonRoomMsgs:
    # 群聊ID 多个Room加 , 即可
    '50303957048@chatroom': '【腾讯文档】WingBy小密圈知识星球说明 进群必看！！！！！\nhttps://docs.qq.com/doc/DS2tmaEJlUmFRY0Vi'
    '49507388138@chatroom': '恭喜您成为尊贵的NGC会员，愿耀眼的bot标带您开拓进取，勇创辉煌，成就卓越。祝您在未来的道路丰衣足食，有人陪你沧海桑田，永远积极向上，热泪盈眶，车到山前必有路，有路必有NGCBot'
    '***********@chatroom': '测试123123'
  # 进群欢迎词 卡片配置
  JoinRoomCard:
    # 不同的Room需要不同配置 多个room加 , 即可
    '***********@chatroom,***********@chatroom,***********@chatroom,***********@chatroom':
      # 左下角显示的名字 一般是公众号名称
      name: '西湖哇塞'
      # 填公众号ID 可以显示对应的头像
      account: 'gh_5fc2f516b008'
      # 卡片消息标题 最多两行
      title: '欢迎 ❤{}❤ 一起去闯荡西湖'
      # 摘要 最多三行
      digest: '西湖哇塞游戏，遇见一个不一样的世界！'
      # 点击后跳转的URL
      url: 'http://mp.weixin.qq.com/s?__biz=MzkyODMxODUwNQ==&amp;mid=**********&amp;idx=1&amp;sn=d0f7675929a314941ad3845769bafdcb&amp;chksm=c21832d0f56fbbc6c4451105df94e39283228e9f6bb69eae00888c4869f5b91e3703bd302e92&amp;mpshare=1&amp;scene=1&amp;srcid=0716KB1OiULdPNzLUXmMVVlV&amp;sharer_shareinfo=dd05c6b9c14d98dc6b745cbf673dabec&amp;sharer_shareinfo_first=24b3af78c3e815a698c81c03a49a0244#rd'
      # 缩略图的链接 到控制台输出拿
      thumbUrl: 'https://mmbiz.qpic.cn/mmbiz_jpg/QABjQccnicQpAnrqNicQd9iaTIQg3TZia6nr3OaAJQS8bOMrdtFct30axbiaO3OTnULGjHpE0RQD0NmT76FWa5daiaZA/300?wxtype=jpeg&amp;wxfrom=401'
#  'roomId':
#    # 左下角显示的名字 一般是公众号名称
#    name: 'NGC660安全实验室'
#    # 填公众号ID 可以显示对应的头像
#    account: ''
#    # 卡片消息标题 最多两行
#    title: ''
#    # 摘要 最多三行
#    digest: ''
#    # 点击后跳转的URL
#    url: ''
#    # 缩略图的链接 到控制台输出拿
#    thumbUrl: ''


## 管理功能关键词配置
adminFunctionWord:
  # 新增区服管理员关键词
  addAdminWord:
    - '添加管理员'
    - '添加管理'
    - '新增管理员'
    - '新增GM'
  # 删除管理员关键词
  delAdminWord:
    - '删除管理员'
    - '删除管理'
    - '移除管理员'
    - '删除GM'
  #关服群聊（closeArea）：停服，暂停功能
  #超服群聊(whiteArea)：特殊游戏群，相当于测试群，权限和功能限制少
  #普通群聊(normalArea)：正常开服群
  # 新增关服群聊关键词
  addCloseRoomWord:
    - '关服'
    - '停服'
  # 开服单群聊关键词
  delCloseRoomWord:
    - '开服'
    - '启动'
  # 查看关服群聊关键词
  showCloseRoomWord:
    - '停服列表'
    - '下线区服'

  # 查看超服群聊关键词
  showSuperRoomWord:
    - '超级服列表'
    - '超级区服'
  # 新增超服群聊关键词
  addSuperRoomWord:
    - '加超服'
    - '加超'
  # 移出超服群聊关键词
  delSuperRoomWord:
    - '移出超服'
    - '去超'

  # 添加黑名单公众号
  addBlackGhWord:
    - '黑号'
    - '拉黑'
  # 移除黑名单公众号
  delBlackGhWord:
    - '移除拉黑'
    - '移除'
    - '移出'
  # 查看黑名单公众号
  showBlackGhWord:
    - '查看拉黑公众号'
    - '拉黑公众号'
    - '拉黑公号'
  # 踢人关键词
  delUserWord:
    - '滚蛋'
    - '滚'
    - '踢出'
    - '踢'
    - '滚出去'
    - '移出'
  # 给好友发消息关键词
  sendMsgWord:
    - '发'
    - '发送'


## 其它功能关键词配置
functionKeyWord:
  # 帮助菜单
  helpMenu:
    - '帮助'
    - 'help'
    - 'Help'
    - 'HELP'
    - '帮助菜单'
    - '菜单'
  # 点歌关键词
  musicWords:
    - '点歌'
    - '点'
  # 群聊消息总结关键词
  summarizeMsgWord:
    - '总结群聊'
    - '群聊总结'
  # 群聊发言榜
  speechListWord:
    - '发言榜'
  # 划水榜
  rowingListWord:
    - '潜水榜'
