from ApiServer.GameServer.GameApiServer import GameApiServer
import ApiServer.pluginServer as Ps


class ApiMainServer:
    def __init__(self):
        """
        将所有插件服务全部注册在__init__.py文件中
        此文件做所有插件总体调用
        """
        # Ai对象实例化
        self.Ga = GameApiServer()

    def getMusic(self, musicName):
        """
        点歌API
        :param musicName:
        :return:
        """
        return Ps.Ha.getMusic(musicName)

    def game(self, msg, senderName, senderHead, atUsers):
        """
        游戏接口
        :param msg 用户发送消息
        :param senderName 发送者名字
        :param atWxIds 被at的微信ID
        :param atNames 被at的微信名
        :return:
        """
        return self.Ga.game(msg, senderName, senderHead, atUsers)


if __name__ == '__main__':
    Ams = ApiMainServer()
    print(Ams.getMusic('风吹过的春天，你不在这里'))
