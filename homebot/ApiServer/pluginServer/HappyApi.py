from meme_generator import get_meme, get_meme_keys
import FileCache.FileCacheServer as Fcs
import Config.ConfigServer as Cs
from OutPut.outPut import op
import lz4.block as lb
import requests
import asyncio
import random
import time
import os
import re


class HappyApi:
    def __init__(self):
        """
        不要直接调用此类
        娱乐功能Api文件
        """
        # 读取配置文件
        configData = Cs.returnConfigData()
        # 读取系统版权设置
        self.systemCopyright = configData['systemConfig']['systemCopyright']

    def getMusic(self, musicName):
        op(f'[*]: 正在调用点歌接口... ...')
        musicApi = "https://www.hhlqilongzhu.cn/api/dg_kugouSQ.php?msg={}&n=1&type=json"
        try:
            jsonData = requests.get(musicApi.format(musicName), verify=True, timeout=30).json()
            songName = jsonData.get('title')
            singerName = jsonData.get('singer')
            songPic = jsonData.get('cover').replace('http://', 'https://')
            dataUrl = jsonData.get('link')
            playUrl = jsonData.get('music_url')
            xml_message = f"""
            <?xml version="1.0"?>
                <msg>
                    <appmsg appid="wx485a97c844086dc9" sdkver="0">
                        <title>{songName}</title>
                        <des>{singerName}</des>
                        <action>view</action>
                        <type>3</type>
                        <showtype>0</showtype>
                        <content />
                        <url>{dataUrl}</url>
                        <dataurl>{playUrl}</dataurl>
                        <lowurl>{playUrl}</lowurl>
                        <lowdataurl>{playUrl}</lowdataurl>
                        <recorditem />
                        <thumburl />
                        <messageaction />
                        <laninfo />
                        <extinfo />
                        <sourceusername />
                        <sourcedisplayname />
                        <commenturl />
                        <appattach>
                            <totallen>0</totallen>
                            <attachid />
                            <emoticonmd5></emoticonmd5>
                            <fileext />
                            <aeskey></aeskey>
                        </appattach>
                        <webviewshared>
                            <publisherId />
                            <publisherReqId>0</publisherReqId>
                        </webviewshared>
                        <weappinfo>
                            <pagepath />
                            <username />
                            <appid />
                            <appservicetype>0</appservicetype>
                        </weappinfo>
                        <websearch />
                        <songalbumurl>{songPic}</songalbumurl>
                    </appmsg>
                    <fromusername>wxid_cbgpauriedg629</fromusername>
                    <scene>0</scene>
                    <appinfo>
                        <version>29</version>
                        <appname>摇一摇</appname>
                    </appinfo>
                    <commenturl />
                </msg>
            """
            # 将文本编码成字节 wxid_cbgpauriedg629
            text_bytes = xml_message.encode('utf-8')
            # 使用 lz4 压缩
            compressed_data = lb.compress(text_bytes,store_size=False)
            # 将压缩后的数据转为十六进制字符串，以便存储到数据库
            compressed_data_hex = compressed_data.hex()
            return compressed_data_hex
        except Exception as e:
            op(f'[-]: 点歌API出现错误, 错误信息: {e}')
            return None


    def getEmoticon(self, avatarPathList, memeKey=None):
        """
        表情包Api接口
        :param memeKey: 消息内容
        :param avatarPathList: 头像列表
        :return:
        """
        op(f'[*]: 正在调用表情包Api接口... ...')
        if not avatarPathList:
            op(f'[-]: 表情包Api接口出现错误, 错误信息: avatarPathList不能为空')
            return
        if not avatarPathList:
            raise 'avatarPathList None'
        if not memeKey:
            memeKey = random.choices(get_meme_keys())[0]

        savePath = Fcs.returnPicCacheFolder() + '/' + str(int(time.time() * 1000)) + '.gif'
        try:
            async def makeEmo():
                meme = get_meme(memeKey)
                result = await meme(images=avatarPathList, texts=[], args={"circle": False})
                with open(savePath, "wb") as f:
                    f.write(result.getvalue())

            loop = asyncio.new_event_loop()
            loop.run_until_complete(makeEmo())
            # 图片大小判断 如果大于1mb 就以图片形式发送
            file_size_bytes = os.path.getsize(savePath)
            size_limit_bytes = 1024 * 1024
            sizeBool = file_size_bytes <= size_limit_bytes
            return savePath, sizeBool
        except Exception as e:
            op(f'[-]: 表情包Api接口出现错误, 错误信息: {e}')
            return None, None


if __name__ == '__main__':
    Ha = HappyApi()
    # print(Ha.getDog())
    # print(Ha.getKfc())
    # Ha.getEmoticon('C:/Users/<USER>/Desktop/NGCBot V2.2/avatar.jpg')
    # print(Ha.getShortPlay('霸道总裁爱上我'))
    # print(Ha.getPic())
    # print(Ha.getVideoAnalysis(
    #     '3.84 复制打开抖音，看看【SQ的小日常的作品】师傅：门可以让我踹吗 # 情侣 # 搞笑 # 反转... https://v.douyin.com/iydr37xU/ bAg:/ <EMAIL> 01/06'))
    # print(Ha.getWechatVideo('14258814955767007275', '14776806611926650114_15_140_59_32_1735528000805808'))
    # print(Ha.getTaLuo())
    # print(Ha.getFish())
    print(Ha.getMusic('晴天'))
