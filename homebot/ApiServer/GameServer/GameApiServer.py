import requests
import json
from typing import Dict, Any, Optional
from OutPut.outPut import op


class GameApiServer:
    def __init__(self):
        """
        游戏API服务器
        初始化基础配置
        """
        # 游戏服务器配置
        self.game_server_url = "http://localhost:8080"  # 实际部署时修改为真实地址
        self.game_api_endpoint = "/api/v1/game/command"  # 游戏命令统一入口
        self.timeout = 10  # 请求超时时间（秒）

    def game(self, msg, senderName: str, senderHead: str, atUsers: Dict[str, str]) -> Optional[bool]:
        """
        处理游戏命令并调用游戏系统API
        
        Args:
            msg: 消息对象
            senderName: 发送者名称
            senderHead: 发送者头像URL
            atUsers: 被@用户字典 {wxid: nickname}
        
        Returns:
            Optional[bool]: 处理结果
        """
        try:
            # 构建请求数据
            request_data = {
                "command": msg.content.strip(),  # 游戏命令
                "sender": {
                    "wxid": msg.sender,
                    "name": senderName,
                    "avatar": senderHead
                },
                "room": {
                    "roomid": msg.roomid,
                },
                "at_users": atUsers,
                "msg_type": msg.type
            }

            # 调用游戏系统API
            response = self._call_game_api(request_data)
            
            if response and response.get("success"):
                # 处理成功响应
                result = response.get("data", {})
                # 这里可以根据需要处理返回的游戏数据
                # TODO: 处理游戏返回数据
                return True
            else:
                # 处理失败响应
                error_msg = response.get("message", "未知错误") if response else "请求失败"
                op(f"[!] 游戏命令处理失败: {error_msg}")
                return False

        except Exception as e:
            op(f"[!] 游戏命令处理异常: {str(e)}")
            return False

    def _call_game_api(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用游戏系统API
        
        Args:
            request_data: 请求数据
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        try:
            headers = {
                "Content-Type": "application/json",
                # TODO: 添加认证信息
                # "Authorization": f"Bearer {self._get_token()}"
            }

            response = requests.post(
                url=f"{self.game_server_url}{self.game_api_endpoint}",
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )

            # 检查响应状态
            response.raise_for_status()
            
            return response.json()

        except requests.exceptions.RequestException as e:
            op(f"[!] 游戏API请求异常: {str(e)}")
            return {
                "success": False,
                "message": f"API请求失败: {str(e)}"
            }
        except json.JSONDecodeError as e:
            op(f"[!] 游戏API响应解析异常: {str(e)}")
            return {
                "success": False,
                "message": "API响应格式错误"
            }
        except Exception as e:
            op(f"[!] 游戏API调用未知异常: {str(e)}")
            return {
                "success": False,
                "message": f"未知错误: {str(e)}"
            }

    # TODO: 实现token获取方法
    def _get_token(self) -> str:
        """
        获取API认证token
        
        Returns:
            str: 认证token
        """
        return ""  # 待实现
